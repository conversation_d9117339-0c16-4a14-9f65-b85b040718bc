<?php

namespace app\service;

use app\BaseService;
use app\ErrorCode;
use app\model\SalesReturn as SalesReturnModel;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\WeChat as WeChatService;
use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;

class SalesReturn extends BaseService
{

    public static function list($params)
    {
        $err_ret       = ['list' => [], 'total' => 0];
        $p_admin_id    = request()->header('vinehoo_uid');
        $preparedsInfo = (new self)->httpGet(env('ITEM.ERP_PREPARED_URL') . '/prepared/v3/prepareds/detail', ['prepared_uid' => $p_admin_id]);
        if (!isset($preparedsInfo['error_code']) || $preparedsInfo['error_code'] != 0 || empty($preparedsInfo['data'])) return $err_ret;
        if ($preparedsInfo['data']['status'] == 0) return $err_ret;

        //查询条件
        $where = function ($query) use ($params, $preparedsInfo) {
            //联系人
            if (isset($params['contacts_name']) && strlen($params['contacts_name']) > 0) {
                $query->where('contacts_name', 'like', '%' . $params['contacts_name'] . '%');
            }

            //制单人
            if (isset($params['operator_zdr_name']) && strlen($params['operator_zdr_name']) > 0) {
                $query->where('operator_zdr_name', '=', $params['operator_zdr_name']);
            }

            //联系人电话
            if (isset($params['contacts_phone']) && strlen($params['contacts_phone']) > 0) {
                $query->where('contacts_phone', '=', $params['contacts_phone']);
            }

            //单据编号
            if (isset($params['sub_order_no']) && strlen($params['sub_order_no']) > 0) {
                $query->where('sub_order_no', 'like', '%' . $params['sub_order_no'] . '%');
            }

            if (isset($params['bill_no']) && strlen($params['bill_no']) > 0) {
                $query->where('bill_no', '=', $params['bill_no']);
            }

            if (isset($params['corp']) && strlen($params['corp']) > 0) {
                $query->where('corp', '=', $params['corp']);
            }

            if (isset($params['clerk_code']) && strlen($params['clerk_code']) > 0) {
                $query->where('clerk_code', '=', $params['clerk_code']);
            }

            if (isset($params['clerk']) && strlen($params['clerk']) > 0) {
                $query->where('clerk', 'LIKE', "%{$params['clerk']}%");
            }

            //客户名称
            if (isset($params['customer']) && strlen($params['customer']) > 0) {
                $query->where('customer', '=', $params['customer']);
            }

            //结算客户名称
            if (isset($params['settle_customer']) && strlen($params['settle_customer']) > 0) {
                $query->where('settle_customer', '=', $params['settle_customer']);
            }

            //仓库
            if (isset($params['warehouse_code']) && strlen($params['warehouse_code']) > 0) {
                $query->where('warehouse_code', '=', $params['warehouse_code']);
            }

            //审批状态
            if (isset($params['dingtalk_status']) && strlen($params['dingtalk_status']) > 0) {
                $query->where('dingtalk_status', '=', $params['dingtalk_status']);
            }
            if (isset($params['push_t_status']) && is_numeric($params['push_t_status'])) {
                $query->where('push_t_status', '=', $params['push_t_status']);
            }
            if (isset($params['start_time']) && strlen($params['start_time']) > 0) {
                $query->where('bill_date', '>=', strtotime($params['start_time']));
            }

            if (isset($params['end_time']) && strlen($params['end_time']) > 0) {
                $query->where('bill_date', '<=', strtotime($params['end_time']));
            }

            if ($preparedsInfo['data']['is_all_inquire_prepared'] == 0) {
                $prepared_inquire_uid = array_column($preparedsInfo['data']['prepared_inquire_items'], 'uid');
                $query->where('operator_id', 'in', $prepared_inquire_uid);
            }

            if (isset($params['push_wms_status']) && is_numeric($params['push_wms_status'])) {
                $query->where('push_wms_status', '=', $params['push_wms_status']);
            }
            //            if ($preparedsInfo['data']['is_all_inquire_salesman'] == 0) {
            //                $clerk_ids = array_column($preparedsInfo['data']['salesman_inquire_items'], 'Code');
            //                $query->where('clerk_code', 'in', $clerk_ids);
            //            }

            //            $query->where('create_time','<>',0);

            // 推送erp时间
            if (isset($params['push_erp_stime']) && strlen($params['push_erp_stime']) > 0) {
                $query->where('push_erp_time', '>=', strtotime($params['push_erp_stime']));
            }
            if (isset($params['push_erp_etime']) && strlen($params['push_erp_etime']) > 0) {
                $query->where('push_erp_time', '<=', strtotime($params['push_erp_etime']));
            }
        };

        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;

        //获取当前用户权限
        //        $authwhere     = [];
        //        $salesreturn   = new SalesReturn();
        //        $preparedsInfo = $salesreturn->httpGet(env('ITEM.ERP_PREPARED_URL') . '/prepared/v3/prepareds/detail', ['prepared_uid' => $params['operator_id']]);
        //        if (!isset($preparedsInfo['error_code']) || $preparedsInfo['error_code'] != 0 || empty($preparedsInfo['data'])) return ['list' => [], 'total' => 0];
        //        if ($preparedsInfo['data']['status'] == 0) return ['list' => [], 'total' => 0];
        //        if ($preparedsInfo['data']['is_all_inquire_prepared'] == 0 && !empty($preparedsInfo['data']['prepared_inquire_items'])) {
        //            $prepared_inquire_uid = array_column($preparedsInfo['data']['prepared_inquire_items'], 'uid');
        //            $authwhere[]          = ['operator_id', 'in', $prepared_inquire_uid];
        //        }

        //        if ($preparedsInfo['data']['is_all_inquire_salesman'] == 0 && !empty($preparedsInfo['data']['salesman_inquire_items'])) {
        //            $salesman_inquire_name = array_column($preparedsInfo['data']['salesman_inquire_items'], 'Name');
        //            $authwhere[]  =['clerk','in',$salesman_inquire_name];
        //        }


        //        $result = SalesReturnModel::where($where)->where($authwhere)->limit($pagestrat, $limit)->order('id', 'desc')->select()->toArray();
        $result = SalesReturnModel::where($where)->limit($pagestrat, $limit)->order('id', 'desc')->select()->toArray();
        //        echo SalesReturnModel::getLastsql();
        $result = array_map(function ($v) {
            $v['detail_json'] = array_values((array)$v['detail_json']);
            return $v;
        }, $result);

        //        $total = SalesReturnModel::where($where)->where($authwhere)->count();
        $total = SalesReturnModel::where($where)->count();
        $companys = Db::name('collecting_company')->column('name', 'corp');
        foreach ($result as &$val) {
            $refund_money = SalesReturnModel::where('sub_order_no', $val['sub_order_no'])->where('dingtalk_status', '<>', 3)->sum('return_amount');
            $val['residue_refund_money'] = strval($val['order_amount'] - $refund_money);

            $val['approval_time'] = !empty($val['approval_time']) ? date('Y-m-d H:i:s', $val['approval_time']) : '';
            $val['push_erp_time'] = !empty($val['push_erp_time']) ? date('Y-m-d H:i:s', $val['push_erp_time']) : '';
            $val['corp_text'] = $companys[$val['corp']] ?? '';
        }
        return ['list' => $result, 'total' => $total];
    }

    /**
     * 创建销售退货 仅限科技公司
     * @param $params
     * @return void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function create($params)
    {
        Db::startTrans();
        try {
            $push_wms = true;
            $push_erp = false;
            //创建退款
            unset($params['id']);
            unset($params['create_time']);
            unset($params['update_time']);
            //线下 001
            if ($params['sale_bill_type'] == 2) {
                $field         = "corp";
                $where         = ['sub_order_no' => $params['sub_order_no']];
                $offline_order = Db::name("offline_order")->field($field)->where($where)->find();
                if ($offline_order['corp'] == '002') throw new ValidateException("线下单据，云酒公司。请在T+处理");
                if (!in_array($offline_order['corp'], ['001','515','003','031','032'])) throw new ValidateException("线下单据，仅处理科技公司,兔子星球,木兰朵。其余请在通过其它渠道处理");
            }

            //三方 001
            if ($params['sale_bill_type'] == 3) {
                $offline_order = self::getTripartiteOrderCorp([$params['sub_order_no']]);
                $offline_order = $offline_order[0];
                if ($offline_order['corp'] == '002') throw new ValidateException("三方单据，云酒公司。请在T+处理");
                if (!in_array($offline_order['corp'], ['001','515','003','031','032'])) throw new ValidateException("三方单据，仅处理科技公司,兔子星球,木兰朵。其余请在通过其它渠道处理");
            }

            //线上
            if ($params['sale_bill_type'] == 1) {
                if (strpos($params['sub_order_no'], 'VHA') !== false) {
                    $offline_order = array(array('sub_order_no' => $params['sub_order_no'], 'corp' => '001'));
                } else {
                    $offline_order = self::getonlineOrderCorp([$params['sub_order_no']]);
                }
                $offline_order = $offline_order[0];
                if ($offline_order['corp'] == '002') throw new ValidateException("线上单据，云酒公司。请在T+处理");
                if (!in_array($offline_order['corp'], ['001','515','003','031','032'])) throw new ValidateException("线上单据，仅处理科技公司,兔子星球,木兰朵。其余请在通过其它渠道处理");
            }

            //简码字符串
            $params['short_codes'] = implode(',', array_column($params['detail_json'], 'short_code'));
            $params['bill_date']   = strtotime($params['bill_date']);
            $params['bill_no']     = isset($params['bill_no']) && $params['bill_no'] != '' ? $params['bill_no'] : self::getBillNo();

            $SalesReturn = SalesReturnModel::where('sub_order_no', $params['sub_order_no'])->where('dingtalk_status', '<>', 3)->select()->toArray();
            //退货金额 start
            $return_amount = 0;
            foreach ($params['detail_json'] as $v) {
                if (bccomp(round($v['nums'], 2), $v['nums'], 3) !== 0) throw new ValidateException("数量的数字限制精度最多为小数点2位");
                //获取所有已退款的订单 产品简码数据
                $short_code_arr_detail = self::getSalesReturnPrice($SalesReturn);
                $short_code_arr        = array_column($short_code_arr_detail, 'short_code');

                //有重合
                if (in_array($v['short_code'], $short_code_arr)) {
                    //sale_bill_type
                    if ($params['sale_bill_type'] == 1) { //酒云线上
                        $online = self::getSalesDocumentsonline([$params['sub_order_no']]);

                        $item = $online[0]['items_info']; //详情
                        //验证数量
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            if ($ks == $v['key']) {
                                $short_code_nums = $vs['nums'];
                                break;
                            }
                        }
                        if ($short_code_nums == -1) continue;
                    } else if ($params['sale_bill_type'] == 2) { //线下
                        $field           = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
                        $where           = ['sub_order_no' => $params['sub_order_no']];
                        $offline_order   = Db::name("offline_order")->field($field)->where($where)->find();
                        $item            = explode(",", $offline_order['items_info']);
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            $goods = explode('*', $vs);

                            if ($ks == $v['key']) {
                                $short_code_nums = $goods[2];
                                break;
                            }
                        }
                        if ($short_code_nums == -1) continue;
                    } else if ($params['sale_bill_type'] == 3) { //三方
                        $field           = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
                        $where           = ['sub_order_no' => $params['sub_order_no']];
                        $offline_order   = Db::name("tripartite_order")->field($field)->where($where)->find();
                        $item            = explode(",", $offline_order['items_info']);
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            $goods = explode('*', $vs);
                            if ($ks == $v['key']) {
                                $short_code_nums = $goods[1] * $offline_order['order_qty'];
                                break;
                            }
                        }
                    }

                    $short_code_arr_detail_nums = 0; //已退货数据数量
                    foreach ($short_code_arr_detail as $k => $value) {
                        if ($value['key'] == $v['key']) $short_code_arr_detail_nums += $value['nums'];
                    }
                    //                    echo $v['short_code']."+++".$short_code_nums."+++".$short_code_arr_detail_nums."+++".$v['nums'].PHP_EOL;
                    //                    var_dump(($short_code_nums-$short_code_arr_detail_nums),$v['nums']);
                    if (($short_code_nums - $short_code_arr_detail_nums) < $v['nums']) { //该退款产品退货数量不能超过订单产品数量
                        throw new ValidateException($v['short_code'] . ":该退款产品退货数量不能超过订单产品数量");
                    }
                } else {
                    if ($params['sale_bill_type'] == 1) { //酒云线上
                        $online = self::getSalesDocumentsonline([$params['sub_order_no']]);
                        $item   = $online[0]['items_info']; //详情
                        //验证数量
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            if ($ks == $v['key']) {
                                $short_code_nums = $vs['nums'];
                                break;
                            }
                        }
                        if ($short_code_nums == -1) continue;
                    } else if ($params['sale_bill_type'] == 2) { //线下
                        $field           = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
                        $where           = ['sub_order_no' => $params['sub_order_no']];
                        $offline_order   = Db::name("offline_order")->field($field)->where($where)->find();
                        $item            = explode(",", $offline_order['items_info']);
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            $goods = explode('*', $vs);

                            if ($ks == $v['key']) {

                                $short_code_nums = $goods[2];
                                break;
                            }
                        }
                    } elseif ($params['sale_bill_type'] == 3) { //三方
                        $field           = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
                        $where           = ['sub_order_no' => $params['sub_order_no']];
                        $offline_order   = Db::name("tripartite_order")->field($field)->where($where)->find();
                        $item            = explode(",", $offline_order['items_info']);
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            $goods = explode('*', $vs);
                            /*if($goods[0] == $v['short_code']){
                                $short_code_nums = $goods[1]; break;
                            }*/
                            if ($ks == $v['key']) {
                                $short_code_nums = $goods[1] * $offline_order['order_qty'];
                                break;
                            }
                        }
                    }
                    //                    echo $v['short_code']."+++".$short_code_nums."+++".$v['nums'].PHP_EOL;
                    if (($short_code_nums - $v['nums']) < 0) { //该退款产品退货数量不能超过订单产品数量
                        throw new ValidateException($v['short_code'] . ":该退款产品退货数量不能超过订单产品数量");
                    }
                }

                //                $return_amount += ($v['tax_unit_price']*$v['nums']);
                $return_amount += (float)$v['tax_total_price'];
            }
            $params['return_amount'] = $return_amount;
            //退货金额 end

            //验证金额
            $checkmoney = self::checkreturnmoney($params['sub_order_no'], $params['sale_bill_type'], $return_amount);
            if (!$checkmoney) throw new ValidateException('退款金额不能超过订单总金额，请检查所有退款订单金额');

            //附件处理
            if (isset($params['media_url']) && !empty($params['media_url'])) {
                $offline            = new Offline();
                $params['media_id'] = $offline->uploadWeiXinTemporary(['file' => $params['media_url']]);
                //                $subData['media_id'] = $params['media_ids'];
            }

            // 1.查询萌芽是否有单据
            $wms_order_status = 0; // 0=不处理，1=为空，2=已存在，3=已确认一致，4=已确认不一致
            $done_wms_orders  = [];
            if (!empty($params['return_courier_no']) && !empty($params['is_push_wms']) && intval($params['is_push_wms']) === 1) {
                $wms_order = \Curl::queryWmsSalesOrder(['wy_no' => $params['return_courier_no']]);

                $wms_order_list = $wms_order ?? [];
                if (!empty($wms_order_list)) {
                    $p_detail_json = [];
                    $p_gooods_num = [];
                    foreach ($params['detail_json'] as $pdj) {
                        $p_detail_json[] = "{$pdj['short_code']}_{$pdj['nums']}";
                        $p_gooods_num[] = [
                            'fictitious' => $params['warehouse_code'],
                            'fictitious_num' => $pdj['nums'],
                            'short_code' => $pdj['short_code'],
                            'bar_code' => $pdj['bar_code'],
                        ];
                    }
                    foreach ($wms_order_list as $wms_order_item) {
                        $p_return_courier_no = explode(',', $params['return_courier_no']);
                        if (in_array($wms_order_item['status_value'], [5,6]) && $wms_order_item['order_no'] == $params['sub_order_no'] && empty(array_diff($p_return_courier_no, $wms_order_item['wy_no']))) {
                            //判断是否匹配
                            $wms_detail_json = [];
                            foreach ($wms_order_item['goods_num'] as $pdj) {
                                if ($pdj['fictitious'] == $params['warehouse_code']) {
                                    $wms_detail_json[] = "{$pdj['short_code']}_{$pdj['fictitious_num']}";
                                }
                            }

                            if (empty(array_diff($p_detail_json, $wms_detail_json))) {
                                //匹配
                                //萌芽单据由 已确认 改为 已完成  并且修改原销售单号, 中台单据直接审核完成.
                                $params['dingtalk_status'] = 2;
                                $push_wms = false;
                                $push_erp = true;
                                \Curl::syncReturnsConfirm([
                                    'order_no' => $params['sub_order_no'],
                                    'wy_no'    => $params['return_courier_no'],
                                    'goods_num'    => $p_gooods_num,
                                ]);
                                break;
                            } else {
                                //不匹配
                                throw new Exception('请到萌芽核实到货');
                            }
                        }
                    }
                }
            }

            // 验证是否存在同仓库、同退货快递单号、同原单的审核中退货单
            $existingReturn = Db::name('sales_return')
                ->where([
                    ['warehouse_code', '=', $params['warehouse_code']],
                    ['return_courier_no', '=', $params['return_courier_no']],
                    ['sub_order_no', '=', $params['sub_order_no']],
                    ['dingtalk_status', '=', 1], // 审核中状态
                ])
                ->find();

            if ($existingReturn) {
                throw new \Exception("已存在相同仓库、快递单号、原单的审核中退货单，请到【{$existingReturn['bill_no']}】上修改");
            }

            $SalesReturnModel = SalesReturnModel::create($params);
            $id               = $SalesReturnModel->getLastInsID();
            $params['id']     = $id;

            if (!empty($params['is_push_wms']) && intval($params['is_push_wms']) === 1) {
                $params['push_wms_status'] = $params['is_push_wms'];
                // 推送萌牙WMS
                if($push_wms){
                    self::pushWms($params);
                }

                if($push_erp){
                    $erp_res = (new self())->SalesReturnPushErp([
                        'operator_id'   => $user_info['id'] ?? 0,
                        'operator_name' => $user_info['realname'] ?? '系统',
                        'bill_no'       => $params['bill_no'],
                    ]);
                    if ($erp_res !== true) {
                        Log::record('推送ERP失败1:', $erp_res);
                    }
                }
            } else {
                //提交审批
                $params['id']           = $SalesReturnModel->getLastInsID();
                $SalesReturn            = new SalesReturn();
                $params['admin_id']     = $params['operator_id'];
                $params['items_info']   = $params['detail_json'];
                $is_gift_arr            = array_column($params['detail_json'], 'is_gift');
                $params['is_have_gift'] = in_array(1, $is_gift_arr) ? "是赠品" : "无赠品"; //有无赠品
                $SalesReturn->ordinarySaleOrderCreateVerify($params);
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            throw new ValidateException($exception->getMessage());
        }
    }

    public static function update($params)
    {
        Db::startTrans();
        try {

            $push_erp = false;
            $push_wms = true;
            $id = $params['id'];
            if (isset($params['create_time'])) {
                unset($params['create_time']);
            }
            $params['update_time'] = time();
            //线下 002
            if ($params['sale_bill_type'] == 2) {
                $field         = "corp";
                $where         = ['sub_order_no' => $params['sub_order_no']];
                $offline_order = Db::name("offline_order")->field($field)->where($where)->find();
                if ($offline_order['corp'] == '002') throw new ValidateException("线下单据，云酒公司。请在T+处理");
            }
            //修改退款
            $salesReturnModel = SalesReturnModel::where('id', $params['id'])->find();
            if (!$salesReturnModel) throw new ValidateException("数据不存在");
            //简码字符串
            $params['short_codes'] = implode(',', array_column($params['detail_json'], 'short_code'));
            $params['bill_date']   = strtotime($params['bill_date']);

            $params['approval_process'] = null;
            $params['approver']         = '';
            $params['dingtalk_status']  = 1;
            //验证数量start
            $SalesReturn = SalesReturnModel::where('sub_order_no', $params['sub_order_no'])->where('dingtalk_status', '<>', 3)->select()->toArray();
            //退货金额 start
            $return_amount = 0;
            foreach ($params['detail_json'] as $v) {
                if (bccomp(round($v['nums'], 2), $v['nums'], 3) !== 0) throw new ValidateException("数量的数字限制精度最多为小数点2位");
                //获取所有已退款的订单 产品简码数据
                $short_code_arr_detail = self::getSalesReturnPrice($SalesReturn);
                $short_code_arr        = array_column($short_code_arr_detail, 'short_code');

                //有重合
                if (in_array($v['short_code'], $short_code_arr)) {
                    //sale_bill_type
                    if ($params['sale_bill_type'] == 1) { //酒云线上
                        $online = self::getSalesDocumentsonline([$params['sub_order_no']]);

                        $item = $online[0]['items_info']; //详情
                        //验证数量
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            if ($ks == $v['key']) {
                                $short_code_nums = $vs['nums'];
                                break;
                            }
                        }
                        if ($short_code_nums == -1) continue;
                    } else if ($params['sale_bill_type'] == 2) { //线下
                        $field           = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
                        $where           = ['sub_order_no' => $params['sub_order_no']];
                        $offline_order   = Db::name("offline_order")->field($field)->where($where)->find();
                        $item            = explode(",", $offline_order['items_info']);
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            $goods = explode('*', $vs);

                            if ($ks == $v['key']) {
                                $short_code_nums = $goods[2];
                                break;
                            }
                        }
                        if ($short_code_nums == -1) continue;
                    } elseif ($params['sale_bill_type'] == 3) { //三方
                        $field           = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
                        $where           = ['sub_order_no' => $params['sub_order_no']];
                        $offline_order   = Db::name("tripartite_order")->field($field)->where($where)->find();
                        $item            = explode(",", $offline_order['items_info']);
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            $goods = explode('*', $vs);
                            /*if($goods[0] == $v['short_code']){
                                $short_code_nums = $goods[1]; break;
                            }*/

                            if ($ks == $v['key']) {
                                $short_code_nums = $goods[1];
                                break;
                            }
                        }
                    }

                    $short_code_arr_detail_nums = 0; //已退货数据数量
                    foreach ($short_code_arr_detail as $k => $value) {
                        if ($value['key'] == $v['key']) $short_code_arr_detail_nums += $value['nums'];
                    }
                    //                    echo $v['short_code']."+++".$short_code_nums."+++".$short_code_arr_detail_nums."+++".$v['nums'].PHP_EOL;
                    //                    var_dump(($short_code_nums-$short_code_arr_detail_nums),$v['nums']);
                    if (($short_code_nums - $short_code_arr_detail_nums) < $v['nums']) { //该退款产品退货数量不能超过订单产品数量
                        throw new ValidateException($v['short_code'] . ":该退款产品退货数量不能超过订单产品数量");
                    }
                } else {
                    if ($params['sale_bill_type'] == 1) { //酒云线上
                        $online = self::getSalesDocumentsonline([$params['sub_order_no']]);

                        $item = $online[0]['items_info']; //详情
                        //验证数量
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            if ($ks == $v['key']) {
                                $short_code_nums = $vs['nums'];
                                break;
                            }
                        }
                        if ($short_code_nums == -1) continue;
                    } else if ($params['sale_bill_type'] == 2) { //线下
                        $field           = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
                        $where           = ['sub_order_no' => $params['sub_order_no']];
                        $offline_order   = Db::name("offline_order")->field($field)->where($where)->find();
                        $item            = explode(",", $offline_order['items_info']);
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            $goods = explode('*', $vs);

                            if ($ks == $v['key']) {

                                $short_code_nums = $goods[2];
                                break;
                            }
                        }
                    } elseif ($params['sale_bill_type'] == 3) { //三方
                        $field           = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
                        $where           = ['sub_order_no' => $params['sub_order_no']];
                        $offline_order   = Db::name("tripartite_order")->field($field)->where($where)->find();
                        $item            = explode(",", $offline_order['items_info']);
                        $short_code_nums = -1;
                        foreach ($item as $ks => $vs) {
                            $goods = explode('*', $vs);
                            /*if($goods[0] == $v['short_code']){
                                $short_code_nums = $goods[1]; break;
                            }*/
                            if ($ks == $v['key']) {
                                $short_code_nums = $goods[1];
                                break;
                            }
                        }
                    }
                    //                    echo $v['short_code']."+++".$short_code_nums."+++".$v['nums'].PHP_EOL;
                    if (($short_code_nums - $v['nums']) < 0) { //该退款产品退货数量不能超过订单产品数量
                        throw new ValidateException($v['short_code'] . ":该退款产品退货数量不能超过订单产品数量");
                    }
                }

                $return_amount += (float)$v['tax_total_price'];
            }
            $params['return_amount'] = $return_amount;
            //验证数量 end

            //验证金额
            $checkmoney = self::checkreturnmoney($params['sub_order_no'], $params['sale_bill_type'], $return_amount);
            if (!$checkmoney) throw new ValidateException('退款金额不能超过订单总金额，请检查所有退款订单金额.');

            //附件处理
            if (isset($params['media_url']) && !empty($params['media_url'])) {
                $offline            = new Offline();
                $params['media_id'] = $offline->uploadWeiXinTemporary(['file' => $params['media_url']]);
                //                $subData['media_id'] = $params['media_ids'];
            }
            $params['is_reject'] = 0;

            // 1.查询萌芽是否有单据
            $wms_order_status = 0; // 0=不处理，1=为空，2=已存在，3=已确认一致，4=已确认不一致
            $done_wms_orders  = [];
            if (!empty($params['return_courier_no']) && !empty($params['is_push_wms']) && intval($params['is_push_wms']) === 1) {
                $wms_order = \Curl::queryWmsSalesOrder(['wy_no' => $params['return_courier_no']]);

                $wms_order_list = $wms_order ?? [];
                if (!empty($wms_order_list)) {
                    $p_detail_json = [];
                    $p_gooods_num = [];
                    foreach ($params['detail_json'] as $pdj) {
                        $p_detail_json[] = "{$pdj['short_code']}_{$pdj['nums']}";
                        $p_gooods_num[] = [
                            'fictitious' => $params['warehouse_code'],
                            'fictitious_num' => $pdj['nums'],
                            'short_code' => $pdj['short_code'],
                            'bar_code' => $pdj['bar_code'],
                        ];
                    }
                    foreach ($wms_order_list as $wms_order_item) {
                        $p_return_courier_no = explode(',', $params['return_courier_no']);
                        if (in_array($wms_order_item['status_value'], [5,6]) && $wms_order_item['order_no'] == $params['sub_order_no'] && empty(array_diff($p_return_courier_no, $wms_order_item['wy_no']))) {
                            //判断是否匹配
                            $wms_detail_json = [];
                            foreach ($wms_order_item['goods_num'] as $pdj) {
                                if ($pdj['fictitious'] == $params['warehouse_code']) {
                                    $wms_detail_json[] = "{$pdj['short_code']}_{$pdj['fictitious_num']}";
                                }
                            }

                            if (empty(array_diff($p_detail_json, $wms_detail_json))) {
                                //匹配
                                //萌芽单据由 已确认 改为 已完成  并且修改原销售单号, 中台单据直接审核完成.
                                $push_wms = false;
                                $push_erp = true;
                                $params['dingtalk_status'] = 2;
                                \Curl::syncReturnsConfirm([
                                    'order_no' => $params['sub_order_no'],
                                    'wy_no'    => $params['return_courier_no'],
                                    'goods_num'    => $p_gooods_num,
                                ]);
                                break;
                            } else {
                                //不匹配
                                throw new Exception('请到萌芽核实到货');
                            }
                        }
                    }
                }
            }

            SalesReturnModel::where('id', $params['id'])->update($params);
            if (!empty($params['is_push_wms']) && intval($params['is_push_wms']) === 1) {

                // 推送萌牙WMS
                if($push_wms){
                    self::pushWms($params);
                }
                if($push_erp){
                    $erp_res = (new self())->SalesReturnPushErp([
                        'operator_id'   => $user_info['id'] ?? 0,
                        'operator_name' => $user_info['realname'] ?? '系统',
                        'bill_no'       => $params['bill_no'],
                    ]);
                    if ($erp_res !== true) {
                        Log::record('推送ERP失败1:', $erp_res);
                    }
                }
            } else {
                //提交审批
                $SalesReturn            = new SalesReturn();
                $params['admin_id']     = $params['operator_id'];
                $params['items_info']   = $params['detail_json'];
                $params['id']           = $id;
                $is_gift_arr            = array_column($params['detail_json'], 'is_gift');
                $params['is_have_gift'] = in_array(1, $is_gift_arr) ? "是赠品" : "无赠品"; //有无赠品
                $SalesReturn->ordinarySaleOrderCreateVerify($params);
            }


            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            throw new ValidateException($exception->getMessage());
        }
    }

    public static function approval($params)
    {
        //审批
        $approval = new SalesReturn();
        $result   = $approval->ordinarySaleOrderVerifyCallBack($params);
        return $result;
    }

    /**
     * 筛选需要开票的销售单据
     * @param $params
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function searchSalesDocuments($params)
    {
        $where = [];
        if (isset($params['sub_order_no']) && !empty($params['sub_order_no'])) {
            $sub_order_no = explode(',', trim($params['sub_order_no']));
            $where[]      = ['sub_order_no', 'in', array_unique($sub_order_no)];
        }
        //        $where[] = ['invoice_progress', 'in', [0, 3]];
        $field = "sub_order_no,items_info,order_qty,payment_amount,items_info,created_time,operator";
        $list  = [];
        $count = 0;
        $page  = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        #分页起始值
        $offset  = ($page - 1) * $limit;
        $where[] = ['created_time', '>=', 1672502400]; //2023-01-01 之后的数据
        if ($params['sales_type'] == 1) {
            $list = self::getSalesDocumentsonline([$params['sub_order_no']]);
            if ($list) {
                foreach ($list as $key => &$val) {
                    $SalesReturn       = SalesReturnModel::where('sub_order_no', $val['sub_order_no'])->where('dingtalk_status', '<>', 3)->select()->toArray();
                    $val['all_return'] = $SalesReturn ? 2 : 1; //全部退款 1 部分退款 2
                    $refund_money = SalesReturnModel::where('sub_order_no', $val['sub_order_no'])->where('dingtalk_status', '<>', 3)->sum('return_amount');
                    $val['residue_refund_money'] = strval($val['payment_amount'] - $refund_money);
                    $item              = $val['items_info'];
                    $items_info        = [];
                    foreach ($item as $k => &$v) {

                        //获取所有已退款的订单 产品简码数据
                        $short_code_arr_detail = self::getSalesReturnPrice($SalesReturn);
                        $short_code_arr        = array_column($short_code_arr_detail, 'short_code');
                        //有重合
                        if (in_array($v['short_code'], $short_code_arr)) {
                            $short_code_arr_detail_nums = 0; //已退货数据数量

                            foreach ($short_code_arr_detail as $value) {
                                if ($value['key'] == $k) $short_code_arr_detail_nums += $value['nums'];
                            }
                            if ((intval($v['nums']) - $short_code_arr_detail_nums) == 0) continue;
                            $nums = isset($v['nums']) ? (intval($v['nums']) - $short_code_arr_detail_nums) : 0;

                            $v['nums'] = $nums;
                            array_push($items_info, $v);
                        } else {
                            array_push($items_info, $v);
                        }
                    }
                    $val['items_info'] = $items_info;
                    if (preg_match('/^VHG/', $val['sub_order_no'])) {
                        $erp_push_amount       = Db::name('sub_order_extend')->where('sub_order_no', $val['sub_order_no'])->value('erp_push_amount');
                        $val['payment_amount'] = $erp_push_amount;
                    }
                }
            }
            $count = count($list);
        } else if ($params['sales_type'] == 2) { #线下销售
            $field   .= ',corp,customer,customer_code,customer_abbreviation,settle_customer,settle_customer_code,department,department_code,clerk,clerk_code,warehouse,warehouse_code,delivery_mode,delivery_mode_code,express_pay_method,express_pay_method_code,settlement_method,settlement_method_code';
            $where[] = [['dingtalk_status', '=', 2]];
            $where[] = [['is_reject', '=', 0]];
            $where[] = [['corp', 'in', ['001', '515', '003']]]; //科技公司
            if (isset($params['customer']) && !empty($params['customer'])) {
                $where[] = ['customer', '=', trim($params['customer'])];
            }
            if (isset($params['settle_customer']) && !empty($params['settle_customer'])) {
                $where[] = ['settle_customer', '=', trim($params['settle_customer'])];
            }
            if (isset($params['customer_abbreviation']) && !empty($params['customer_abbreviation'])) {
                $where[] = ['customer_abbreviation', '=', trim($params['customer_abbreviation'])];
            }
            $list  = Db::name("offline_order")->field($field)->where($where)->limit(intval($offset), intval($limit))->select()->toArray();
            $count = Db::name("offline_order")->where($where)->count('sub_order_no');
            //查询制单人名称
            $admin_id_arr = array_unique(array_column($list, 'operator'));
            $admin_id_str = implode(',', $admin_id_arr);
            $adminInfo    = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $admin_id_str, 'field' => 'realname']);

            foreach ($list as $key => &$val) {
                $item = explode(",", $val['items_info']);
                //查询已创建的订单产品
                $SalesReturn = SalesReturnModel::where('sub_order_no', $val['sub_order_no'])->where('dingtalk_status', '<>', 3)->select()->toArray();

                $items_info        = [];
                $val['all_return'] = $SalesReturn ? 2 : 1; //全部退款 1 部分退款 2
                $refund_money = SalesReturnModel::where('sub_order_no', $val['sub_order_no'])->where('dingtalk_status', '<>', 3)->sum('return_amount');
                $val['residue_refund_money'] = strval($val['payment_amount'] - $refund_money);
                foreach ($item as $k => $v) {

                    $goods = explode('*', $v);

                    //判断是否在已有数据。有
                    if ($SalesReturn) {

                        //获取所有已退款的订单 产品简码数据
                        $short_code_arr_detail = self::getSalesReturnPrice($SalesReturn);
                        $short_code_arr        = array_column($short_code_arr_detail, 'short_code');

                        //有重合
                        if (in_array($goods[1], $short_code_arr)) {
                            $short_code_arr_detail_nums = 0; //已退货数据数量

                            foreach ($short_code_arr_detail as $value) {
                                //                                if($value['short_code'] == $goods[1]) $short_code_arr_detail_nums +=$value['nums'];
                                if ($value['key'] == $k) $short_code_arr_detail_nums += $value['nums'];
                            }
                            //                            var_dump((intval($goods[2])));
                            if ((intval($goods[2]) - $short_code_arr_detail_nums) == 0) continue;
                            $nums         = isset($goods[2]) ? (intval($goods[2]) - $short_code_arr_detail_nums) : 0;
                            $items_detail = [
                                'key'             => $k,
                                'bar_code'        => isset($goods[0]) ? $goods[0] : '',
                                'short_code'      => isset($goods[1]) ? $goods[1] : '',
                                'nums'            => $nums,
                                'tax_unit_price'  => isset($goods[3]) ? $goods[3] : 0,
                                'tax_total_price' => isset($goods[12]) ? $goods[12] : ($goods[3] * $goods[2]),
                                'product_name'    => isset($goods[6]) ? $goods[6] : '',
                                'unit'            => isset($goods[8]) ? $goods[8] : '',
                                'year'            => isset($goods[7]) ? $goods[7] : '',
                                'capacity'        => isset($goods[11]) ? $goods[11] : '',
                                'tax_rate'        => "13%",
                                'is_gift'         => isset($goods[4]) ? intval($goods[4]) : 0,
                            ];
                        } else {
                            $items_detail = [
                                'key'             => $k,
                                'bar_code'        => isset($goods[0]) ? $goods[0] : '',
                                'short_code'      => isset($goods[1]) ? $goods[1] : '',
                                'nums'            => isset($goods[2]) ? intval($goods[2]) : 0,
                                'tax_unit_price'  => isset($goods[3]) ? $goods[3] : 0,
                                'tax_total_price' => isset($goods[12]) ? $goods[12] : ($goods[3] * $goods[2]),
                                'product_name'    => isset($goods[6]) ? $goods[6] : '',
                                'unit'            => isset($goods[8]) ? $goods[8] : '',
                                'year'            => isset($goods[7]) ? $goods[7] : '',
                                'capacity'        => isset($goods[11]) ? $goods[11] : '',
                                'tax_rate'        => "13%",
                                'is_gift'         => isset($goods[4]) ? intval($goods[4]) : 0,
                            ];
                        }
                    } else {
                        $items_detail = [
                            'key'             => $k,
                            'bar_code'        => isset($goods[0]) ? $goods[0] : '',
                            'short_code'      => isset($goods[1]) ? $goods[1] : '',
                            'nums'            => isset($goods[2]) ? intval($goods[2]) : 0,
                            'tax_unit_price'  => isset($goods[3]) ? $goods[3] : 0,
                            'tax_total_price' => isset($goods[12]) ? $goods[12] : ($goods[3] * $goods[2]),
                            'product_name'    => isset($goods[6]) ? $goods[6] : '',
                            'unit'            => isset($goods[8]) ? $goods[8] : '',
                            'year'            => isset($goods[7]) ? $goods[7] : '',
                            'capacity'        => isset($goods[11]) ? $goods[11] : '',
                            'tax_rate'        => "13%",
                            'is_gift'         => isset($goods[4]) ? intval($goods[4]) : 0,
                        ];
                    }

                    array_push($items_info, $items_detail);
                }
                $list[$key]['items_info'] = $items_info;
                $val['created_time']      = date("Y-m-d H:i:s");
                $val['documents_type']    = 1;
                $val['operator_zdr_name'] = isset($adminInfo['data'][$val['operator']]) ? $adminInfo['data'][$val['operator']] : '';
            }
        } elseif ($params['sales_type'] == 3) { #三方订单萌芽推送成功的
            //            $where[]=['push_wms_status','=',1];
            $where[] = ['items_info', '<>', ""];
            //查询仓库是科技公司的
            //todo
            $list = Db::name("tripartite_order")->field($field . ',warehouse_id')->where($where)->limit(intval($offset), intval($limit))->select()->toArray();
            //            $count = Db::name("tripartite_order")->where($where)->count('sub_order_no');
            //查询制单人名称
            $admin_id_arr          = array_unique(array_column($list, 'operator'));
            $admin_id_str          = implode(',', $admin_id_arr);
            $adminInfo             = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $admin_id_str, 'field' => 'realname']);
            $tripartite_order      = array_column($list, 'sub_order_no');
            $tripartite_order_data = self::getTripartiteOrderCorp($tripartite_order);
            $sub_order_no_key      = array_column($tripartite_order_data, 'corp', 'sub_order_no');
            $data                  = [];
            foreach ($list as $key => &$val) {
                $item = explode(",", $val['items_info']);
                //查询已创建的订单产品
                $SalesReturn       = SalesReturnModel::where('sub_order_no', $val['sub_order_no'])->where('dingtalk_status', '<>', 3)->select()->toArray();
                $val['all_return'] = $SalesReturn ? 2 : 1; //全部退款 1 部分退款 2
                $refund_money = SalesReturnModel::where('sub_order_no', $val['sub_order_no'])->where('dingtalk_status', '<>', 3)->sum('return_amount');
                $val['residue_refund_money'] = strval($val['payment_amount'] - $refund_money);
                $items_info        = [];
                foreach ($item as $k => $v) {
                    $goods = explode('*', $v);

                    //判断是否在已有数据。有
                    if ($SalesReturn) {
                        //获取所有已退款的订单 产品简码数据
                        $short_code_arr_detail = self::getSalesReturnPrice($SalesReturn);
                        $short_code_arr        = array_column($short_code_arr_detail, 'short_code');
                        $price                 = isset($goods[2]) ? $goods[2] : 0;
                        $product               = self::shortSearchProduct(isset($goods[0]) ? $goods[0] : '');
                        $nums                  = isset($goods[1]) && $goods[1] != 0 ? intval($goods[1]) : 1;
                        $nums1                 = isset($goods[1]) && $goods[1] != 0 ? intval($goods[1]) : 1;
                        if (in_array($goods[0], $short_code_arr)) {
                            $short_code_arr_detail_nums = 0; //已退货数据数量
                            foreach ($short_code_arr_detail as $value) {
                                if ($value['key'] == $k) $short_code_arr_detail_nums += $value['nums'];
                            }
                            if ((intval($goods[1] * $val['order_qty']) - $short_code_arr_detail_nums) == 0) continue;
                            //                            echo $goods[1]."+++".$short_code_arr_detail_nums;
                            $nums         = isset($goods[1]) ? (intval($goods[1]) * $val['order_qty'] - $short_code_arr_detail_nums) : 0;
                            $items_detail = [
                                'key'             => $k,
                                'bar_code'        => $product['bar_code'] ?? '',
                                'short_code'      => isset($goods[0]) ? $goods[0] : '',
                                'nums'            => $nums,
                                'tax_unit_price'  => $price == 0 ? 0 : bcdiv($price / $val['order_qty'], $nums1, 2),
                                'tax_total_price' => $price == 0 ? 0 : bcdiv($price, 1, 2),
                                'product_name'    => $product['product_name'] ?? "",
                                'unit'            => $product['product_unit'] ?? "",
                                'year'            => $product['year'] ?? "",
                                'capacity'        => $product['capacity'] ?? "",
                                'tax_rate'        => "13%",
                                'is_gift'         => isset($goods[4]) ? intval($goods[4]) : 0,
                            ];
                        } else {
                            $items_detail = [
                                'key'             => $k,
                                'bar_code'        => $product['bar_code'] ?? '',
                                'short_code'      => isset($goods[0]) ? $goods[0] : '',
                                'nums'            => $nums,
                                'tax_unit_price'  => $price == 0 ? 0 : bcdiv($price, $nums, 2),
                                'tax_total_price' => $price == 0 ? 0 : bcdiv($price, 1, 2),
                                'product_name'    => $product['product_name'] ?? "",
                                'unit'            => $product['product_unit'] ?? "",
                                'year'            => $product['year'] ?? "",
                                'capacity'        => $product['capacity'] ?? "",
                                'tax_rate'        => "13%",
                                'is_gift'         => isset($goods[4]) ? intval($goods[4]) : 0,
                            ];
                        }
                    } else {
                        $price        = isset($goods[2]) ? $goods[2] : 0;
                        $product      = self::shortSearchProduct(isset($goods[0]) ? $goods[0] : '');
                        $nums         = isset($goods[1]) && $goods[1] != 0 ? intval($goods[1]) * $val['order_qty'] : 1;
                        $items_detail = [
                            'key'             => $k,
                            'bar_code'        => $product['bar_code'] ?? '',
                            'short_code'      => isset($goods[0]) ? $goods[0] : '',
                            'nums'            => $nums,
                            'tax_unit_price'  => $price == 0 ? 0 : bcdiv($price, $nums, 2),
                            'tax_total_price' => $price == 0 ? 0 : bcdiv($price, 1, 2),
                            'product_name'    => $product['product_name'] ?? "",
                            'unit'            => $product['product_unit'] ?? "",
                            'year'            => $product['year'] ?? "",
                            'capacity'        => $product['capacity'] ?? "",
                            'tax_rate'        => "13%",
                            'is_gift'         => isset($goods[4]) ? intval($goods[4]) : 0,
                        ];
                    }


                    array_push($items_info, $items_detail);
                }
                $list[$key]['items_info'] = $items_info;
                $val['created_time']      = date("Y-m-d H:i:s");
                $val['documents_type']    = 1;
                $val['operator_zdr_name'] = isset($adminInfo['data'][$val['operator']]) ? $adminInfo['data'][$val['operator']] : '';

                $val['corp']                    = $sub_order_no_key[$val['sub_order_no']] ?? "";
                $val['customer']                = "";
                $val['customer_code']           = "";
                $val['customer_abbreviation']   = "";
                $val['settle_customer']         = "";
                $val['settle_customer_code']    = "";
                $val['department']              = "";
                $val['department_code']         = "";
                $val['clerk']                   = "";
                $val['clerk_code']              = "";
                $val['warehouse']               = "";
                $val['warehouse_code']          = "";
                $val['delivery_mode']           = "";
                $val['delivery_mode_code']      = "";
                $val['express_pay_method']      = "";
                $val['express_pay_method_code'] = "";
                $val['settlement_method']       = "";
                $val['settlement_method_code']  = "";
                $val['documents_type']          = "";
                $val['operator_zdr_name']       = "";

//                if ($sub_order_no_key[$val['sub_order_no']] == '001') array_push($data, $val);
                array_push($data, $val);
            }
            $list  = $data;
            $count = count($data);
        }
        $result['list']  = $list;
        $result['total'] = $count;
        return $result;
    }

    /**
     * es通过简码查询商品名称
     * @param $short_code
     * @return array|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function shortSearchProduct($short_code)
    {
        $es      = new ElasticSearchService();
        $data    = $es->getDocumentList(['index' => ['panshi.products'], 'match' => [['short_code' => $short_code]], 'source' => ['cn_product_name', 'bar_code', 'short_code', 'capacity', 'product_unit_name', 'products_years'], 'limit' => 100]);
        $product = ['capacity' => '', 'product_name' => '', 'product_unit' => "瓶", 'year' => 0, "bar_code" => ""];

        if (count($data['data'])) {

            $product = [
                "capacity"     => isset($data['data'][0]['capacity']) ? $data['data'][0]['capacity'] : '',
                "product_name" => isset($data['data'][0]['cn_product_name']) ? $data['data'][0]['cn_product_name'] : '',
                "product_unit" => isset($data['data'][0]['product_unit_name']) ? $data['data'][0]['product_unit_name'] : '瓶',
                "year"         => isset($data['data'][0]['products_years']) ? $data['data'][0]['products_years'] : '',
                "bar_code"     => isset($data['data'][0]['bar_code']) ? $data['data'][0]['bar_code'] : '',
            ];
        }
        return $product;
    }

    /**
     * Description:新增销售单审批回调处理
     * Author: zrc
     * Date: 2022/10/8
     * Time: 11:25
     * @param $params
     * @return int
     * @throws \think\db\exception\DbException
     */
    public function ordinarySaleOrderVerifyCallBack($requestparams)
    {
        Log::record('企业微信退货单审批日志', json_encode($requestparams));
        $params = $requestparams['process_instance'];
        // 获取发起人信息
        $userRes   = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['originator_userid']]);
        $user_info = [];
        if (!empty($userRes['data'])) {
            foreach ($userRes['data'] as $v) {
                $user_info = $v;
            }
        }

        $ID = '';
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '模板ID') $ID = trim($val['value']);
            }
        }

        if (empty($ID)) {
            $this->weChatSendText($params['originator_userid'], '企业微信退货单审批:未获取到模板ID,审批流处理失败!');
            $this->throwError('企业微信退货单审批:未获取到模板ID,审批流处理失败!');
        }
        $bill_items = Db::name('sales_return')->where(['bill_no|group_bill_no' => $ID])->column('sub_order_no,bill_no');

        $bill_nos = array_column($bill_items,'bill_no');
        $sub_order_nos = array_column($bill_items,'sub_order_no');
        //审批通过处理
        if ($params['result'] == 'agree') {
            $updateData = array(
                'dingtalk_status' => 2,
                'update_time'     => time(),
                'approval_time'   => time(),
                'push_erp_time'   => time(),
            );
            //更改订单钉钉审批状态
            $res = Db::name('sales_return')->where('bill_no', 'in', $bill_nos)->update($updateData);

            // 审批通过推送ERP
            if (!empty($res)) {
                foreach ($bill_nos as $bill_no) {
                    $erp_res = $this->SalesReturnPushErp([
                        'operator_id'   => $user_info['id'] ?? 0,
                        'operator_name' => $user_info['realname'] ?? '系统',
                        'bill_no'       => $bill_no,
                    ]);
                    if ($erp_res !== true) {
                        Log::record('推送ERP失败', $erp_res);
                    }
                }
            }

            $latest_work_orders = Db::table('vh_customer_service.vh_work_order')
                ->where('order_no', 'in', $sub_order_nos)
                ->order('created_time desc')
                ->column('id, order_no, work_order_type, created_time');

            $sub_not_order_nos = [];
            $latest_orders     = [];
            foreach ($latest_work_orders as $work_order) {
                if (!isset($latest_orders[$work_order['order_no']])) {
                    $latest_orders[$work_order['order_no']] = $work_order;
                    if ($work_order['work_order_type'] != 5) { // 工单类型=退货退款
                        $sub_not_order_nos[] = $work_order['order_no'];
                    }
                }
            }

            // 查询已开票的订单
            $invoiced_orders = Db::table('vh_invoice.vh_invoice_code')->alias('t1')
                ->leftJoin('vh_orders.vh_offline_order t2', 't1.order_no = t2.sub_order_no')
                ->where('t1.status', 'in', [1, 2])  // 开票状态 1,待开篇2，已开票3，开票拒绝
                ->whereNotIn('t1.order_no', $sub_not_order_nos)
                ->whereIn('t1.order_no', $sub_order_nos)
                ->column('t1.invoice_code,t1.order_no,t2.operator');
            $realnames = Db::table('vh_authority.vh_admins')->where('id','in',array_column($invoiced_orders, 'operator'))->column('realname');

            // 如果存在已开票订单,发送提醒
            if (!empty($invoiced_orders)) {
                $msg = "原订单号:" . implode(',', array_column($invoiced_orders, 'order_no')) . "\n";
                $msg .= "对应发票单号:" . implode(',', array_column($invoiced_orders, 'invoice_code')) . "\n";
                $msg .= "制单人:" . implode(',', $realnames) . "\n";
                $msg .= "该订单已开具发票,请财务处理冲红发票";

                $res = \Curl::sendWechatSender([
                    'msg'          => $msg,
                    'at'           => '',
                    'access_token' => 'b08f7db0-b1b0-4cfc-985b-11156adaada1',
                ]);
            }

        }
        if ($params['result'] == 'refuse' || $params['status'] == 'TERMINATED') {
            $updateData = array(
                'dingtalk_status' => 3,
                'update_time'     => time(),
                'approval_time'   => time(),
            );
            //更改订单钉钉审批状态
            Db::name('sales_return')->where('bill_no', 'in', $bill_nos)->update($updateData);
        }
        //审批流处理
        $spNodeStatus          = $params['spNodeStatus'];
        $spNodeApprovers       = $params['spNodeApprovers'];
        $approval_process      = [];
        $approver              = "";
        $approval_process_info = Db::name('sales_return')->where(['bill_no' => $ID])->value('approval_process');
        $approval_processArr   = json_decode($approval_process_info, true);
        if (!empty($approval_processArr)) {
            foreach ($approval_processArr as $key => $val) {
                if ($val['status'] != $spNodeStatus[$key]) {
                    $approval_process[] = array(
                        'approver'    => $val['approver'],
                        'status'      => $spNodeStatus[$key],
                        'approver_id' => $val['approver_id'],
                        'check_time'  => $spNodeStatus[$key] > 1 ? date('Y-m-d H:i:s', time()) : '',
                    );
                } else {
                    $approval_process[] = array(
                        'approver'    => $val['approver'],
                        'status'      => $val['status'],
                        'approver_id' => $val['approver_id'],
                        'check_time'  => $val['check_time'],
                    );
                }
                if ($spNodeStatus[$key] > 1) $approver = $val['approver'];
            }
            $updateData = array(
                'approver'         => $approver,
                'approval_process' => json_encode($approval_process, JSON_UNESCAPED_UNICODE)
            );
        } else {
            foreach ($spNodeApprovers as $key => $val) {
                $uidArr      = explode('/', $val);
                $userNameArr = [];
                $userName    = '';
                foreach ($uidArr as &$v) {
                    $userInfo = $this->httpGet(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/contacts/userinfo/byuid', ['uid' => $v]);
                    if (isset($userInfo['data']['name'])) $userNameArr[] = $userInfo['data']['name'];
                }
                $userName           = implode('/', $userNameArr);
                $approval_process[] = array(
                    'approver'    => $userName,
                    'status'      => $spNodeStatus[$key],
                    'approver_id' => $val,
                    'check_time'  => $spNodeStatus[$key] > 1 ? date('Y-m-d H:i:s', time()) : '',
                );
                if ($spNodeStatus[$key] > 1) $approver = $userName;
            }
            $updateData = array(
                'approver'         => $approver,
                'approval_process' => json_encode($approval_process, JSON_UNESCAPED_UNICODE)
            );
        }
        //更改订单钉钉审批状态
        Db::name('sales_return')->where('bill_no', 'in', $bill_nos)->update($updateData);
        return true;
    }


    /**
     * Description:企业微信发送消息
     * Author: zrc
     * Date: 2022/10/8
     * Time: 11:26
     * @param $userid
     * @param $content
     */
    public function weChatSendText($userid, $content)
    {
        $msgData = array(
            'content' => $content,
            'userid'  => $userid,
            'msgtype' => 'text',
            'agentid' => 0,
        );
        httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
    }

    /**
     * Description:新增销售单创建审批
     * Author: zrc
     * Date: 2022/10/8
     * Time: 10:54
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function ordinarySaleOrderCreateVerify($params)
    {
        //日志记录
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'saleOrderLog' . '.log', json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        //获取发起人信息
        $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
        if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('发起企业微信批失败：未获取到企业微信用户信息');
        $userid = $userInfo['data'][$params['admin_id']]['userid']; //企业微信userid
        //客户名称审批字段匹配处理
        $params['customerApproval'] = $params['customer'];
        $customer                   = env('ORDERS.CUSTOMER');
        $customerArr                = explode(',', $customer);
        if (!in_array($params['customer'], $customerArr)) $params['customerApproval'] = '其他';
        //审批表单数据处理
        $goodsInfo  = [];
        $totalMoney = 0;
        //商品详情处理
        foreach ($params['items_info'] as $key => $val) {
            $goodsInfo[$key] = array(
                array(
                    'name'  => '简码',
                    'value' => strval($val['short_code']),
                ),
                array(
                    'name'  => '商品名称',
                    'value' => $val['product_name'],
                ),
                array(
                    'name'  => '条码',
                    'value' => strval($val['bar_code']),
                ),
                array(
                    'name'  => '年份',
                    'value' => strval($val['year']),
                ),
                array(
                    'name'  => '含税单价',
                    'value' => $val['tax_unit_price'] == 0 ? '0' : strval($val['tax_unit_price']),
                ),
                array(
                    'name'  => '规格型号',
                    'value' => strval($val['unit']),
                ),
                array(
                    'name'  => '退货数量',
                    'value' => strval($val['nums']),
                ),
                array(
                    'name'  => '是否赠品',
                    'value' => $val['is_gift'] == 1 ? '是' : "否",
                ),
            );
            //合计金额处理
            $totalMoney += ($val['tax_unit_price'] * $val['nums']);
        }
        //附件处理
        $media_id_arr = [];
        if (isset($params['media_id']) && !empty($params['media_id'])) {
            $media_id = explode(',', $params['media_id']);
            foreach ($media_id as &$v) {
                $media_id_arr[] = ['fileId' => $v];
            }
        }
        $form_component_values = array(
            array(
                'name'  => '退货单号',
                'value' => $params['bill_no'],
            ),
            array(
                'name'  => '原销售单号',
                'value' => $params['sub_order_no'],
            ),
            array(
                'name'  => '客户名称',
                'value' => $params['customer'],
            ),
            array(
                'name'  => '业务部门',
                'value' => isset($params['department']) ? $params['department'] : '',
            ),
            array(
                'name'  => '业务员',
                'value' => $params['clerk'],
            ),
            array(
                'name'  => '合计金额',
                'value' => strval($params['return_amount'] ?? ($totalMoney == 0 ? '0' : $totalMoney)),
            ),
            array(
                'name'  => '收款方式',
                'value' => isset($params['settlement_method']) ? $params['settlement_method'] : '',
            ),
            array(
                'name'  => '备注',
                'value' => isset($params['remarks']) ? $params['remarks'] : '',
            ),
            array(
                'name'  => '商品详情',
                'value' => $goodsInfo
            ),
            array(
                'name'  => '退回仓库',
                'value' => $params['warehouse'],
            ),
            array(
                'name'  => '仓库编码',
                'value' => $params['warehouse_code'],
            ),
            array(
                'name'  => '运输方式',
                'value' => $params['delivery_mode'],
            ),

            array(
                'name'  => '收件人',
                'value' => $params['contacts_name'] ?? "",
            ),
            array(
                'name'  => '收件人手机号',
                'value' => $params['contacts_phone'] ?? "",
            ),
            array(
                'name'  => '送货地址',
                'value' => $params['contacts_addr'] ?? "",
            ),

            array(
                'name'  => '审批字段',
                'value' => $params['customerApproval'],
            ),
            array(
                'name'  => '数据来源',
                'value' => '线下业务',
            ),
            array(
                'name'  => '模板ID',
                'value' => (string)$params['bill_no'],
            ),
            array(
                'name'  => '退货快递单号',
                'value' => (string)($params['return_courier_no'] ?? ""),
            ),
        );
        if (!empty($media_id_arr)) {
            $form_component_values[] = array(
                'name'  => '附件',
                'value' => $media_id_arr
            );
        }

        $process_code = env('ORDERS.ordinary_sales_return_order_verify_id');

        $pushData = array(
            'form_component_values' => $form_component_values,
            'process_code'          => $process_code,
            'dept_id'               => 0, //部门传0默认获取自己主部门
            'originator_user_id'    => $userid
        );

        //【新增】在发起审批前先验证审批是否已存在，避免重复发起审批
        $start_time    = time() - 86400;   // 审批创建时间范围的开始时间，10分钟前
        $end_time      = time();           // 当前时间为结束时间
        $existsPayload = [
            "start_time"   => $start_time,
            "end_time"     => $end_time,
//            "creator_uid"  => $userid,
            "template_id"  => $process_code,  // 审批模板ID（根据不同corp已经设置）
//            "department"   => 0,            // 此处默认0，可根据需要调整
            "unique_name"  => "退货单号",       // 唯一字段名称
            "unique_value" => $params['bill_no']  // 使用bill_no作为唯一值
        ];
        $existsUrl     = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/exists';
        $existsResult  = httpPostString($existsUrl, json_encode($existsPayload, JSON_UNESCAPED_UNICODE),'',10);
        if (isset($existsResult['error_code']) && $existsResult['error_code'] == 0
            && isset($existsResult['exists']) && $existsResult['exists'] === true) {
            // 如果审批已存在，则直接返回成功，避免重复发起审批
            Db::name('sales_return')->where('bill_no',$params['bill_no'])->update(['dingtalk_status' => 3]);
            return true;
        }

        $result = httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/create', json_encode($pushData, JSON_UNESCAPED_UNICODE));

        if ($result['error_code'] != 0) throw new ValidateException('发起企业微信审批失败：' . $result['error_msg']);
        //        if ($result['error_code'] != 0) $this->throwError('发起企业微信审批失败：' . $result['error_msg']);
        return true;
    }


    //获取单据编号
    public static function getBillNo()
    {
        $bill_no_param = "TH-" . date("Y-m-d");
        if((env("APP_DEBUG") === true)){
            $bill_no_param = "TEST-TH-" . date("Y-m-d");
        }
        $time          = strtotime(date("Y-m-d", time()) . " 00:00:00");
        $bill_no       = SalesReturnModel::where('create_time', '>=', $time)->order('id', 'desc')->value('bill_no');

        if ($bill_no) {
            $bill_no_unit_nums = substr($bill_no, -4) + 1;
            $bill_no_param     .= "-" . str_pad($bill_no_unit_nums, 4, 0, STR_PAD_LEFT);
        } else {
            $bill_no_param = "TH-" . date("Y-m-d") . "-0001";
            if((env("APP_DEBUG") === true)){
                $bill_no_param = "TEST-TH-" . date("Y-m-d") . "-0001";
            }
        }

        return $bill_no_param;
    }

    //获取销售退货价格
    public static function getSalesReturnPrice($SalesReturn)
    {
        $short_code_price_arr = [];
        foreach ($SalesReturn as $v) {
            $short_code_detail    = (array)$v['detail_json'];
            $short_code_price_arr = array_merge($short_code_price_arr, $short_code_detail);
        }
        return $short_code_price_arr;
    }

    //获取支付主体 根据等单号和订单类型
    public static function getCorpByOrderNo($order)
    {
        /* //特殊订单走 科技公司
         $tsorder = ['SO-2023-01-01-9901','SO-2023-01-01-9913','SO-2023-01-01-9912','SO-2023-01-01-9911','SO-2023-01-01-9910','SO-2023-01-01-9908','SO-2023-01-01-9907','SO-2023-01-01-9905','SO-2023-01-01-9904','SO-2023-01-01-9903','SO-2023-01-01-9902','SO-2023-01-01-9091'];


         //云酒
         $tsorder2 = ["SO-2023-01-03-3292", "SO-2023-01-03-5304", "SO-2023-01-03-9836", "SO-2023-01-04-0001", "SO-2023-01-04-0377", "SO-2023-01-05-3518", "SO-2023-01-09-5455", "SO-2023-01-11-2485", "SO-2023-01-28-2788"];
         $a = array_map(function ($v){
             return [$v,'001'];
         },$tsorder2);
         echo json_encode($a);
         exit();*/
        $data             = [];
        $online_order     = [];
        $offline_order    = [];
        $tripartite_order = [];
        $lw               = [];
        $paimai           = [];
        $store            = [];

        $ts = Db::name('order_pay_corp')->whereIn('sub_order_no', array_column($order, 'sub_order_no'))->select()->toArray();

        foreach ($order as $v) {
            if ($ts) {
                $ts_key = array_column($ts, 'corp', 'sub_order_no');
                if (isset($ts_key[$v['sub_order_no']])) {
                    array_push($data, ['sub_order_no' => $v['sub_order_no'], 'corp' => $ts_key[$v['sub_order_no']]]);
                    continue;
                }
            }

            if ($v['order_type'] == 1) { //酒云线上
                array_push($online_order, $v['sub_order_no']);
            }
            if ($v['order_type'] == 2) { //酒云线下
                array_push($offline_order, $v['sub_order_no']);
            }
            if ($v['order_type'] == 3) { //三方线上
                array_push($tripartite_order, $v['sub_order_no']);
            }

            if ($v['order_type'] == 4) { //老外
                array_push($lw, $v['sub_order_no']);
            }
            if ($v['order_type'] == 11) { //拍卖
                array_push($paimai, $v['sub_order_no']);
            }
            if ($v['order_type'] == 12) { //门店
                array_push($store, $v['sub_order_no']);
            }
        }
        //酒云线上
        if ($online_order) {
            $online_order_data = self::getonlineOrderCorp($online_order);
            $data              = array_merge($data, $online_order_data);
        }
        //酒云线下
        if ($offline_order) {
            $offline_order_data = self::getOfflineOrderCorp($offline_order);
            $data               = array_merge($data, $offline_order_data);
        }

        //酒云三方
        if ($tripartite_order) {
            $tripartite_order_data = self::getTripartiteOrderCorp($tripartite_order);
            $data                  = array_merge($data, $tripartite_order_data);
        }

        //老外买酒
        if ($lw) {
            $lw_data = self::getLwOrderCorp($lw);
            $data    = array_merge($data, $lw_data);
        }

        //拍卖
        if ($paimai) {
            $pai_data = self::getpaimaiOrderCorp($paimai);
            $data     = array_merge($data, $pai_data);
        }
        //门店
        if ($store) {
            $pai_data = self::getStoreOrderCorp($store);
            $data     = array_merge($data, $pai_data);
        }


        return $data;
    }


    /**
     * 获取线上订单，支付主体
     * @param $order
     * @return array [['sub_order_no'=>'123','corp'=>'001']]
     */
    public static function getonlineOrderCorp($order)
    {

        //获取订单 类型
        $es    = new ElasticSearchService();
        $where = ['index' => ['orders'], 'terms' => [['_id' => $order]], 'source' => ['sub_order_no', 'order_type', 'period', 'created_time'], 'limit' => count($order)];
        $data  = $es->getDocumentList($where);

        //订单类型：0-闪购 1-秒发 2-跨境 3-尾货 4-兔头实物 5-酒会(酒历) 6-课程 7-三方 8-线下 9-商家秒发(科技) 10-商家闪购(科技) 11-拍卖(科技)
        $periods = [];
        foreach ($data['data'] as $v) {
            //闪购 尾货
            if ($v['order_type'] == 0 || $v['order_type'] == 3) array_push($periods, $v['period']);
        }

        //获取期数对应的收款主体
        $where          = ['index' => ['periods'], 'terms' => [['id' => $periods]], 'source' => ['payee_merchant_id', 'id'], 'limit' => count($periods)];
        $periodsdata    = $es->getDocumentList($where);
        $periodsdatakey = array_column($periodsdata['data'], 'payee_merchant_id', 'id');
        $items          = [];

        foreach ($data['data'] as $v) {

            //闪购 尾货
            if ($v['order_type'] == 0 || $v['order_type'] == 3) {

                if ($periodsdatakey[$v['period']] == 1) { //云酒
                    $corp = '002';
                } elseif ($periodsdatakey[$v['period']] == 2) { //科技
                    $corp = '001';
                } elseif ($periodsdatakey[$v['period']] == 5) { //渝中区微醺酒业商行
                    $corp = '008';
                } elseif ($periodsdatakey[$v['period']] == 8) {
                    $corp = '515';
                } elseif ($periodsdatakey[$v['period']] == 9) {
                    $corp = '031';
                } elseif ($periodsdatakey[$v['period']] == 10) {
                    $corp = '032';
                } else {
                    continue;
                }
                $item = [
                    'sub_order_no' => $v['sub_order_no'],
                    'corp'         => $corp,
                ];
                array_push($items, $item);
            }
            //秒发 商家秒发 商家闪购
            if ($v['order_type'] == 1 || $v['order_type'] == 9 || $v['order_type'] == 10) {
                $item = [
                    'sub_order_no' => $v['sub_order_no'],
                    'corp'         => '001',
                ];
                array_push($items, $item);
            }
            //跨境
            if ($v['order_type'] == 2) {
                if (strtotime($v['created_time']) > strtotime('2023-02-01 10:00:00')) { //2023-02-01 后 科技公司
                    $corp = '001';
                } else { //云酒
                    $corp = '002';
                }
                $item = [
                    'sub_order_no' => $v['sub_order_no'],
                    'corp'         => $corp,
                ];
                array_push($items, $item);
            }
            //兔头 龙飞说的、兔头类型返回一个 不存在的公司 999 
            if ($v['order_type'] == 4) {

                $item = [
                    'sub_order_no' => $v['sub_order_no'],
                    'corp'         => '002',
                ];
                array_push($items, $item);
            }
        }

        return $items;
    }

    /**
     * 获取线下订单，支付主体
     * @param $order
     * @return array [['sub_order_no'=>'123','corp'=>'001']]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getOfflineOrderCorp($order)
    {
        $field   = 'sub_order_no,corp,warehouse_code,document_type,corp';
        $where[] = ['sub_order_no', 'in', implode(',', $order)];
        $list    = Db::name("offline_order")->field($field)->where($where)->select()->toArray();
        //        $data = array_column($list,'warehouse_code');
        //        $corpWarehouse = self::getTripartiteOrderCorpByWarehouse($data);
        //        $corpWarehousekey = array_column($corpWarehouse,'corp','warehouse');
        $return_data = array_map(function ($v) { //use($corpWarehousekey)
            if ($v['document_type'] == 0) { //样酒 云酒
                $vdata = [
                    'sub_order_no' => $v['sub_order_no'],
                    'corp'         => $v['corp'] ?? "002",
                ];
            } else if ($v['document_type'] == 1) { //销售单 科技
                $vdata = [
                    'sub_order_no' => $v['sub_order_no'],
                    'corp'         => "001",
                ];
            } else if ($v['document_type'] == 2) { //T+销售单 单据本身
                $vdata = [
                    'sub_order_no' => $v['sub_order_no'],
                    'corp'         => $v['corp'],
                ];
            } else {
                new ValidateException("未定义的线下销售单据类型.");
            }
            //            $vdata = [
            //                'sub_order_no'=>$v['sub_order_no'],
            //                'corp'=>$corpWarehousekey[$v['warehouse_code']],
            //            ];
            return $vdata;
        }, $list);
        return $return_data;
    }

    /**
     * 获取三方订单，支付主体
     * @param $order
     * @return array|array[] [['sub_order_no'=>'123','corp'=>'001']]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getTripartiteOrderCorp($order)
    {
        $field   = 'sub_order_no,warehouse_id,store_id';
        $where[] = ['sub_order_no', 'in', implode(',', $order)];
        $list    = Db::name("tripartite_order")->field($field)->where($where)->select()->toArray();
        if (!$list) return [];
        try {
            $warehouse = array_values(array_unique(array_column($list, 'warehouse_id')));
            $warecorp  = self::getTripartiteOrderCorpByWarehouse($warehouse);
            $warehouse = array_column($warecorp, 'corp', 'warehouse');
            $result    = array_map(function ($v) use ($warehouse) {
                if (in_array($v['store_id'], [
                    '62b98b750d601800010dc853',
                    '6426461c6fdda100018a7b76',
                    '646b6293ebbcb20001b9ee01',
                    '1000398259',
                    '5946418',
                    '419938814',
                    '227734657',
                    '28073049081',
                    '10232541',
                    '827871079',
                    '68119621',
                    '686511',
                    '541276454',
                    '566768114',
                    '558695549',
                    'tianmaochaoshi20230518',
                    '10136705',
                    '452890329',
                    '114968496',
                    '492964257',
                    'ldd_1',
                    'wx7ab502be70d73aa5',
                    '216379813',
                    '68029232',
                    '381499438',
                    '02223',
                ])) { //小红书 004 仓库 主体 科技
                    $data = [
                        'sub_order_no' => $v['sub_order_no'],
                        'corp'         => '001'
                    ];
                } else if (in_array($v['store_id'], [
                    '650a60e17fa15200013acf16',
                    '18565487',
                    'wx0fe6132e6a98d48a',
                    '208798183',
                    '320552154',
                    '163005133',
                    '100620515',
                    '381499438',
                ])) { //【小红书】木兰朵Mulando的店，属于木兰朵
                    $data = [
                        'sub_order_no' => $v['sub_order_no'],
                        'corp'         => '003'
                    ];
                } else if (in_array($v['store_id'], [
                    '662831451',
                    '2807304908',
                    '8911798',
                ])) { //桃公子拼多多店铺  仓库 主体 云酒
                    $data = [
                        'sub_order_no' => $v['sub_order_no'],
                        'corp'         => '002'
                    ];
                }  else if (in_array($v['store_id'], [
                    '848265289',
                ])) { //开瓶有益 - 老陈的酒馆
                    $data = [
                        'sub_order_no' => $v['sub_order_no'],
                        'corp'         => '031'
                    ];
                } else if (in_array($v['store_id'], [
                    '187506117',
                    '311029595',
                    '258788035',
                    '517978682',
                    '20369140079',
                ])) { //天猫-蒙大菲酒类旗舰， 松鸽的Soul List 属于松鸽
                    $data = [
                        'sub_order_no' => $v['sub_order_no'],
                        'corp'         => '313'
                    ];
                } else if (in_array($v['store_id'], [
                    '6542368b368edf00013dc6f5',
                    '65113b63effd830001ca90e0',
                    '653b599dbffe730001559bd6',
                ])) { //【小红书】兔总葡萄酒买手店的店，属于重庆兔子星球科技有限公司
                    $data = [
                        'sub_order_no' => $v['sub_order_no'],
                        'corp'         => '515'
                    ];
                } else if (in_array($v['store_id'], [
                    '20318455683',
                    '20121190650',
                    '20959411495',
                ])) { //查询三方订单表company_id
                    $corp       = '001';
                    $company_id = Db::name('tripartite_order')->where(['sub_order_no' => $v['sub_order_no']])->value('company_id');
                    switch ($company_id) {
                        case 0:
                            throw new ValidateException($v['sub_order_no'] . '支付主体未找到');
                            break;
                        case 1:
                            $corp = '002';
                            break;
                        case 2:
                            $corp = '001';
                            break;
                        case 10:
                            $corp = '032';
                            break;
                        case 5:
                            $corp = '008';
                            break;
                    }
                    $data = [
                        'sub_order_no' => $v['sub_order_no'],
                        'corp'         => $corp
                    ];
                } else {

                    if (!isset($warehouse[$v['warehouse_id']])) throw new ValidateException($v['warehouse_id'] . '支付主体未找到');
                    $data = [
                        'sub_order_no' => $v['sub_order_no'],
                        'corp'         => $warehouse[$v['warehouse_id']]
                    ];
                }
                return $data;
            }, $list);
        } catch (\Exception $exception) {
            throw new ValidateException("三方订单仓库，获取支付主体失败:" . $exception->getMessage());
        }

        return $result;
    }

    /**
     * 获取老外订单，支付主体
     * @param $order
     * @return array|array[]
     */
    public static function getLwOrderCorp($order)
    {
        if (!$order) return [];
        $result = array_map(function ($v) {
            return ['sub_order_no' => $v, 'corp' => '001'];
        }, $order);
        return $result;
    }

    /**
     * 拍卖支付主体
     * @param $order
     * @return array|array[]
     */
    public static function getpaimaiOrderCorp($order)
    {
        if (!$order) return [];
        //拍卖 数据库
        $list   = Db::table("vh_auction.vh_orders")->field('order_no,warehouse_code')->whereIn('order_no', $order)->select()->toArray();
        $result = array_map(function ($v) {
            if ($v['warehouse_code'] == 318 || $v['warehouse_code'] == 322) {
                $t = ['sub_order_no' => $v['order_no'], 'corp' => '001'];
            } else {
                $t = ['sub_order_no' => $v['order_no'], 'corp' => '002'];
            }
            return $t;
        }, $list);
        return $result;
    }

    /**
     * 门店支付主体
     * @param $order
     * @return array|array[]
     */
    public static function getStoreOrderCorp($order)
    {
        if (!$order) return [];
        $result = array_map(function ($v) {
            return ['sub_order_no' => $v, 'corp' => '002'];
        }, $order);
        return $result;
    }

    /**
     * 获取三方 支付主体 根据仓库编码
     * @param $warehouse ['005']
     * @return array [['warehouse'=>'005','corp'=>'001']]
     */
    public static function getTripartiteOrderCorpByWarehouse($warehouse)
    {
        if ($warehouse == []) return [];
        $warehouse   = implode(',', $warehouse);
        $method      = '/pushtplus/v3/thirdStore/getCorpByWarehouse';
        $salesreturn = new SalesReturn();
        $result      = $salesreturn->httpGet(env('ITEM.PUSH_T_PLUS_URL') . $method, ['warehouse' => $warehouse]);
        if (!isset($result['error_code']) || $result['error_code'] != 0) {
            throw new ValidateException('三方订单仓库，获取支付主体失败!');
        }
        return $result['data'];
    }

    public static function getSalesDocumentsonline($order)
    {
        //获取订单 类型
        $es    = new ElasticSearchService();
        $where = ['index' => ['orders'], 'terms' => [['_id' => $order]], 'source' => ['sub_order_no', 'payment_amount', 'order_qty', 'package_id', 'warehouse_code', 'period', 'order_type', 'product_code', 'uec_code', 'main_order_no']];
        $data  = $es->getDocumentList($where);
        if ($data['total']['value'] == 0) return [];
        $package_id   = array_column($data['data'], 'package_id');
        $sub_order_no = array_column($data['data'], 'sub_order_no'); //线上订单
        $order_data   = $data['data'];
        if ($order_data[0]['order_type'] == 11) {
            //验证仓库
            if (!in_array($order_data[0]['uec_code'], ['318', '309'])) throw new ValidateException('该订单不属于【佰酿云酒(南通拍卖仓) 】或【南通次品仓】');
            //获取订单支付主体
            $corp                          = array(array('sub_order_no' => $order_data[0]['sub_order_no'], 'corp' => '001'));
            $corpkey                       = array_column($corp, 'corp', 'sub_order_no');
            $order_data[0]['product_code'] = json_decode($order_data[0]['product_code'], true);
            $short_code                    = array_column($order_data[0]['product_code'], 'short_code');
            $panshi_data                   = $es->getDocumentList(['index' => ['panshi.products'], 'terms' => [['short_code' => $short_code]], 'source' => ['id', 'cn_product_name', 'bar_code', 'short_code', 'capacity', 'product_unit_name', 'products_years'], 'limit' => 100]);
            $product_data                  = $panshi_data['data'];
            if (count($product_data)) {
                foreach ($product_data as &$v) {
                    $v["capacity"]     = isset($v['capacity']) ? $v['capacity'] : '';
                    $v["product_name"] = isset($v['cn_product_name']) ? $v['cn_product_name'] : '';
                    $v["product_unit"] = isset($v['product_unit_name']) ? $v['product_unit_name'] : '瓶';
                    $v["year"]         = isset($v['products_years']) ? $v['products_years'] : '';
                    $v["bar_code"]     = isset($v['bar_code']) ? $v['bar_code'] : '';
                }
            }
            foreach ($order_data[0]['product_code'] as &$val) {
                foreach ($product_data as &$v) {
                    if ($val['short_code'] == $v['short_code']) $val['product_id'] = $v['id'];
                }
            }
            $product_key      = array_column($order_data, 'product_code', 'period');
            $product_data_key = array_column($product_data, null, 'id');
        } else {
            //获取订单支付主体
            $corp    = self::getonlineOrderCorp($sub_order_no);
            $corpkey = array_column($corp, 'corp', 'sub_order_no');

            //获取套餐
            $where       = ['index' => ['periods_set'], 'terms' => [['_id' => $package_id]], 'source' => ['associated_products', 'id', 'is_mystery_box']];
            $data        = $es->getDocumentList($where);
            $periods_set = $data['data'];
            foreach ($periods_set as &$v) {
                if ($v['is_mystery_box'] == 1) {
                    $associated_products = Db::name('order_mystery_box_log')->where(['main_order_no' => $order_data[0]['main_order_no']])->value('product_info');
                    if (empty($associated_products)) throw new ValidateException('未获取到盲盒订单产品信息');
                    $v['associated_products'] = json_decode($associated_products, true);
                } else {
                    $v['associated_products'] = json_decode($v['associated_products'], true);
                }
            }
            $product_ids = array_column($v['associated_products'], 'product_id');
            $product_key = array_column($periods_set, 'associated_products', 'id');

            //获取产品
            $product_data     = self::shortSearchProductBatch($product_ids);
            $product_data_key = array_column($product_data, null, 'id');
        }
        $filldata    = [
            //            'items_info'=>$items_info,
            'created_time'            => date("Y-m-d H:i:s"),
            'documents_type'          => 1,
            'corp'                    => '',
            'customer'                => '',
            'customer_code'           => '',
            'customer_abbreviation'   => '',
            'settle_customer'         => '',
            'settle_customer_code'    => '',
            'department'              => '',
            'department_code'         => '',
            'clerk'                   => '',
            'clerk_code'              => '',
            'warehouse'               => '',
            'delivery_mode'           => '',
            'delivery_mode_code'      => '',
            'express_pay_method'      => '',
            'express_pay_method_code' => '',
            'settlement_method'       => '',
            'settlement_method_code'  => '',
            'documents_type'          => '',
            'operator_zdr_name'       => '',
        ];
        $return_data = [];
        $wh_codes = array_column(config('config.warehouse_code'),null,'value');
        foreach ($order_data as &$v) {
            //赛选科技公司订单
            if ($corpkey[$v['sub_order_no']] != '001') continue;
            if ($v['order_type'] == 11) {
                $periods_set_data = $product_key[$v['period']];
            } else {
                $periods_set_data = $product_key[$v['package_id']];
            }
            $payment_amount = preg_match('/^VHG/', $v['sub_order_no']) ? Db::name('sub_order_extend')->where('sub_order_no', $v['sub_order_no'])->value('erp_push_amount') : $v['payment_amount'];
            $order_qty      = $v['order_qty'];

            $items_info = [];
            foreach ($periods_set_data as $k => $value) {
                $product                     = $product_data_key[$value['product_id']];
                $periods_set_product_num_arr = array_column($periods_set_data, 'nums');
                $periods_set_product_num     = 0;
                foreach ($periods_set_product_num_arr as $pvalue) {
                    $periods_set_product_num += $pvalue;
                }
                //                $tax_unit_price = round($payment_amount/$order_qty/$periods_set_product_num,2);
                $tax_unit_price = floor($payment_amount / $order_qty / $periods_set_product_num * 100) / 100;
                $items_detail   = [
                    'key'             => $k,
                    'bar_code'        => $product['bar_code'] ?? '',
                    'short_code'      => $product['short_code'] ? $product['short_code'] : '',
                    'nums'            => $value['nums'] * $order_qty,
                    'tax_unit_price'  => $tax_unit_price,
                    'tax_total_price' => $tax_unit_price * $order_qty * $value['nums'],
                    'product_name'    => $product['product_name'] ?? "",
                    'unit'            => $product['product_unit'] ?? "",
                    'year'            => $product['year'] ?? "",
                    'capacity'        => $product['capacity'] ?? "",
                    'tax_rate'        => "13%",
                    'is_gift'         => $value['isGift'] ?? 0,
                ];
                array_push($items_info, $items_detail);
            }
            $filldata['items_info'] = $items_info;
            $filldata['corp'] = $corpkey[$v['sub_order_no']];
            $filldata['warehouse'] = $wh_codes[$v['warehouse_code']]['label'] ?? '';
            $v                      = array_merge($v, $filldata);
            array_push($return_data, $v);
        }
        return $return_data;
    }

    /**
     * es通过简码查询商品名称
     * @param $short_code
     * @return array|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function shortSearchProductBatch($ids)
    {
        $es   = new ElasticSearchService();
        $data = $es->getDocumentList(['index' => ['panshi.products'], 'terms' => [['id' => $ids]], 'source' => ['id', 'cn_product_name', 'bar_code', 'short_code', 'capacity', 'product_unit_name', 'products_years'], 'limit' => 100]);

        $list = $data['data'];
        if (count($list)) {

            foreach ($list as &$v) {
                $v["capacity"]     = isset($v['capacity']) ? $v['capacity'] : '';
                $v["product_name"] = isset($v['cn_product_name']) ? $v['cn_product_name'] : '';
                $v["product_unit"] = isset($v['product_unit_name']) ? $v['product_unit_name'] : '瓶';
                $v["year"]         = isset($v['products_years']) ? $v['products_years'] : '';
                $v["bar_code"]     = isset($v['bar_code']) ? $v['bar_code'] : '';
            }
        }
        return $list;
    }


    /**
     * @param $order 订单号
     * @param $sale_bill_type 订单类型
     * @param $money 退款金额
     * @return bool true 可以退款 false不可以退款
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function checkreturnmoney($order, $sale_bill_type, $money)
    {
        $SalesReturn = SalesReturnModel::where('sub_order_no', $order)->where('dingtalk_status', '<>', 3)->select()->toArray();

        $countmoney = 0; //已经支付了的钱
        foreach ($SalesReturn as $v) {
            $countmoney += $v['return_amount'];
        }

        if ($sale_bill_type == 1) { //酒云线上

            $es    = new ElasticSearchService();
            $where = ['index' => ['orders'], 'terms' => [['_id' => explode(',', $order)]], 'source' => ['sub_order_no', 'payment_amount']];
            $data  = $es->getDocumentList($where);
            if (!$data || $data['total']['value'] < 1) throw new ValidateException('原订单未查询到，请检查！');
            if (preg_match('/^VHG/', $order)) {
                $erp_push_amount = Db::name('sub_order_extend')->where('sub_order_no', $order)->value('erp_push_amount');
                $payment_amount  = $erp_push_amount;
            } else {
                $payment_amount = $data['data'][0]['payment_amount'];
            }
        } else if ($sale_bill_type == 2) { //线下
            $where          = ['sub_order_no' => $order];
            $payment_amount = Db::name("offline_order")->where($where)->value('payment_amount');
            if (!$payment_amount) throw new ValidateException('原订单未查询到，请检查！');
        } else if ($sale_bill_type == 3) { //三方
            $where          = ['sub_order_no' => $order];
            $payment_amount = Db::name("tripartite_order")->where($where)->value('payment_amount');
            if (!$payment_amount) throw new ValidateException('原订单未查询到，请检查！');
        }

        if (bccomp($payment_amount - $countmoney, $money, 10) >= 0) return true;
        return false;
    }


    /**
     * 审批导出 回调
     * @param $params
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function export($params)
    {
        Log::write('销售退货单导出审批回调:' . json_encode($params));

        //解析数据
        $blackdata  = $params['process_instance']['form_component_values'];
        $start_time = strtotime(date("y-m-d 00:00:00", time()));
        $end_time   = strtotime(date("y-m-d 23:59:59", time()));
        foreach ($blackdata as $v) {
            if ($v['name'] == '开始日期') $start_time = strtotime($v['value'] . " 00:00:00");
            if ($v['name'] == '结束日期') $end_time = strtotime($v['value'] . " 23:59:59");
        }
        $userid = $params['process_instance']['originator_userid'];
        $where  = [
            ["bill_date", ">=", $start_time],
            ["bill_date", "<=", $end_time]
        ];
        //拒绝
        if ($params['process_instance']['result'] == 'refuse') {
            $msgData = array(
                'content' => "您申请的销售退货导出，已被拒绝。详情请查看审批.",
                'userid'  => $userid,
                'msgtype' => 'text',
                'agentid' => 0,
            );
            httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            throw new ValidateException("您申请的销售退货导出，已被拒绝。详情请查看审批.");
        }

        //同意
        if ($params['process_instance']['result'] == 'agree') {
            $push_t_status = [0=>'未推送', 1=>'推送成功', 2=>'推送失败', 3=>'不推送'];
            $result              = Db::table('vh_sales_return')
                ->field('bill_no,sub_order_no,push_t_status,return_courier_no,bill_date,corp,department,clerk,customer,operator_name,warehouse,order_amount,return_amount,dingtalk_status,approval_time,detail_json,remarks')
                ->where($where)
                ->select()->toArray();
            $resultData          = [];
            $companys = Db::name('collecting_company')->column('name', 'corp');
            $dingtalk_status_arr = [1 => "审核中", 2 => "审核通过", 3 => "已拒绝"];
            foreach ($result as $v) {

                $detail_json = json_decode($v['detail_json'], true);
                unset($v['detail_json']);
                $v['push_t_status'] = $push_t_status[$v['push_t_status']] ?? '';
                $v['bill_date']       = date('Y-m-d H:i:s', $v['bill_date']);
                $v['approval_time']   = date('Y-m-d H:i:s', $v['approval_time']);
                $v['dingtalk_status'] = $dingtalk_status_arr[$v['dingtalk_status']];
                $v['corp'] = $companys[$v['corp']] ?? '';
                foreach ($detail_json as $k => $vv) {
                    $v['product_name']    = $vv['product_name'];
                    $v['short_code']      = $vv['short_code'];
                    $v['nums']            = $vv['nums'];
                    $v['tax_unit_price']  = $vv['tax_unit_price'];
                    $v['tax_total_price'] = $vv['tax_total_price'];
                    $v['is_gift']         = $vv['is_gift'] == 0 ? "不是" : "是";
                    array_push($resultData, array_values($v));
                }
            }

            $firstrow = ['单据编号', '原销售单号', 'ERP推送状态', '退回运单号', "单据时间", '公司', '部门', '业务员', '客户', '退货申请人', '退回仓库', '订单金额', '退货金额', '审批状态', '审批时间', "备注", "存货", "简码", "数量", "单价", "金额", "赠品",];

            [$filePath, $filename] = self::excleExport($firstrow, $resultData);
            $media_id = weixinUpload($filePath, $filename);
            $msgData  = array(
                'content' => $media_id,
                'userid'  => $userid,
                'msgtype' => 'file',
                'agentid' => 0,
            );
            $aa       = httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
        }
    }

    /**
     * 存储数据到本地excel文件
     * @param $firstrow
     * @param $data
     * @return array
     */
    public static function excleExport($firstrow, $data)
    {
        $path       = public_path() . "storage";
        $config     = [
            'path' => $path
        ];
        $excel      = new \Vtiful\Kernel\Excel($config);
        $filename   = '销售退货' . date("Y-m-d") . '.xlsx';
        $fileObject = $excel->fileName($filename, '销售退货数据');

        $fileObject->header($firstrow)->data($data);
        $filePath = $fileObject->output();
        return [$filePath, $filename];
    }

    /**
     * 重跑数据
     * @return void
     */
    public static function rerundata()
    {
        $result = Db::table('vh_sales_return')
            //            ->field('bill_no,sub_order_no,bill_date,customer,operator_name,warehouse,order_amount,return_amount,dingtalk_status,update_time,detail_json')
            //            ->where($where)
            //            ->limit(0,1)
            ->select()->toArray();
        foreach ($result as $v) {
            $approval_process = json_decode($v['approval_process'], true);
            $approval_time    = strtotime($approval_process[count($approval_process) - 1]['check_time']);
            if ($v['dingtalk_status'] == 2 || $v['dingtalk_status'] == 3) {
                $result = Db::table('vh_sales_return')->where('id', $v['id'])->save(['approval_time' => $approval_time]);
                if (!$result) echo $v['id'] . PHP_EOL;
            }
        }
    }

    /**
     * Description:批量导入销售退货
     * Author: zrc
     * Date: 2023/6/29
     * Time: 16:58
     * @param $params
     * @return \think\response\Json
     * @throws \Exception
     */
    public function salesReturnBatchImport($params)
    {
        if (strpos($params['file'], '.xlsx') !== false) {
            $path = env('ALIURL') . urldecode($params['file']);
            //$path = 'D:\线上-销售退货批量导入-单个.xlsx';
            try {
                #下载文件
                $local_path = download_image($path, 'xlsx');
                #解析文件
                $eData = readExcelData($local_path);
                if ($eData['error_code'] != 0) $this->throwError('excel解析失败');
                if (count($eData['data']) == 0) $this->throwError('未获取到excel内容');
                $excelData = $eData['data'];
                unset($excelData[0]);
                $items          = [];
                $checkData      = [];
                $sale_bill_type = 0;
                //数据解析验证
                foreach ($excelData as $key => $val) {
                    $count = $key + 1;
                    if (empty($val[0]) && empty($val[1]) && empty($val[4]) && empty($val[6]) && empty($val[7]) && empty($val[8]) && empty($val[9]) && empty($val[10])) {
                        break;
                    }
                    if (empty($val[0]) || empty($val[1]) || empty($val[4]) || empty($val[6]) || empty($val[7]) || empty($val[8]) || empty($val[9]) || !isset($val[10])) {
                        $this->throwError('第' . $count . '行数据错误：请检查必填项！', ErrorCode::PARAM_ERROR);
                    }
                    if ($key == 1) {
                        $checkData = array(
                            'sale_bill_type'       => str_replace('\n', '', trim($val[0])),
                            'bill_date'            => str_replace('\n', '', trim($val[1])),
                            'customer'             => str_replace('\n', '', trim($val[2])),
                            'customer_code'        => '',
                            'settle_customer'      => str_replace('\n', '', trim($val[3])),
                            'settle_customer_code' => '',
                            'department'           => str_replace('\n', '', trim($val[4])),
                            'department_code'      => '',
                            'clerk'                => str_replace('\n', '', trim($val[5])),
                            'clerk_code'           => '',
                            'warehouse'            => str_replace('\n', '', trim($val[6])),
                            'warehouse_code'       => '',
                            'sub_order_no'         => str_replace('\n', '', trim($val[7])),
                            'short_code'           => str_replace('\n', '', trim($val[8])),
                            'nums'                 => str_replace('\n', '', trim($val[9])),
                            'tax_total_price'      => str_replace('\n', '', trim($val[10])),
                        );
                        //单据类型验证
                        switch ($checkData['sale_bill_type']) {
                            case '酒云线上':
                                $sale_bill_type = 1;
                                break;
                            case '线下销售':
                                $sale_bill_type = 2;
                                break;
                            case '三方线上':
                                $sale_bill_type = 3;
                                break;
                            default:
                                $this->throwError('第' . $count . '行数据错误：销售单据类型错误');
                                break;
                        }
                        //仓库验证
                        $warehouseUseOptions = curlRequest(env('ITEM.ERP_PREPARED_URL') . '/prepared/v3/prepareds/warehouseUseOptions', ['Name' => $checkData['warehouse']], ['vinehoo-uid:' . $params['admin_id']], 'GET');
                        if (!isset($warehouseUseOptions['error_code'])) $this->throwError('第' . $count . '行数据错误：获取仓库信息失败！制单人管理模块访问异常');
                        if ($warehouseUseOptions['error_code'] != 0) $this->throwError('第' . $count . '行数据错误：获取仓库信息失败！' . $warehouseUseOptions['error_msg']);
                        if (!isset($warehouseUseOptions['data'][0]['Code'])) $this->throwError('第' . $count . '行数据错误：未获取到仓库编码');
                        $checkData['warehouse_code'] = $warehouseUseOptions['data'][0]['Code'];
                        if ($sale_bill_type == 2) { //线下销售数据验证客户、结算客户、部门、业务员
                            $field        = 'customer,customer_code,settle_customer,settle_customer_code,department,department_code,clerk,clerk_code';
                            $offlineOrder = Db::name('offline_order')->field($field)->where(['sub_order_no' => $checkData['sub_order_no']])->find();
                            if (empty($offlineOrder)) $this->throwError('第' . $count . '行数据错误：未获取到原单据信息');
                            if ($checkData['customer'] != $offlineOrder['customer']) {
                                $this->throwError('第' . $count . '行数据错误：请填写与原单据相对应的客户');
                            }
                            if ($checkData['settle_customer'] != $offlineOrder['settle_customer']) {
                                $this->throwError('第' . $count . '行数据错误：请填写与原单据相对应的结算客户');
                            }
                            if ($checkData['department'] != $offlineOrder['department']) {
                                $this->throwError('第' . $count . '行数据错误：请填写与原单据相对应的部门');
                            }
                            if ($checkData['clerk'] != $offlineOrder['clerk']) {
                                $this->throwError('第' . $count . '行数据错误：请填写与原单据相对应的业务员');
                            }
                            $checkData['customer_code']        = $offlineOrder['customer_code'];
                            $checkData['settle_customer_code'] = $offlineOrder['settle_customer_code'];
                            $checkData['department_code']      = $offlineOrder['department_code'];
                            $checkData['clerk_code']           = $offlineOrder['clerk_code'];
                        } else { //其他类型验证部门
                            $departmentPushData = array(
                                'name'  => $checkData['department'],
                                'page'  => 1,
                                'limit' => 10,
                            );
                            $departmentList     = curlRequest(env('ITEM.SUPPLYCHAIN_URL') . '/supplychain/v3/department/list', $departmentPushData, [], 'GET');
                            if (!isset($departmentList['error_code'])) $this->throwError('第' . $count . '行数据错误：获取部门信息失败！供应链系统访问异常');
                            if ($departmentList['error_code'] != 0) $this->throwError('第' . $count . '行数据错误：获取部门信息失败！' . $departmentList['error_msg']);
                            if (!isset($departmentList['data']['list'][0]['dept_code'])) $this->throwError('第' . $count . '行数据错误：未获取到部门编码');
                            $checkData['department_code'] = $departmentList['data']['list'][0]['dept_code'];
                            //验证客户
                            if (!empty($checkData['customer'])) {
                                $customerPushData = array(
                                    'name'  => $checkData['customer'],
                                    'page'  => 1,
                                    'limit' => 10,
                                );
                                $customerList     = curlRequest(env('ITEM.SUPPLYCHAIN_URL') . '/supplychain/v3/partnerentity/list', $customerPushData, [], 'GET');
                                if (!isset($customerList['error_code'])) $this->throwError('第' . $count . '行数据错误：获取客户信息失败！供应链系统访问异常');
                                if ($customerList['error_code'] != 0) $this->throwError('第' . $count . '行数据错误：获取客户信息失败！' . $customerList['error_msg']);
                                if (!isset($customerList['data']['list'][0]['code'])) $this->throwError('第' . $count . '行数据错误：未获取到客户编码');
                                $checkData['customer_code'] = $customerList['data']['list'][0]['code'];
                            }
                            //验证结算客户
                            if (!empty($checkData['settle_customer'])) {
                                $settleCustomerPushData = array(
                                    'name'  => $checkData['customer'],
                                    'page'  => 1,
                                    'limit' => 10,
                                );
                                $settleCustomerList     = curlRequest(env('ITEM.SUPPLYCHAIN_URL') . '/supplychain/v3/partnerentity/list', $settleCustomerPushData, [], 'GET');
                                if (!isset($settleCustomerList['error_code'])) $this->throwError('第' . $count . '行数据错误：获取结算客户信息失败！供应链系统访问异常');
                                if ($settleCustomerList['error_code'] != 0) $this->throwError('第' . $count . '行数据错误：获取结算客户信息失败！' . $settleCustomerList['error_msg']);
                                if (!isset($settleCustomerList['data']['list'][0]['code'])) $this->throwError('第' . $count . '行数据错误：未获取到结算客户编码');
                                $checkData['settle_customer_code'] = $settleCustomerList['data']['list'][0]['code'];
                            }
                            //验证业务员
                            if (!empty($checkData['clerk'])) {
                                $salesmanUseOptions = curlRequest(env('ITEM.ERP_PREPARED_URL') . '/prepared/v3/prepareds/salesmanUseOptions', ['Name' => $checkData['clerk']], ['vinehoo-uid:' . $params['admin_id']], 'GET');
                                if (!isset($salesmanUseOptions['error_code'])) $this->throwError('第' . $count . '行数据错误：获取业务员信息失败！制单人管理模块访问异常');
                                if ($salesmanUseOptions['error_code'] != 0) $this->throwError('第' . $count . '行数据错误：获取业务员信息失败！' . $salesmanUseOptions['error_msg']);
                                if (!isset($salesmanUseOptions['data'][0]['Code'])) $this->throwError('第' . $count . '行数据错误：未获取到业务员编码');
                                $checkData['clerk_code'] = $salesmanUseOptions['data'][0]['Code'];
                            }
                        }
                    }
                    if ($checkData['sale_bill_type'] != $val[0]) $this->throwError('第' . $count . '行数据错误：请保持单据类型一致！', ErrorCode::PARAM_ERROR);
                    if ($checkData['bill_date'] != $val[1]) $this->throwError('第' . $count . '行数据错误：请保持单据日期一致！', ErrorCode::PARAM_ERROR);
                    if ($checkData['customer'] != $val[2]) $this->throwError('第' . $count . '行数据错误：请保持客户一致！', ErrorCode::PARAM_ERROR);
                    if ($checkData['settle_customer'] != $val[3]) $this->throwError('第' . $count . '行数据错误：请保持结算客户一致！', ErrorCode::PARAM_ERROR);
                    if ($checkData['department'] != $val[4]) $this->throwError('第' . $count . '行数据错误：请保持部门一致！', ErrorCode::PARAM_ERROR);
                    if ($checkData['clerk'] != $val[5]) $this->throwError('第' . $count . '行数据错误：请保持业务员一致！', ErrorCode::PARAM_ERROR);
                    if ($checkData['warehouse'] != $val[6]) $this->throwError('第' . $count . '行数据错误：请保持退回仓库一致！', ErrorCode::PARAM_ERROR);
                    $items[$val[7]][] = array(
                        'sub_order_no'    => $val[7],
                        'short_code'      => $val[8],
                        'nums'            => $val[9],
                        'tax_total_price' => $val[10],
                    );
                }
                if (empty($items)) $this->throwError('未获取到excel有效导入数据');
                //获取企微用户信息
                $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
                if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) $this->throwError('发起企业微信批失败：未获取到企业微信用户信息');
                $userid = $userInfo['data'][$params['admin_id']]['userid']; //企业微信userid
                //创建销售退货数据组装
                $billData = array(
                    'sale_bill_type'       => $sale_bill_type,
                    'bill_date'            => $checkData['bill_date'],
                    'customer'             => $checkData['customer'],
                    'customer_code'        => $checkData['customer_code'],
                    'settle_customer'      => $checkData['settle_customer'],
                    'settle_customer_code' => $checkData['settle_customer_code'],
                    'department'           => $checkData['department'],
                    'department_code'      => $checkData['department_code'],
                    'clerk'                => $checkData['clerk'],
                    'clerk_code'           => $checkData['clerk_code'],
                    'warehouse'            => $checkData['warehouse'],
                    'warehouse_code'       => $checkData['warehouse_code'],
                    'userid'               => $userid,
                    'admin_id'             => $params['admin_id'],
                    'operator_name'        => $params['operator_name'],
                    'items'                => $items
                );
                //var_dump(json_encode($billData, JSON_UNESCAPED_UNICODE));exit;
                $pushData = array(
                    'namespace' => 'orders',
                    'key'       => 'salesReturnBatchImport_' . time(),
                    'data'      => base64_encode(json_encode($billData, JSON_UNESCAPED_UNICODE)),
                    'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/salesreturn/salesReturnBatchImportDeal',
                    'timeout'   => '10s',
                );
                $result   = curlRequest(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', json_encode($pushData));
                if (!isset($result['error_code'])) $this->throwError('导入失败：秒级计划任务模块访问异常');
                if ($result['error_code'] != 0) $this->throwError('导入失败：' . $result['error_msg']);
            } catch (\Exception $e) {
                @unlink($local_path);
                $this->throwError($e->getMessage());
            }
        } else {
            $this->throwError('请用模板文件制作excel进行上传');
        }
        return $this->success((object)[], "提交成功，导入结果处理中，请稍后刷新列表查看结果");
    }

    /**
     * Description:销售退货批量导入异步处理
     * Author: zrc
     * Date: 2023/6/30
     * Time: 17:57
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function salesReturnBatchImportDeal($params)
    {
        $itemsArr            = [];
        $total_order_amount  = 0;
        $total_return_amount = 0;
        Db::startTrans();
        try {
            $sub_order_no_arr = [];
            $short_codes_arr  = [];
            $now = time();
            $first_bill_no = null;
            $inster_items = [];
            /*            foreach ($params['items'] as $k => $v) {
                if ($i + 1 == count($params['items'])) {
                    $sub_order_no_str .= $k;
                    $short_codes_str  = implode(',', array_unique(array_column($v, 'short_code')));
                } else {
                    $sub_order_no_str .= $k . ',';
                    $short_codes_str  = implode(',', array_unique(array_column($v, 'short_code'))) . ',';
                }
                $i++;
            }*/

            foreach ($params['items'] as $key => $val) {
                $i_short_codes  = array_unique(array_column($val, 'short_code'));
                $c_items_arr = [];
                $c_total_order_amount = 0;
                $c_total_return_amount = 0;

                if (empty($first_bill_no)) {
                    $item_bill_no = self::getBillNo();
                    $first_bill_no = $item_bill_no;
                } else {
                    $inc = intval(substr($item_bill_no, -4)) + 1;
                    $item_bill_no = substr($item_bill_no, 0, (strlen($item_bill_no) - 4)) . str_pad($inc, 4, 0, STR_PAD_LEFT);
                }

                $totalRefundMoney = array_sum(array_column($val, 'tax_total_price'));
                $checkResult      = $this->checkRefundMoneyNums($params['sale_bill_type'], $key, $totalRefundMoney, $val);
                foreach ($checkResult as $vv) {
                    $total_order_amount  += $vv['payment_amount'];
                    $c_total_order_amount  += $vv['payment_amount'];
                    $total_return_amount += $vv['tax_total_price'];
                    $c_total_return_amount += $vv['tax_total_price'];

                    $product_item_info = array(
                        'key'             => $vv['key'],
                        'short_code'      => $vv['short_code'],
                        'bar_code'        => $vv['bar_code'],
                        'nums'            => $vv['nums'],
                        'tax_unit_price'  => round($vv['tax_total_price'] / $vv['nums'], 2),
                        'tax_total_price' => $vv['tax_total_price'],
                        'product_name'    => $vv['product_name'],
                        'unit'            => $vv['unit'],
                        'year'            => $vv['year'],
                        'capacity'        => $vv['capacity'],
                        'tax_rate'        => '13%',
                        'is_gift'         => $vv['is_gift'],
                        'sub_order_no'    => $vv['sub_order_no'],
                    );
                    $itemsArr[] = $product_item_info;
                    $c_items_arr[] = $product_item_info;
                }
                $sub_order_no_arr[] = $key;
                $short_codes_arr = array_values(array_unique(array_merge($short_codes_arr, $i_short_codes)));
                $inster_items[] = [
                    'group_bill_no'        => $first_bill_no,
                    'sub_order_no'         => $key,
                    'bill_no'              => $item_bill_no,
                    'bill_date'            => strtotime($params['bill_date']),
                    'customer'             => $params['customer'],
                    'customer_code'        => $params['customer_code'],
                    'settle_customer'      => $params['settle_customer'],
                    'settle_customer_code' => $params['settle_customer_code'],
                    'department'           => $params['department'],
                    'department_code'      => $params['department_code'],
                    'clerk'                => $params['clerk'],
                    'clerk_code'           => $params['clerk_code'],
                    'warehouse'            => $params['warehouse'],
                    'warehouse_code'       => $params['warehouse_code'],
                    'sale_bill_type'       => $params['sale_bill_type'],
                    'corp'                 => '001',
                    'detail_json'          => json_encode($c_items_arr, JSON_UNESCAPED_UNICODE),
                    'short_codes'          => implode(',', $i_short_codes),
                    'create_time'          => $now,
                    'operator_id'          => $params['admin_id'],
                    'operator_name'        => $params['operator_name'],
                    'order_amount'         => $c_total_order_amount,
                    'return_amount'        => $c_total_return_amount,
                    'operator'             => $params['admin_id'],
                    'operator_zdr_name'    => $params['operator_name'],
                    'dingtalk_uid'         => $params['userid'],
                ];
            }
            //销售退货数据组装
            $createData = array(
                'sub_order_no'         => implode(',', $sub_order_no_arr),
                'bill_no'              => $first_bill_no,
                'bill_date'            => strtotime($params['bill_date']),
                'customer'             => $params['customer'],
                'customer_code'        => $params['customer_code'],
                'settle_customer'      => $params['settle_customer'],
                'settle_customer_code' => $params['settle_customer_code'],
                'department'           => $params['department'],
                'department_code'      => $params['department_code'],
                'clerk'                => $params['clerk'],
                'clerk_code'           => $params['clerk_code'],
                'warehouse'            => $params['warehouse'],
                'warehouse_code'       => $params['warehouse_code'],
                'sale_bill_type'       => $params['sale_bill_type'],
                'corp'                 => '001',
                'detail_json'          => json_encode($itemsArr, JSON_UNESCAPED_UNICODE),
                'short_codes'          => implode(',', $short_codes_arr),
                'create_time'          => $now,
                'operator_id'          => $params['admin_id'],
                'operator_name'        => $params['operator_name'],
                'order_amount'         => $total_order_amount,
                'return_amount'        => $total_return_amount,
                'operator'             => $params['admin_id'],
                'operator_zdr_name'    => $params['operator_name'],
                'dingtalk_uid'         => $params['userid'],
            );
            $result     = Db::name('sales_return')->insertAll($inster_items);
            if (empty($result)) $this->throwError('销售退货数据写入失败');
            //创建审批
            $weChatService = new WeChatService();
            $weChatService->salesRetrunCreateVerify($createData, $params['userid']);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $weChatService = new WeChatService();
            $weChatService->weChatSendText($params['userid'], '销售退货批量导入处理失败：' . $e->getMessage());
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:验证销售退货订单可退货金额、可退货数量
     * Author: zrc
     * Date: 2023/7/7
     * Time: 15:53
     * @param $sale_bill_type
     * @param $sub_order_no
     * @param $total_refund_money
     * @param $items
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function checkRefundMoneyNums($sale_bill_type, $sub_order_no, $total_refund_money, $items)
    {
        switch ($sale_bill_type) {
            case 1:
                //查询原单
                $es   = new ElasticSearchService();
                $arr  = array(
                    'index'  => ['orders'],
                    'match'  => [['sub_order_no.keyword' => $sub_order_no]],
                    'limit'  => 1,
                    'source' => ['sub_order_no', 'period', 'package_id', 'payment_amount', 'order_qty', 'order_type']
                );
                $data = $es->getDocumentList($arr);
                if (!isset($data['data'][0])) $this->throwError($sub_order_no . '未获取到订单信息');
                $orderInfo = $data['data'][0];
                //获取订单公司主体
                $getOrderCorp = $this->getcorpbyorderno([['sub_order_no' => $sub_order_no, 'order_type' => 1]]);
                if (!isset($getOrderCorp[0]['corp'])) $this->throwError($sub_order_no . '未获取到订单所属公司主体');
                if (!in_array($getOrderCorp[0]['corp'], ['001','515','003','031','032'])) $this->throwError($sub_order_no . '仅处理科技公司,兔子星球,木兰朵销售退货，其余公司请在通过其它渠道处理');
                //查询订单产品信息
                $esPeriodsSetData = $es->getDocumentList(['index' => ['periods_set'], 'match' => [['id' => $orderInfo['package_id']]], 'source' => ['associated_products'], 'limit' => 1]);
                if (!isset($esPeriodsSetData['data'][0])) $this->throwError($sub_order_no . '未获取到商品套餐信息');
                $associated_products = json_decode($esPeriodsSetData['data'][0]['associated_products'], true);
                $orderItem           = [];
                foreach ($associated_products as &$val) {
                    $short_code  = Db::table('vh_commodities.vh_periods_product_inventory')->where(['period' => $orderInfo['period'], 'product_id' => $val['product_id']])->value('short_code');
                    $orderItem[] = array(
                        'short_code' => $short_code,
                        'nums'       => $val['nums'] * $orderInfo['order_qty'],
                    );
                }
                //查询已退货信息
                $residue_payment_amount = $orderInfo['payment_amount'];
                $refundOrderInfo        = Db::name('sales_return')->field('detail_json,return_amount')->where([['sub_order_no', '=', $sub_order_no], ['dingtalk_status', 'in', [1, 2]]])->select()->toArray();
                if (!empty($refundOrderInfo)) {
                    $refundItem = [];
                    foreach ($refundOrderInfo as &$value) {
                        $residue_payment_amount = $residue_payment_amount - $value['return_amount'];
                        $detail_json            = json_decode($value['detail_json'], true);
                        foreach ($detail_json as &$v) {
                            $refundItem[] = array(
                                'short_code' => $v['short_code'],
                                'nums'       => $v['nums'],
                            );
                        }
                    }
                    foreach ($orderItem as $kk => $vv) {
                        foreach ($refundItem as &$va) {
                            if ($vv['short_code'] == $va['short_code']) {
                                $orderItem[$kk]['nums'] = $vv['nums'] - $va['nums'];
                            }
                        }
                    }
                }
                if (bcsub($residue_payment_amount, $total_refund_money, 2) < 0) $this->throwError($sub_order_no . '退货金额大于订单可退货金额，请核实后重试！');
                $short_code_arr = array_column($orderItem, 'short_code');
                foreach ($items as $k => $v) {
                    if (!in_array($v['short_code'], $short_code_arr)) $this->throwError($sub_order_no . $v['short_code'] . '该简码不属于当前订单,请核实后重新提交');
                    foreach ($orderItem as &$vv) {
                        if ($vv['short_code'] == $v['short_code']) {
                            $difference = $vv['nums'] - $v['nums'];
                            if ($difference < 0) $this->throwError($sub_order_no . $v['short_code'] . '可退货数量为' . $vv['nums'] . ',请核实后重新提交');
                        }
                    }
                    //查询磐石es数据
                    $panshiProducts = $es->getDocumentList(['index' => ['panshi.products'], 'match' => [['short_code' => $v['short_code']]], 'source' => ['cn_product_name', 'bar_code', 'short_code', 'capacity', 'product_unit_name', 'products_years'], 'limit' => 1]);
                    if (!isset($panshiProducts['data'][0])) $this->throwError($sub_order_no . '未获取到产品磐石信息');
                    if ($k == 0) {
                        $items[$k]['payment_amount'] = $orderInfo['payment_amount'];
                    } else {
                        $items[$k]['payment_amount'] = 0;
                    }
                    $items[$k]['key']          = $k;
                    $items[$k]['is_gift']      = 0;
                    $items[$k]['capacity']     = isset($panshiProducts['data'][0]['capacity']) ? $panshiProducts['data'][0]['capacity'] : '';
                    $items[$k]['product_name'] = isset($panshiProducts['data'][0]['cn_product_name']) ? $panshiProducts['data'][0]['cn_product_name'] : '';
                    $items[$k]['unit']         = isset($data['data'][0]['product_unit_name']) ? $data['data'][0]['product_unit_name'] : '瓶';
                    $items[$k]['year']         = isset($data['data'][0]['products_years']) ? $data['data'][0]['products_years'] : '';
                    $items[$k]['bar_code']     = isset($data['data'][0]['bar_code']) ? $data['data'][0]['bar_code'] : '';
                }
                break;
            case 2:
                $orderInfo = Db::name('offline_order')->where(['sub_order_no' => $sub_order_no])->find();
                if (empty($orderInfo)) $this->throwError($sub_order_no . '未获取到订单信息');
                //获取订单公司主体
                $getOrderCorp = $this->getcorpbyorderno([['sub_order_no' => $sub_order_no, 'order_type' => 2]]);
                if (!isset($getOrderCorp[0]['corp'])) $this->throwError($sub_order_no . '未获取到订单所属公司主体');
                if (!in_array($getOrderCorp[0]['corp'], ['001','515','003','031','032'])) $this->throwError($sub_order_no . '仅处理科技公司,兔子星球,木兰朵销售退货，其余公司请在通过其它渠道处理');
                $orderItem = [];
                $goodsInfo = explode(',', $orderInfo['items_info']);
                foreach ($goodsInfo as $k => $v) {
                    $goods                         = explode('*', $v);
                    $orderItem[$k]['key']          = $k;
                    $orderItem[$k]['bar_code']     = isset($goods[0]) ? $goods[0] : '';
                    $orderItem[$k]['short_code']   = isset($goods[1]) ? $goods[1] : '';
                    $orderItem[$k]['nums']         = isset($goods[2]) ? intval($goods[2]) : 0;
                    $orderItem[$k]['is_gift']      = isset($goods[4]) ? intval($goods[4]) : 0;
                    $orderItem[$k]['product_name'] = isset($goods[6]) ? $goods[6] : '';
                    $orderItem[$k]['year']         = isset($goods[7]) ? $goods[7] : '';
                    $orderItem[$k]['unit']         = isset($goods[8]) ? $goods[8] : '';
                    $orderItem[$k]['capacity']     = isset($goods[11]) ? $goods[11] : '';
                }
                //查询已退货信息
                $residue_payment_amount = $orderInfo['payment_amount'];
                $refundOrderInfo        = Db::name('sales_return')->field('detail_json,return_amount')->where([['sub_order_no', '=', $sub_order_no], ['dingtalk_status', 'in', [1, 2]]])->select()->toArray();
                if (!empty($refundOrderInfo)) {
                    $refundItem = [];
                    foreach ($refundOrderInfo as &$value) {
                        $residue_payment_amount = $residue_payment_amount - $value['return_amount'];
                        $detail_json            = json_decode($value['detail_json'], true);
                        foreach ($detail_json as &$v) {
                            $refundItem[] = array(
                                'short_code' => $v['short_code'],
                                'nums'       => $v['nums'],
                            );
                        }
                    }
                    foreach ($orderItem as $kk => $vv) {
                        foreach ($refundItem as &$va) {
                            if ($vv['short_code'] == $va['short_code']) {
                                $orderItem[$kk]['nums'] = $vv['nums'] - $va['nums'];
                            }
                        }
                    }
                }
                if (bcsub($residue_payment_amount, $total_refund_money, 2) < 0) $this->throwError($sub_order_no . '退货金额大于订单可退货金额，请核实后重试！');
                $short_code_arr = array_column($orderItem, 'short_code');
                foreach ($items as $k => $v) {
                    if (!in_array($v['short_code'], $short_code_arr)) $this->throwError($sub_order_no . $v['short_code'] . '该简码不属于当前订单,请核实后重新提交');
                    foreach ($orderItem as &$vv) {
                        if ($vv['short_code'] == $v['short_code']) {
                            $difference = $vv['nums'] - $v['nums'];
                            if ($difference < 0) $this->throwError($sub_order_no . $v['short_code'] . '可退货数量为' . $vv['nums'] . ',请核实后重新提交');
                            if ($k == 0) {
                                $items[$k]['payment_amount'] = $orderInfo['payment_amount'];
                            } else {
                                $items[$k]['payment_amount'] = 0;
                            }
                            $items[$k]['key']          = $vv['key'];
                            $items[$k]['is_gift']      = $vv['is_gift'];
                            $items[$k]['capacity']     = $vv['capacity'];
                            $items[$k]['product_name'] = $vv['product_name'];
                            $items[$k]['unit']         = $vv['unit'];
                            $items[$k]['year']         = $vv['year'];
                            $items[$k]['bar_code']     = $vv['bar_code'];
                        }
                    }
                }
                break;
            case 3:
                $orderInfo = Db::name('tripartite_order')->where(['sub_order_no' => $sub_order_no])->find();
                if (empty($orderInfo)) $this->throwError($sub_order_no . '未获取到订单信息');
                //获取订单公司主体
                $getOrderCorp = $this->getcorpbyorderno([['sub_order_no' => $sub_order_no, 'order_type' => 3]]);
                if (!isset($getOrderCorp[0]['corp'])) $this->throwError($sub_order_no . '未获取到订单所属公司主体');
                if (!in_array($getOrderCorp[0]['corp'], ['001','515','003','031','032'])) $this->throwError($sub_order_no . '仅处理科技公司,兔子星球,木兰朵销售退货，其余公司请在通过其它渠道处理');
                $orderItem = [];
                $goodsInfo = explode(',', $orderInfo['items_info']);
                foreach ($goodsInfo as $k => $v) {
                    $goods                       = explode('*', $v);
                    $orderItem[$k]['key']        = $k;
                    $orderItem[$k]['short_code'] = isset($goods[0]) ? $goods[0] : '';
                    $orderItem[$k]['nums']       = isset($goods[1]) ? $goods[1] : 0;
                    $orderItem[$k]['is_gift']    = $goods[2] > 0 ? 0 : 1;
                }
                //查询已退货信息
                $residue_payment_amount = $orderInfo['payment_amount'];
                $refundOrderInfo        = Db::name('sales_return')->field('detail_json,return_amount')->where([['sub_order_no', '=', $sub_order_no], ['dingtalk_status', 'in', [1, 2]]])->select()->toArray();
                if (!empty($refundOrderInfo)) {
                    $refundItem = [];
                    foreach ($refundOrderInfo as &$value) {
                        $residue_payment_amount = $residue_payment_amount - $value['return_amount'];
                        $detail_json            = json_decode($value['detail_json'], true);
                        foreach ($detail_json as &$v) {
                            $refundItem[] = array(
                                'short_code' => $v['short_code'],
                                'nums'       => $v['nums'],
                            );
                        }
                    }
                    foreach ($orderItem as $kk => $vv) {
                        foreach ($refundItem as &$va) {
                            if ($vv['short_code'] == $va['short_code']) {
                                $orderItem[$kk]['nums'] = $vv['nums'] - $va['nums'];
                            }
                        }
                    }
                }
                if (bcsub($residue_payment_amount, $total_refund_money, 2) < 0) $this->throwError($sub_order_no . '退货金额大于订单可退货金额，请核实后重试！');
                $short_code_arr = array_column($orderItem, 'short_code');
                foreach ($items as $k => $v) {
                    if (!in_array($v['short_code'], $short_code_arr)) $this->throwError($sub_order_no . $v['short_code'] . '该简码不属于当前订单,请核实后重新提交');
                    foreach ($orderItem as &$vv) {
                        if ($vv['short_code'] == $v['short_code']) {
                            $difference = $vv['nums'] - $v['nums'];
                            if ($difference < 0) $this->throwError($sub_order_no . $v['short_code'] . '可退货数量为' . $vv['nums'] . ',请核实后重新提交');
                            $items[$k]['key']     = $k;
                            $items[$k]['is_gift'] = $vv['is_gift'];
                        }
                    }
                    //查询磐石es数据
                    $es             = new ElasticSearchService();
                    $panshiProducts = $es->getDocumentList(['index' => ['panshi.products'], 'match' => [['short_code' => $v['short_code']]], 'source' => ['cn_product_name', 'bar_code', 'short_code', 'capacity', 'product_unit_name', 'products_years'], 'limit' => 1]);
                    if (!isset($panshiProducts['data'][0])) $this->throwError($sub_order_no . '未获取到产品磐石信息');
                    if ($k == 0) {
                        $items[$k]['payment_amount'] = $orderInfo['payment_amount'];
                    } else {
                        $items[$k]['payment_amount'] = 0;
                    }
                    $items[$k]['capacity']     = isset($panshiProducts['data'][0]['capacity']) ? $panshiProducts['data'][0]['capacity'] : '';
                    $items[$k]['product_name'] = isset($panshiProducts['data'][0]['cn_product_name']) ? $panshiProducts['data'][0]['cn_product_name'] : '';
                    $items[$k]['unit']         = isset($data['data'][0]['product_unit_name']) ? $data['data'][0]['product_unit_name'] : '瓶';
                    $items[$k]['year']         = isset($data['data'][0]['products_years']) ? $data['data'][0]['products_years'] : '';
                    $items[$k]['bar_code']     = isset($data['data'][0]['bar_code']) ? $data['data'][0]['bar_code'] : '';
                }
                break;
        }
        return $items;
    }

    /**
     * Description:销售退货推送ERP
     * Author: gangh
     * Date: 2023/8/8
     * @param array $params 请求参数['operator_id'=>1,'operator_name'=>'','bill_no'=>'']
     * @return bool|string
     */
    public function SalesReturnPushErp($params)
    {
        $return = Db::name('sales_return')->where('bill_no', $params['bill_no'])->findOrEmpty();
        if (empty($return)) {
            return '销售退货订单查询失败';
        }
        if (empty($return['dingtalk_status']) || $return['dingtalk_status'] != 2) {
            return '销售退货订单未通过审核';
        }
        $return_list = [];
        $detail      = json_decode($return['detail_json'], true);
        foreach ($detail as $v) {
            $return_list[] = [
                "short_code"   => $v['short_code'],
                "number"       => $v['nums'],
                "price"        => $v['tax_unit_price'],
                "total_amount" => $v['tax_total_price'],
            ];
        }
        if (empty($return_list)) {
            return '销售退货订单产品查询失败';
        }
        
        if ($return['sale_bill_type'] == 2) {
            // 退货单审核完成后如果是线下的订单，需要生成负数应收、负数收款单
            pushQueue('orders', 'create_arap_ys_order', [
                'sub_order_no' => $return['sub_order_no'],
                'order_type' => -1,
                'bill_no' => $return['bill_no'],
            ]);
        }

        if ($return['bill_date'] < 1685548800) {
            $return['bill_date'] = 1685548800;
        }
        $body = [
            "corp"            => "001",
            "origin_order_no" => $return['sub_order_no'],
            "return_order_no" => $return['bill_no'],
            "warehouse"       => $return['warehouse_code'],
            "bill_date"       => date('Y-m-d', $return['push_erp_time']),
            "dept_code"       => $return['department_code'],
            "clerk"           => $return['clerk_code'],
            "delivery_model"  => $return['delivery_mode_code'],
            "waybill_no"      => $return['return_courier_no'],
            "settle_type"     => $return['settlement_method_code'],
            "return_list"     => $return_list,
            'main_order_no'   => self::getOriginalMainOrderNo($return),
        ];

        $offline_order = Db::name('offline_order')->where([
            ['sub_order_no', '=', $return['sub_order_no']],
            ['corp', '=', '001'], //单据公司编码：001-科技 002-云酒
            ['document_type', '=', '0'], //单据类型：0-样酒 1-销售单 2-T+销售单
        ])->find();
        if ($offline_order) {
            $body['remark']                   = $offline_order['remarks'];
            $body['customer_code']            = $offline_order['customer_code'];
            $body['settlement_customer_code'] = $offline_order['settle_customer_code'];
            $body['collection_type']          = $offline_order['collection_type'];
        }

        $url  = env('ITEM.ERP_URL') . '/erp/v3/saleOrder/returnHandle';
        $resp = curlRequest($url, json_encode($body));

        $push_t_status = 0;
        $remarks       = '';
        if (isset($resp['error_code']) && intval($resp['error_code']) === 0) {
            $push_t_status = 1;
            $remarks       = '推送ERP成功';
        } else {
            $push_t_status     = 2;
            $resp['error_msg'] = $resp['error_msg'] ?? '';
            $remarks           = '推送ERP失败：' . $resp['error_msg'];
        }
        Db::startTrans();
        try {

            // 修改订单已推送状态
            Db::name('sales_return')->where('id', $return['id'])->update([
                'push_t_status' => $push_t_status,
                'update_time'   => time(),
            ]);
            // 记录备注
            Db::name('order_remarks')->insert([
                'sub_order_no' => $return['bill_no'],
                'admin_id'     => $params['operator_id'],
                'remarks'      => $remarks,
                'created_time' => time(),
            ]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }

        if ($push_t_status == 2) {
            return $remarks;
        }

        return true;
    }

    /**
     * Description:销售退货弃审
     * Author: zrc
     * Date: 2023/8/24
     * Time: 11:41
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function rejectSalesReturn($params)
    {
        $salesReturn = Db::name('sales_return')->field('push_t_status,dingtalk_status,is_reject')->where(['bill_no' => $params['bill_no']])->find();
        if (empty($salesReturn)) $this->throwError('未获取到单据信息');
        if ($salesReturn['dingtalk_status'] != 2) $this->throwError('单据审批未通过，弃审失败');
        if ($salesReturn['is_reject'] == 1) $this->throwError('单据已弃审，请勿重复操作');
        //已推送erp的删除erp单据
        if ($salesReturn['push_t_status'] == 1) {
            $delErp = curlRequest(env('ITEM.ERP_URL') . '/erp/v3/saleOrder/deleteHandle', ['sub_order_no' => $params['bill_no']]);
            if (isset($delErp['error_code']) && $delErp['error_code'] != 0) $this->throwError($delErp['error_msg']);
        }
        $updateData = array(
            'dingtalk_status' => 3,
            'push_t_status'   => 0,
            'is_reject'       => 1,
            'update_time'     => time()
        );
        $result     = Db::name('sales_return')->where(['bill_no' => $params['bill_no']])->update($updateData);
        if (empty($result)) $this->throwError('弃审失败，请重试');
        //添加订单备注
        $remarks = array(
            'sub_order_no' => $params['bill_no'],
            'admin_id'     => $params['admin_id'],
            'remarks'      => '销售退货单弃审:' . $params['reason'],
            'created_time' => time()
        );
        Db::name('order_remarks')->insert($remarks);
        return true;
    }

    /**
     * Description:销售退货修改推送ERP时间
     * Author: gangh
     * Date: 2023/12/05
     * @param array $params 请求参数
     * @return bool|string
     */
    public function updatePushErpTime($params)
    {
        $return = Db::name('sales_return')->where('bill_no', $params['bill_no'])->findOrEmpty();
        if (empty($return)) {
            return '销售退货订单查询失败';
        }
        if (empty($return['dingtalk_status']) || $return['dingtalk_status'] != 2) {
            return '销售退货订单未通过审核';
        }
        $push_erp_time = strtotime($params['push_erp_time']);
        if (empty($push_erp_time) || $push_erp_time <= 0) {
            return '推送ERP时间格式错误';
        }
        Db::startTrans();
        try {

            // 修改订单已推送状态
            Db::name('sales_return')->where('id', $return['id'])->update([
                'push_erp_time' => $push_erp_time,
                'update_time'   => time(),
            ]);
            // 记录备注
            Db::name('order_remarks')->insert([
                'sub_order_no' => $return['bill_no'],
                'admin_id'     => $params['operator_id'],
                'remarks'      => '修改推送ERP时间',
                'created_time' => time(),
            ]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }

        return true;
    }

    /**
     * Description:销售退货推送萌牙WMS
     * Author: ggh
     * Date: 2023/12/8
     * @param array $params 请求参数['operator_id'=>1,'operator_name'=>'','bill_no'=>'']
     * @return bool|string
     */
    public function SalesReturnPushWms($params)
    {
        $return = Db::name('sales_return')->where('bill_no', $params['bill_no'])->findOrEmpty();
        if (empty($return)) {
            return '销售退货订单查询失败';
        }
        if ($return['push_wms_status'] == 1) {
            return '销售退货已推送成功，请勿重复操作。';
        }
        if ($return['is_push_wms'] == 0) {
            return '销售退货已设置不推送萌牙。';
        }
        $return['detail_json'] = json_decode($return['detail_json'], true);
        Db::startTrans();
        try {

            // 推送萌牙WMS
            $res = self::pushWms($return);

            Db::commit();
            return $res;
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }

    /**
     * Description:推送萌牙WMS
     * Author: gangh
     * Date: 2023/12/
     * @param array $params 销售退货信息
     * @return bool|string
     */
    static function pushWms($params)
    {
        $original_fictitious = self::getOriginalFictitious($params);
        $payee_merchant_id = self::getOrderCrop($params);

        $p_return_courier_no = [];
        foreach (explode(',', $params['return_courier_no']) as $rco) {
            $p_return_courier_no[] = trim($rco);
        }

        $body = [
            'corp'        => $payee_merchant_id,
            'store_code'  => env('ORDERS.STORE_CODE'),
            'order_no'    => $params['sub_order_no'],
            'return_code' => $params['bill_no'],
            'wy_no'       => $p_return_courier_no,
            'creator'       => $params['operator_name'],
            'user_remark' => $params['remarks'] ?? '',
            'goods'       => [],
        ];
        foreach ($params['detail_json'] as $v) {
            $body['goods'][] = [
                "goods_name" => $v['product_name'],
                "bar_code" => $v['bar_code'],
                "goods_years" => $v['year'],
                "fictitious" => $params['warehouse_code'],
                "number" => $v['nums'],
                "en_goods_name" => $v['product_name'],
                "short_code" => $v['short_code'],
                "capacity" => $v['capacity'],
                'original_fictitious' => $original_fictitious,
            ];
        }
        $url = env("ITEM.DISTRIBUTE_URL") . "/sync/returnsSync";
        $res = curlRequest($url, json_encode($body));

        // 默认推送成功
        $update = [
            'push_wms_status' => 2,
            'update_time'     => time(),
        ];
        $remarks = '推送萌牙成功';

        // 推送失败
        if (!isset($res['errorCode']) || intval($res['errorCode']) !== 0) {
            $update['push_wms_status'] = 3;
            $remarks = $res['msg'] ?? '未收到萌牙响应，请确认是否推送成功。';
            $remarks = "推送萌牙失败：" . $res['msg'];
        }

        
        switch ($update['push_wms_status']) {
            case 2:// 推送成功，修改状态审批中
                $update['dingtalk_status'] = 1;
                break;
            case 3:// 推送失败，修改状态审批拒绝
                $update['dingtalk_status'] = 3;
                break;
        }

        // 修改订单已推送状态
        Db::name('sales_return')->where('bill_no', $params['bill_no'])->update($update);
        // 记录备注
        Db::name('order_remarks')->insert([
            'sub_order_no' => $params['bill_no'],
            'admin_id'     => $params['operator_id'],
            'remarks'      => $remarks,
            'created_time' => time(),
        ]);

        return $remarks == '推送萌牙成功' ? true : $remarks;
    }

    /**
     * Description:WMS更新销售退货状态
     * Author: ggh
     * Date: 2024/05/16
     * @param array $params 请求参数
     * @return bool|string
     */
    public function updateStatusUp($params)
    {
        $time = time();
        $bill_no = explode(',', $params['bill_no']);
        $bill_items = Db::name('sales_return')->whereIn('bill_no', $bill_no)->column('id,sub_order_no,sale_bill_type');
        if (empty($bill_items)) {
            return true;
        }
        $ids = array_column($bill_items,'id');
        $sub_order_nos = array_column($bill_items,'sub_order_no');

        //1待上架，2部分上架，3已上架，4已撤销
        // 上架完成推送ERP
        if ($params['wms_order_status'] == 3) {
            Db::name('sales_return')->whereIn('id', $ids)->update([
                'dingtalk_status' => 2,
                'approval_time' => $time,
                'push_erp_time' => $time,
                'update_time' => $time,
            ]);
            // 记录备注
            Db::name('order_remarks')->insert([
                'sub_order_no' => $params['bill_no'],
                'admin_id'     => 0,
                'remarks'      => '萌牙退货入库单上架完成，审核通过。',
                'created_time' => $time,
            ]);

            $latest_work_orders = Db::table('vh_customer_service.vh_work_order')
                ->where('order_no', 'in', $sub_order_nos)
                ->order('created_time desc')
                ->column('id, order_no, work_order_type, created_time');

            $sub_not_order_nos = [];
            $latest_orders     = [];
            foreach ($latest_work_orders as $work_order) {
                if (!isset($latest_orders[$work_order['order_no']])) {
                    $latest_orders[$work_order['order_no']] = $work_order;
                    if ($work_order['work_order_type'] != 5) { // 工单类型=退货退款
                        $sub_not_order_nos[] = $work_order['order_no'];
                    }
                }
            }

            // 查询已开票的订单
            $invoiced_orders = Db::table('vh_invoice.vh_invoice_code')->alias('t1')
                ->leftJoin('vh_orders.vh_offline_order t2', 't1.order_no = t2.sub_order_no')
                ->where('t1.status', 'in', [1, 2])  // 开票状态 1,待开篇2，已开票3，开票拒绝
                ->whereNotIn('t1.order_no', $sub_not_order_nos)
                ->whereIn('t1.order_no', $sub_order_nos)
                ->column('t1.invoice_code,t1.order_no,t2.operator');
            $realnames = Db::table('vh_authority.vh_admins')->where('id', 'in', array_column($invoiced_orders, 'operator'))->column('realname');

            // 如果存在已开票订单,发送提醒
            if (!empty($invoiced_orders)) {
                $msg = "原订单号:" . implode(',', array_column($invoiced_orders, 'order_no')) . "\n";
                $msg .= "对应发票单号:" . implode(',', array_column($invoiced_orders, 'invoice_code')) . "\n";
                $msg .= "制单人:" . implode(',', $realnames) . "\n";
                $msg .= "该订单已开具发票,请财务处理冲红发票";

                $res = \Curl::sendWechatSender([
                    'msg'          => $msg,
                    'at'           => '',
                    'access_token' => 'b08f7db0-b1b0-4cfc-985b-11156adaada1',
                ]);
            }
            return $this->SalesReturnPushErp([
                'bill_no'       => $params['bill_no'],
                'operator_id'   => 0,
                'operator_name' => '萌牙上架',
            ]);
        }
        // 已撤销
        if ($params['wms_order_status'] == 4) {
            Db::startTrans();
            try {
                Db::name('sales_return')->whereIn('id', $ids)->update([
                    'dingtalk_status' => 3,
                    'update_time' => $time,
                ]);
                $data = [];
                foreach ($bill_no as $v) {
                    $data[] = [
                        'sub_order_no' => $v,
                        'admin_id'     => 0,
                        'remarks'      => '萌牙撤销退货入库单，退货单已驳回',
                        'created_time' => $time,
                    ];
                }
                // 记录备注
                Db::name('order_remarks')->insertAll($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return $e->getMessage();
            }
        }

        return true;
    }

    public function updateByInventory($param)
    {
        $now = time();
        Db::startTrans();
        try {
            $list = (new SalesReturnModel())
                ->where('dingtalk_status', 'in', [1, 0])
                ->where('sub_order_no', $param['sub_order_no'])
                ->where('return_courier_no', $param['return_courier_no'])
                ->field('id,bill_no,sub_order_no,warehouse_code,detail_json,return_courier_no,dingtalk_uid')
                ->select();

            if ($list->isEmpty()) {
                return ['status' => 1, 'msg' => '没有找到退回运单号匹配的销售退货单据'];
            }

            $p_items = $match_ids = $match_orders = $no_match_ids = $send_msg = $lf_msg = [];
            foreach ($param['detail_json'] as $item) {
                $p_items[$item['wh_code']][] = "{$item['short_code']}_{$item['nums']}";
            }

            foreach ($list as $th_order) {
                $list_ids[] = $th_order['id'];
                $p_json     = $p_items[$th_order['warehouse_code']] ?? null;

                if (empty($p_json)) {
                    $no_match_ids[] = $th_order['id'];
                    $send_msg[]     = [
                        'userid'  => $th_order['dingtalk_uid'],
                        'content' => "{$th_order['bill_no']} 退货单与仓库验收不一致,请及时修改.",
                        'msgtype' => "text",
                    ];
                    $lf_msg[]     = [
                        'userid'  => "LongFei",
                        'content' => "{$th_order['bill_no']} 退货单与仓库验收不一致,请及时修改.",
                        'msgtype' => "text",
                    ];
                    continue;
                }
                $to_items = json_decode(json_encode($th_order['detail_json']), true);
                $to_json  = [];
                foreach ($to_items as $to_item) {
                    $to_json[] = "{$to_item['short_code']}_{$to_item['nums']}";
                }
                $diff = array_diff($to_json, $p_json);
                if (!empty($diff)) {
                    $no_match_ids[] = $th_order['id'];
                    $send_msg[]     = [
                        'userid'  => $th_order['dingtalk_uid'],
                        'content' => "{$th_order['bill_no']} 退货单与仓库验收不一致,请及时修改.",
                        'msgtype' => "text",
                    ];
                    $lf_msg[]     = [
                        'userid'  => "LongFei",
                        'content' => "{$th_order['bill_no']} 退货单与仓库验收不一致,请及时修改.",
                        'msgtype' => "text",
                    ];
                } else {
                    $match_ids[] = $th_order['bill_no'];
                    $match_orders[] = $th_order['sub_order_no'];
                    $send_msg[]     = [
                        'userid'  => $th_order['dingtalk_uid'],
                        'content' => "{$th_order['bill_no']} 退货单仓库已完成上架.",
                        'msgtype' => "text",
                    ];
                }
            }

            if (empty($match_ids)) {
                $match_status = 2; //完全不匹配
                $msg          = '单据不一致';
            } elseif (empty($no_match_ids)) {
                $match_status = 4; //完全匹配
                $msg          = '单据一致';
            } else {
                $match_status = 3; //部分匹配
                $msg          = '单据部分一致';
            }

            //修改数据库
            if (!empty($match_ids)) {
                (new SalesReturnModel)->where('bill_no', 'in', $match_ids)->update([
                    'dingtalk_status' => 2,
                    'push_erp_time'   => $now,
                    'approval_time'   => $now,
                    'update_time'     => $now,
                ]);


                $latest_work_orders = Db::table('vh_customer_service.vh_work_order')
                    ->where('order_no', 'in', $match_orders)
                    ->order('created_time desc')
                    ->column('id, order_no, work_order_type, created_time');

                $sub_not_order_nos = [];
                $latest_orders     = [];
                foreach ($latest_work_orders as $work_order) {
                    if (!isset($latest_orders[$work_order['order_no']])) {
                        $latest_orders[$work_order['order_no']] = $work_order;
                        if ($work_order['work_order_type'] != 5) { // 工单类型=退货退款
                            $sub_not_order_nos[] = $work_order['order_no'];
                        }
                    }
                }

                // 查询已开票的订单
                $invoiced_orders = Db::table('vh_invoice.vh_invoice_code')->alias('t1')
                    ->leftJoin('vh_orders.vh_offline_order t2', 't1.order_no = t2.sub_order_no')
                    ->where('t1.status', 'in', [1, 2])  // 开票状态 1,待开篇2，已开票3，开票拒绝
                    ->whereNotIn('t1.order_no', $sub_not_order_nos)
                    ->whereIn('t1.order_no', $match_orders)
                    ->column('t1.invoice_code,t1.order_no,t2.operator');
                $realnames = Db::table('vh_authority.vh_admins')->where('id', 'in', array_column($invoiced_orders, 'operator'))->column('realname');

                // 如果存在已开票订单,发送提醒
                if (!empty($invoiced_orders)) {
                    $msg = "原订单号:" . implode(',', array_column($invoiced_orders, 'order_no')) . "\n";
                    $msg .= "对应发票单号:" . implode(',', array_column($invoiced_orders, 'invoice_code')) . "\n";
                    $msg .= "制单人:" . implode(',', $realnames) . "\n";
                    $msg .= "该订单已开具发票,请财务处理冲红发票";

                    $res = \Curl::sendWechatSender([
                        'msg'          => $msg,
                        'at'           => '',
                        'access_token' => 'b08f7db0-b1b0-4cfc-985b-11156adaada1',
                    ]);
                }

            }

            if (!empty($no_match_ids)) {
                (new SalesReturnModel)->where('id', 'in', $no_match_ids)->update([
                    'dingtalk_status' => 3,
                    'push_erp_time'   => $now,
                    'approval_time'   => $now,
                    'update_time'     => $now,
                ]);
            }

            if (!empty($send_msg)) {
                foreach ($send_msg as $send) {
                    try {
                        \Curl::wecomSend($send['content'], $send['userid'], $send['msgtype']);
                    } catch (Exception $e) {
                        Log::write('updateByInventory 发送企业微信消息错误: ' . $e->getMessage());
                    }
                }
            }

            if (!empty($lf_msg)) {
                foreach ($lf_msg as $send) {
                    try {
                        \Curl::wecomSend($send['content'], $send['userid'], $send['msgtype']);
                    } catch (Exception $e) {
                        Log::write('updateByInventory 发送企业微信消息错误: ' . $e->getMessage());
                    }
                }
            }

            Db::commit();

            if (!empty($match_ids)) {
                try {
                    foreach ($match_ids as $match_bill_no) {
                        $this->SalesReturnPushErp([
                            'bill_no'       => $match_bill_no,
                            'operator_id'   => 0,
                            'operator_name' => '萌牙单据完结',
                        ]);
                    }
                }catch (\Exception $ex){
                    Log::write('updateByInventory 推送ERP失败: '. $ex->getMessage());
                }
            }

            return ['status' => $match_status, 'msg' => $msg];
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('updateByInventory 执行错误: '. $e->getMessage());
            return $this->throwError($e->getMessage(), ErrorCode::PARAM_ERROR);
        }
    }

    public function revokeReturns($param)
    {
        $now = time();
        Db::startTrans();
        try {
            $info = (new SalesReturnModel())
                ->where('dingtalk_status', 'in', [1]) //审批中
                ->where('bill_no', 'in', $param['bill_no'])
                ->field('id,bill_no,sub_order_no,warehouse_code,detail_json,return_courier_no,dingtalk_uid')
                ->find();

            if (!$info) {
                return ['status' => 1, 'msg' => '未找到退货单，请检查参数和状态'];
            }

            \Curl::revokeReturns(['order_no' => $info['sub_order_no']]);

            (new SalesReturnModel)->where('id', $info['id'])->update([
                'dingtalk_status' => 3,
//                    'push_erp_time'   => $now,
//                    'approval_time'   => $now,
                'update_time'     => $now,
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('updateByInventory 执行错误: ' . $e->getMessage());
            return $this->throwError($e->getMessage(), ErrorCode::PARAM_ERROR);
        }
    }

    public function priceSplit($param)
    {
        Db::startTrans();
        try {
            $order = Es::name(Es::ORDERS)->where([['_id', '==', $param['sub_order_no']]])->field('id,payment_amount,main_order_no,order_qty,sub_order_no,period,package_id')->find();
            if (empty($order)) throw new Exception('未找到订单');
            $package = Es::name(Es::PERIODS_PACKAGE)->where([['_id', '==', $order['package_id']]])
                ->field('id,is_mystery_box,associated_products')
                ->find();
            if (empty($package)) throw new Exception('未找到订单');


            $ppi_list = Db::table('vh_commodities.vh_periods_product_inventory')
                ->where('period', $order['period'])
                ->column('id,period,product_id,costprice,short_code,product_name,en_product_name', 'product_id');

            if ($package['is_mystery_box'] == 1) {
                $product_items       = Db::name('order_mystery_box_log')
                    ->where('main_order_no', $order['main_order_no'])
                    ->where('package_id', $order['package_id'])
                    ->value('product_info');
                $associated_products = json_decode($product_items, true);
            } else {
                $associated_products = json_decode($package['associated_products'], true);
            }

            $pkg_costprice     = 0; //套餐成本
            $pkg_nums          = 0; //套餐数量
            $product_costprice = [];
            foreach ($associated_products as &$product) {
                $pi_num                                    = bcmul($product['nums'], $order['order_qty']);
                $product['nums']                           = $pi_num;
                $pkg_nums                                  += $pi_num;
                $pi_costprice                              = $ppi_list[$product['product_id']]['costprice'] ?? 0;
                $product['costprice']                      = $pi_costprice;
                $product['short_code']                     = $ppi_list[$product['product_id']]['short_code'] ?? '未知简码';
                $product['product_name']                   = $ppi_list[$product['product_id']]['product_name'] ?? '';
                $product['en_product_name']                = $ppi_list[$product['product_id']]['en_product_name'] ?? '';
                $product_costprice[$product['product_id']] = $pi_costprice;
                $pkg_costprice                             = bcadd($pkg_costprice, bcmul($pi_num, $pi_costprice, 2), 2);
            }
            $package['associated_products'] = $associated_products;
            $package['costprice']           = $pkg_costprice;
            $package['pkg_nums']            = $pkg_nums;
            $package['product_costprice']   = $product_costprice;

            $data = [];
            foreach ($package['associated_products'] as &$product) {
                $product['ratio']                         = bcdiv($product['costprice'], $pkg_costprice, 8);
                $data['products'][$product['short_code']] = $product['ratio'];
            }

            $refund_money = 0;
            if (!empty($param['products'])) {
                foreach ($param['products'] as $prod) {
                    $ratio        = $data['products'][$prod['short_code']];
                    $refund_money = bcadd($refund_money, bcmul($prod['nums'], bcmul($order['payment_amount'], $ratio, 8), 8), 8);
                }
                $data['refund_money'] = round(min(max(0, $refund_money), $order['payment_amount']), 2);
            }
            Db::commit();
            return $data;
        } catch (\Exception $e) {
            Db::rollback();
            Log::write('priceSplit 拆分金额 执行错误: ' . $e->getMessage());
            return $this->throwError($e->getMessage(), ErrorCode::PARAM_ERROR);
        }
    }

    static public function getOrderCrop($param)
    {
        $payee_merchant_id = null;
        //销售单据类型 1酒云线上2线下销售3三方线上
        if ($param['sale_bill_type'] == 1) {
            //酒云线上
            $period            = Es::name(Es::ORDERS)->where([['_id', '==', $param['sub_order_no']]])->value('period');
            $payee_merchant_id = Es::name(Es::PERIODS)->where([['_id', '==', $period]])->value('payee_merchant_id');
        } else if ($param['sale_bill_type'] == 2) {
            //线下销售
            $payee_merchant_id = Db::name('offline_order')->alias('o')->join('collecting_company c', 'c.corp = o.corp')->where('o.sub_order_no', '=', $param['sub_order_no'])->value('c.id');
        } else if ($param['sale_bill_type'] == 3) {
            //三方线上
            $offline_order     = self::getTripartiteOrderCorp([$param['sub_order_no']]);
            $offline_order     = $offline_order[0];
            $payee_merchant_id = Db::name('collecting_company')->where('corp', $offline_order['corp'])->value('id');
        }
        return $payee_merchant_id;
    }

    static public function getOriginalFictitious($param)
    {
        $wh_code = null;
        //销售单据类型 1酒云线上2线下销售3三方线上
        if ($param['sale_bill_type'] == 1) {
            //酒云线上
            $order = Es::name(Es::ORDERS)->where([['_id', '==', $param['sub_order_no']]])->field('period,warehouse_code')->find();
            if (empty($order['warehouse_code'])) {
                $wh_code = Db::table('vh_commodities.vh_periods_product_inventory')->where('period', $order['period'])->value('warehouse_id');
            } else {
                $wh_code = $order['warehouse_code'];
            }
        } else if ($param['sale_bill_type'] == 2) {
            //线下销售
            $wh_code = Db::name('offline_order')->where('sub_order_no', '=', $param['sub_order_no'])->value('warehouse_code');
        } else if ($param['sale_bill_type'] == 3) {
            //三方线上
            $wh_code = Db::name('tripartite_order')->where('sub_order_no', '=', $param['sub_order_no'])->value('warehouse_id');
        }
        return $wh_code;
    }

    static public function getOriginalMainOrderNo($param)
    {
        $wh_code = null;
        //销售单据类型 1酒云线上2线下销售3三方线上
        if ($param['sale_bill_type'] == 1) {
            //酒云线上
            $order = Es::name(Es::ORDERS)->where([['_id', '==', $param['sub_order_no']]])->field('id,main_order_no')->find();
            $wh_code = $order['main_order_no'] ?? '';
        } else if ($param['sale_bill_type'] == 2) {
            //线下销售
            $wh_code = Db::name('offline_order')
                ->alias('of')
                ->leftJoin('order_main om', 'om.id = of.main_order_id')
                ->where('of.sub_order_no', '=', $param['sub_order_no'])
                ->value('om.main_order_no');
        } else if ($param['sale_bill_type'] == 3) {
            //三方线上
            $wh_code = Db::name('tripartite_order')
                ->alias('t')
                ->leftJoin('order_main om', 'om.id = t.main_order_id')
                ->where('t.sub_order_no', '=', $param['sub_order_no'])
                ->value('om.main_order_no');
        }
        return $wh_code;
    }

}
