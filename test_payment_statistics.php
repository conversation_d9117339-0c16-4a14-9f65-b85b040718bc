<?php
/**
 * 测试收款统计功能
 * 用于验证订单数量和退款订单数量的统计是否正确
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\service\PaymentStatistics;
use app\model\DailyPaymentStatistics;

// 模拟测试数据
echo "开始测试收款统计功能...\n";

try {
    $service = new PaymentStatistics();
    
    // 测试1: 写入收款数据（旧版本接口）
    echo "测试1: 写入收款数据（旧版本接口）\n";
    $result1 = $service->writePaymentData('001', 1, 100.50);
    echo "结果: " . ($result1 ? "成功" : "失败") . "\n";
    
    // 测试2: 写入退款数据（旧版本接口）
    echo "测试2: 写入退款数据（旧版本接口）\n";
    $result2 = $service->writePaymentData('001', 2, 50.25);
    echo "结果: " . ($result2 ? "成功" : "失败") . "\n";
    
    // 测试3: 获取当日数据
    echo "测试3: 获取当日数据\n";
    $todayData = $service->getTodayData('001');
    echo "当日数据: " . json_encode($todayData, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 测试4: 同步数据到数据库
    echo "测试4: 同步数据到数据库\n";
    $syncResult = $service->syncRedisToDatabase(date('Y-m-d'));
    echo "同步结果: " . ($syncResult ? "成功" : "失败") . "\n";
    
    // 测试5: 按月统计
    echo "测试5: 按月统计\n";
    $monthlyData = $service->getMonthlyStatistics([
        'company_code' => '001',
        'year' => date('Y'),
        'month' => date('n')
    ]);
    echo "月度统计: " . json_encode($monthlyData, JSON_UNESCAPED_UNICODE) . "\n";
    
    echo "所有测试完成！\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
}
