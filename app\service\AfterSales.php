<?php


namespace app\service;

use app\BaseService;
use app\model\AfterSales as AfterSalesModel;
use app\service\es\Es;
use app\service\Order as OrderService;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

class AfterSales extends BaseService
{

    /**
     * Description:获取订单可退款金额
     * Author: zrc
     * Date: 2022/3/3
     * Time: 9:40
     * @param $requestparams
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderRefundMoney($params)
    {
        if ($params['order_type'] == 11) {
            $orderInfo = Db::table('vh_auction.vh_orders')
                ->field('payment_amount,created_time,payment_time')
                ->where(['order_no' => $params['sub_order_no']])->find();
            if (empty($orderInfo)) $this->throwError('未获取到订单信息');
            if (empty($orderInfo['payment_time'])) $this->throwError('订单未支付');
            $result = array(
                'refund_money' => $orderInfo['payment_amount'],
                'express_fee'  => 0,
                'ts_day'       => 0,
                'ts_free'      => '0.00'
            );
        } else {
            //查询订单信息
            $config_order_type = config('config')['order_type'];//订单频道获取
            $orderInfo         = Db::name($config_order_type[intval($params['order_type'])]['table'])
                ->field('payment_amount,express_fee,is_ts,created_time,sub_order_status,payment_time')
                ->where(['sub_order_no' => $params['sub_order_no']])->find();
            if (empty($orderInfo)) $this->throwError('未获取到订单信息');
            if (empty($orderInfo['payment_time'])) $this->throwError('订单未支付');
            $result = array(
                'refund_money' => $orderInfo['payment_amount'],
                'express_fee'  => $orderInfo['express_fee'],
                'ts_day'       => 0,
                'ts_free'      => '0.00'
            );
            //暂存费用计算
            if ($orderInfo['is_ts'] == 1) {
                $ts_day = round((time() - $orderInfo['created_time']) / 86400, 0);
//            $ts_free = 0;
//            if ($ts_day > env('ORDERS.free_ts_day')) {
//                $ts_free = round($orderInfo['payment_amount'] * ($ts_day - env('ORDERS.free_ts_day')) * env('ORDERS.ts_rate'), 2);
//            }
                $result['ts_day'] = $ts_day;
                //$result['ts_free'] = $ts_free;
            }
        }
        return $result;
    }

    /**
     * Description:创建工单计算运费差价
     * Author: zrc
     * Date: 2021/12/16
     * Time: 14:09
     * @param $requestparams
     * @return int[]|string[]
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function freightDifference($requestparams)
    {
        $params       = $requestparams;
        $orderService = new OrderService();
        $orderInfo    = $orderService->orderDetail(['order_no' => $params['sub_order_no'], 'field' => 'period,package_id,order_qty,express_fee,express_type,province_id,city_id,express_coupon_id,order_type']);
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        $difference = 0;
        if ($params['ticket_type'] == 1) {//修改快递方式
            if (in_array($params['express_type'], [3, 31]) && $orderInfo['express_type'] != $params['express_type'] && empty($orderInfo['express_coupon_id'])) {
                if ($orderInfo['order_type'] == 2) {//跨境商品冷链费计算
                    $courier_fee = Db::name('cross_sfll_area')->where(['regional_id' => $orderInfo['province_id']])->value('courier_fee');
                    $difference  = round($courier_fee - $orderInfo['express_fee'], 2);
                } else {//普通商品冷链费计算
                    //套餐ID获取商品套餐信息
                    $packageInfo = esGetOne($orderInfo['package_id'], 'vinehoo.periods_set');
                    if (empty($packageInfo)) $this->throwError('未获取到产品套餐信息');
                    $associated_products = json_decode($packageInfo['associated_products'], true);
                    $capacity            = 0;
                    foreach ($associated_products as &$vv) {
                        $productsInfo = $this->httpGet(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/info', ['id' => $vv['product_id'], 'fields' => 'capacity']);
                        if ($productsInfo['error_code'] != 0 || empty($productsInfo['data'])) $this->throwError('未获取到产品信息');
                        $capacity += (int)$productsInfo['data']['capacity'] * $vv['nums'] * $orderInfo['order_qty'];//套餐的总容量
                    }
                    $freight    = calcFreight($orderInfo['city_id'], $capacity);
                    $difference = round($freight - $orderInfo['express_fee'], 2);
                }
            }
        }
        if ($params['ticket_type'] == 2) {//修改收货地址
            if (in_array($orderInfo['express_type'], [3, 31]) && empty($orderInfo['express_coupon_id'])) {
                if ($orderInfo['order_type'] == 2) {//跨境商品冷链费计算
                    $courier_fee = Db::name('cross_sfll_area')->where(['regional_id' => $params['province_id']])->value('courier_fee');
                    $difference  = round($courier_fee - $orderInfo['express_fee'], 2);
                } else {//普通商品冷链费计算
                    //套餐ID获取商品套餐信息
                    $packageInfo = esGetOne($orderInfo['package_id'], 'vinehoo.periods_set');
                    if (empty($packageInfo)) $this->throwError('未获取到产品套餐信息');
                    $associated_products = json_decode($packageInfo['associated_products'], true);
                    $capacity            = 0;
                    foreach ($associated_products as &$vv) {
                        $productsInfo = $this->httpGet(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/info', ['id' => $vv['product_id'], 'fields' => 'capacity']);
                        if ($productsInfo['error_code'] != 0 || empty($productsInfo['data'])) $this->throwError('未获取到产品信息');
                        $capacity += (int)$productsInfo['data']['capacity'] * $vv['nums'] * $orderInfo['order_qty'];//套餐的总容量
                    }
                    $freight    = calcFreight($params['city_id'], $capacity);
                    $difference = round($freight - $orderInfo['express_fee'], 2);
                }
            }
        }
        if ($difference < 0) $difference = 0;
        $result = array('difference' => $difference);
        return $result;
    }

    /**
     * Description:冻结/解冻订单
     * Author: zrc
     * Date: 2022/3/3
     * Time: 17:05
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function freezeOrder($params)
    {
        $config_order_type = config('config')['order_type'];//订单频道获取
        $field             = 'push_wms_status,freeze_status,warehouse_code';
        if ($params['order_type'] == 2) {
            $field = 'freeze_status,payment_doc,warehouse_code';
        }
        if ($params['order_type'] == 11) {
            $orderInfo = Db::table('vh_auction.vh_orders')->field($field)->where(['order_no' => $params['sub_order_no']])->find();
        } else {
            $orderInfo = Db::name($config_order_type[intval($params['order_type'])]['table'])
                ->field($field)
                ->where(['sub_order_no' => $params['sub_order_no']])
                ->find();
        }
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($params['type'] == 1) {
            if ($orderInfo['freeze_status'] == 1) return true;//订单已冻结
            if ($params['order_type'] == 2) {
                if ($orderInfo['payment_doc'] == 1) $this->throwError('订单已推送支付单，冻结失败');
            }
            $status     = 1;
            $msg        = '冻结';
            $updateData = array(
                'freeze_status' => 1,
                'update_time'   => time(),
            );
        } else {
            if ($orderInfo['freeze_status'] == 0) return true;//订单未冻结
            $status     = 2;
            $msg        = '解冻';
            $updateData = array(
                'freeze_status' => 0,
                'update_time'   => time(),
            );
        }
        Db::startTrans();
        try {
            if ($params['order_type'] == 11) {
                $updateOrder = Db::table('vh_auction.vh_orders')->where(['order_no' => $params['sub_order_no']])->update($updateData);
            } else {
                $updateOrder = Db::name($config_order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->update($updateData);
            }
            if (empty($updateOrder)) $this->throwError($msg . '失败');
            if (isset($orderInfo['push_wms_status']) && $orderInfo['push_wms_status'] == 1) {
                //前置仓已推送订单处理
                if ($params['type'] == 1) {
                    //获取仓库编码配置
                    $config_warehouse = config('config')['warehouse'];
                    $wms_id           = 1;
                    foreach ($config_warehouse as &$val) {
                        if (in_array($orderInfo['warehouse_code'], explode(',', $val['code']))) $wms_id = $val['value'];
                    }
                    if ($wms_id == 2) {
                        //前置仓撤单
                        $revokeOrder = $this->httpPost(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order', ['sub_order_no' => $params['sub_order_no']]);
                        if (!isset($revokeOrder['error_code']) || $revokeOrder['error_code'] != 0) {
                            $this->throwError('京东前置仓撤单失败！' . isset($revokeOrder['error_msg']) ? $revokeOrder['error_msg'] : '');
                        }
                    }
                }
                $dealWMS = $this->httpPost(env('ITEM.DISTRIBUTE_URL') . '/sync/receiveOrderFreeze', ['store_code' => env('ORDERS.STORE_CODE'), 'orderno' => $params['sub_order_no'], 'status' => $status]);
                if ($dealWMS['errorCode'] != 0) $this->throwError($dealWMS['msg']);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:手动创建补差价、换货、补发订单
     * Author: zrc
     * Date: 2022/3/11
     * Time: 14:59
     * @param $requestparams
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createOrder($requestparams)
    {
        $params    = $requestparams;
        $orderInfo = $params['order_info'];
        //主订单信息获取
        $mainOrderInfo = Db::name('order_main')->where(['id' => $orderInfo['main_order_id']])->find();
        if (empty($mainOrderInfo)) $this->throwError('未获取到主订单信息');
        $receiveInfo = Db::name('sub_order_receive_information')->where(['sub_order_no' => $orderInfo['sub_order_no'], 'uid' => $mainOrderInfo['uid']])->find();
        if (!empty($receiveInfo)) {
            $mainOrderInfo['province_id']     = $receiveInfo['province_id'];
            $mainOrderInfo['city_id']         = $receiveInfo['city_id'];
            $mainOrderInfo['district_id']     = $receiveInfo['district_id'];
            $mainOrderInfo['address']         = $receiveInfo['address'];
            $mainOrderInfo['consignee']       = $receiveInfo['consignee'];
            $mainOrderInfo['consignee_phone'] = $receiveInfo['consignee_phone'];
        }
        $afterSalesModel = new AfterSalesModel();
        if ($params['type'] == 1) {//补差价
            if (!empty($params['consignee'])) {
                //用户信息加密处理
                $consignee           = trim($params['consignee']);
                $encrypt             = cryptionDeal(1, [$consignee], $params['operator'], '后端用户');
                $consignee           = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                $params['consignee'] = $consignee;
            }
            if (!empty($params['consignee_phone'])) {
                //用户信息加密处理
                $phone                     = trim($params['consignee_phone']);
                $encrypt                   = cryptionDeal(1, [$phone], $params['operator'], '后端用户');
                $phone                     = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
                $params['consignee_phone'] = $phone;
            }
            $data = array(
                'uid'               => $mainOrderInfo['uid'],
                'payment_amount'    => $params['freight_difference'],
                'cash_amount'       => $params['freight_difference'],
                'province_id'       => isset($params['province_id']) ? $params['province_id'] : $mainOrderInfo['province_id'],
                'city_id'           => isset($params['city_id']) ? $params['city_id'] : $mainOrderInfo['city_id'],
                'district_id'       => isset($params['district_id']) ? $params['district_id'] : $mainOrderInfo['district_id'],
                'address'           => isset($params['address']) ? $params['address'] : $mainOrderInfo['address'],
                'consignee'         => isset($params['consignee']) ? $params['consignee'] : $mainOrderInfo['consignee'],
                'consignee_phone'   => isset($params['consignee_phone']) ? $params['consignee_phone'] : $mainOrderInfo['consignee_phone'],
                'order_type'        => 0,//补差价固定闪购频道
                'operator'          => $params['operator'],
                'period'            => 71502,//补差价固定期数
                'package_id'        => 205401,//补差价固定套餐
                'order_from'        => $orderInfo['order_from'],
                'order_qty'         => 1,
                'express_type'      => isset($params['express_type']) ? $params['express_type'] : $orderInfo['express_type'],
                'is_virtual'        => 1,
                'predict_time'      => strtotime(predictTimeDeal($orderInfo['predict_time'], $orderInfo['order_type'])),
                'payment_time'      => $params['freight_difference'] > 0 ? 0 : time(),
                'related_order_no'  => $orderInfo['sub_order_no'],
                'push_t_status'     => 3,
                'push_wms_status'   => 3,
                'payment_doc'       => 3,
                'push_store_status' => 3,
                'work_order_id'     => $params['work_order_id'],
            );
            //商家秒发数据处理
            if ($orderInfo['order_type'] == 9) {
                $data['order_type'] = 9;
                //获取商家对应的补差价商品期数套餐ID
                $merchantInfo = $this->httpGet(env('ITEM.VMALL_URL') . '/vmall/v3/merchant/detail', ['id' => $orderInfo['merchant_id']]);
                if (!isset($merchantInfo['error_code']) || $merchantInfo['error_code'] != 0) $this->throwError('获取商家补差价商品信息失败');
                $data['period']              = $merchantInfo['data']['period'];
                $data['package_id']          = $merchantInfo['data']['package_id'];
                $data['product_channel']     = $orderInfo['product_channel'];
                $data['delivery_method']     = $orderInfo['delivery_method'];
                $data['delivery_store_id']   = $orderInfo['delivery_store_id'];
                $data['delivery_store_name'] = $orderInfo['delivery_store_name'];
                $data['merchant_id']         = $orderInfo['merchant_id'];
            }
            //补差价订单，子订单号统一为VHG+原单号，后续-1-2-3....-mll
            $data['old_sub_order_no'] = $orderInfo['sub_order_no'];
            $order_no                 = $afterSalesModel->createOrder($data);
            $main_order_no            = $order_no['main_order_no'];
            $sub_order_no             = $order_no['sub_order_no'];
            if (empty($main_order_no)) $this->throwError('创建订单失败');
            //日志记录
            $log    = array(
                'main_order_no'       => $main_order_no,
                'create_param'        => '',
                'stock_param'         => '',
                'dec_stock_status'    => 0,
                'timeout_deal_status' => json_encode(['stock' => 1, 'coupon' => 1]),
                'notify_deal_status'  => json_encode(['order' => 0, 'invoice' => 1, 'group' => 1, 'fullgift' => 1, 'wms' => 1]),
                'created_time'        => time()
            );
            $addLog = Db::name('order_deal_log')->insert($log);
            if (empty($addLog)) $this->throwError('日志写入失败');
            //添加超时任务
            $pushData     = array(
                'namespace' => 'orders',
                'key'       => $main_order_no,
                'data'      => base64_encode(json_encode(['main_order_no' => $main_order_no])),
                'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/order/timeOutOrderDeal',
                'timeout'   => env('ORDERS.AFTER_SALES_ORDER_TIMEOUT'),
            );
            $timingResult = $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
            //任务日志
            $taskLog = '补差价超时任务日志：' . $main_order_no . '请求参数：' . json_encode($pushData, JSON_UNESCAPED_UNICODE) . '返回参数：' . json_encode($timingResult, JSON_UNESCAPED_UNICODE);
            file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'timingLog' . '.log', $taskLog . PHP_EOL, FILE_APPEND);
            if (!isset($timingResult['error_code']) || $timingResult['error_code'] != 0) $this->throwError('订单创建超时任务失败');
            $backParams = array(
                'main_order_no' => $main_order_no,
                'sub_order_no'  => $sub_order_no,
                'countdown'     => strval(intval(env('ORDERS.AFTER_SALES_ORDER_TIMEOUT')) * 60),
                'create_time'   => time()
            );
        } else if ($params['type'] == 2) {//换货、补发
            //库存扣除
            $main_order_no = creatOrderNo(env('ORDERS.ORDER_MAIN'), $mainOrderInfo['uid']);
            if ($orderInfo['order_type'] == 0) $channel = 'flash';
            if ($orderInfo['order_type'] == 1) $channel = 'second';
            if ($orderInfo['order_type'] == 2) $channel = 'cross';
            if ($orderInfo['order_type'] == 3) $channel = 'leftover';
            if ($orderInfo['order_type'] == 4) $channel = 'rabbit';
            if ($orderInfo['order_type'] == 9) $channel = 'second_merchants';
            if (isset($channel)) {
                $items       = array(
                    array(
                        'period'            => $orderInfo['period'],
                        'set_id'            => $params['new_package_id'],
                        'delivery_store_id' => isset($orderInfo['delivery_store_id']) ? $orderInfo['delivery_store_id'] : 0,
                        'buy_num'           => $params['nums'],
                        'channel'           => $channel,
                        'mystery_box'       => []
                    )
                );
                $stock_param = json_encode(['orderno' => $main_order_no, 'groupid' => 0, 'group_status' => 0, 'items' => $items]);
                $stockVerify = httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/dec', $stock_param);
                if ($stockVerify['error_code'] != 0) $this->throwError('商品库存扣减失败', 20107);
            }
            if (!empty($params['consignee'])) {
                //用户信息加密处理
                $consignee           = trim($params['consignee']);
                $encrypt             = cryptionDeal(1, [$consignee], $params['operator'], '后端用户');
                $consignee           = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                $params['consignee'] = $consignee;
            }
            if (!empty($params['consignee_phone'])) {
                //用户信息加密处理
                $phone                     = trim($params['consignee_phone']);
                $encrypt                   = cryptionDeal(1, [$phone], $params['operator'], '后端用户');
                $phone                     = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
                $params['consignee_phone'] = $phone;
            }
            if ($orderInfo['order_type'] != 9) {
                //仓库编码获取
                $productList = $this->httpGet(env('ITEM.COMMODITIES_URL') . '/commodities/v3/package/productList', ['period' => $orderInfo['period'], 'periods_type' => $orderInfo['order_type']]);
                if (!isset($productList['error_code']) || $productList['error_code'] != 0 || !isset($productList['data'][0]['product'][0]['erp_id'])) {
                    //退还库存
                    httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', $stock_param);
                    $this->throwError('未获取到商品仓库编码');
                }
                $warehouse_code = $productList['data'][0]['product'][0]['erp_id'];
            } else {
                $warehouse_code = $orderInfo['warehouse_code'];
            }

            $period_ifno = Es::name(Es::PERIODS)->where([['_id', '==', $orderInfo['period']]])->field('id,predict_shipment_time')->find();
            if (!empty($period_ifno['predict_shipment_time'])) {
                $p_predict_time = strtotime(predictTimeDeal(strtotime($period_ifno['predict_shipment_time']), $orderInfo['order_type'], $orderInfo['warehouse_code']));
//                $p_predict_time = strtotime($period_ifno['predict_shipment_time']);
            } else {
                $p_predict_time = strtotime(predictTimeDeal($orderInfo['predict_time'], $orderInfo['order_type'], $orderInfo['warehouse_code']));
            }

            $data = array(
                'uid'              => $mainOrderInfo['uid'],
                'main_order_no'    => $main_order_no,
                'payment_amount'   => 0,
                'payment_method'   => $mainOrderInfo['payment_method'],
                'province_id'      => isset($params['province_id']) ? $params['province_id'] : $mainOrderInfo['province_id'],
                'city_id'          => isset($params['city_id']) ? $params['city_id'] : $mainOrderInfo['city_id'],
                'district_id'      => isset($params['district_id']) ? $params['district_id'] : $mainOrderInfo['district_id'],
                'address'          => isset($params['address']) ? $params['address'] : $mainOrderInfo['address'],
                'consignee'        => isset($params['consignee']) ? $params['consignee'] : $mainOrderInfo['consignee'],
                'consignee_phone'  => isset($params['consignee_phone']) ? $params['consignee_phone'] : $mainOrderInfo['consignee_phone'],
                'order_type'       => $orderInfo['order_type'],
                'operator'         => $params['operator'],
                'period'           => $orderInfo['period'],
                'package_id'       => $params['new_package_id'],
                'order_from'       => $orderInfo['order_from'],
                'order_qty'        => $params['nums'],
                'express_type'     => 5,
                'predict_time'     => $p_predict_time,
                'payment_time'     => $params['different_money'] > 0 ? 0 : time(),
                'related_order_no' => $orderInfo['sub_order_no'],
                'work_order_id'    => $params['work_order_id'],
                'warehouse_code'   => $warehouse_code,
                'erp_push_amount'  => $params['erp_push_amount'],
            );
            if ($orderInfo['order_type'] == 2) {
                $data['realname']   = $orderInfo['realname'];
                $data['id_card_no'] = $orderInfo['id_card_no'];
            }
            if ($orderInfo['order_type'] == 9) {
                $data['product_channel']     = $orderInfo['product_channel'];
                $data['delivery_method']     = $orderInfo['delivery_method'];
                $data['delivery_store_id']   = $orderInfo['delivery_store_id'];
                $data['delivery_store_name'] = $orderInfo['delivery_store_name'];
                $data['merchant_id']         = $orderInfo['merchant_id'];
                $data['express_type']        = 0;
                $data['push_wms_status']     = 3;
            }
            //订单金额处理
            $data['payment_amount'] = $params['different_money'] > 0 ? $params['different_money'] : 0;
            if ($data['payment_amount'] == 0) {
                $data['order_status'] = 1;
            }
            //换货、补发订单，子订单号统一为VHG+原单号，后续-1-2-3....-lf
            $data['old_sub_order_no'] = $orderInfo['sub_order_no'];
            if ($orderInfo['order_type'] < 9) {
                //快递方式处理
                $calculateData               = array(
                    'items_info'        => [[
                        'period'              => intval($data['period']),
                        'periods_type'        => intval($data['order_type']),
                        'package_id'          => intval($data['package_id']),
                        'nums'                => intval($data['order_qty']),
                        'express_type'        => 0,
                        'predict_time'        => date('Y-m-d,H:i:s', $data['predict_time']),
                        'is_ts'               => 0,
                        'is_original_package' => isset($params['is_original_package']) ? $params['is_original_package'] : 0,
                    ]],
                    'coupon_id'         => 0,
                    'is_use_coupon'     => 0,
                    'province_id'       => intval($data['province_id']),
                    'district_id'       => intval($data['district_id']),
                    'express_coupon_id' => 0,
                    'submit_type'       => intval($data['order_type']),
                    'city_id'           => intval($data['city_id']),
                    'special_type'      => 0
                );
                $header                      = [
                    "content-type: application/json",
                    "vinehoo-client: orders",
                    "vinehoo-uid:" . $data['uid']
                ];
                $countMoney                  = httpPostString(env('ITEM.CALC-ORDERS_PRICE') . '/ordersPrice/v3/order/calculateOrderMoney', json_encode($calculateData), $header);
                $data['express_type']        = isset($countMoney['data']['items_info'][0]['express_type']) ? $countMoney['data']['items_info'][0]['express_type'] : 5;
                $data['is_original_package'] = isset($params['is_original_package']) ? $params['is_original_package'] : 0;
            }
            Db::startTrans();
            try {
                $order_no      = $afterSalesModel->createOrder($data);
                $main_order_no = $order_no['main_order_no'];
                if (empty($main_order_no)) $this->throwError('创建订单失败');
                //日志记录
                $log = array(
                    'main_order_no'       => $main_order_no,
                    'create_param'        => '',
                    'stock_param'         => $stock_param,
                    'dec_stock_status'    => 1,
                    'timeout_deal_status' => json_encode(['stock' => 0, 'coupon' => 1]),
                    'notify_deal_status'  => json_encode(['order' => 0, 'invoice' => 1, 'group' => 1, 'fullgift' => 1, 'wms' => $orderInfo['order_type'] == 9 ? 1 : 0]),
                    'created_time'        => time()
                );
                //不需要支付的订单处理
                if ($data['payment_amount'] == 0) {
                    $log['timeout_deal_status'] = json_encode(['stock' => 1, 'coupon' => 1]);
                    $log['notify_deal_status']  = json_encode(['order' => 1, 'invoice' => 1, 'group' => 1, 'fullgift' => 1, 'wms' => 1]);
                }
                $addLog = Db::name('order_deal_log')->insert($log);
                if (empty($addLog)) $this->throwError('日志写入失败');
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                if (isset($stock_param)) {
                    //退还库存
                    httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', $stock_param);
                }
                $this->throwError($e->getMessage());
            }
            //不需要支付的订单处理
            if ($data['payment_amount'] == 0 && $orderInfo['order_type'] != 9) {
                //萌牙推送处理队列
                $pushData = array(
                    'exchange_name' => 'orders',
                    'routing_key'   => 'push.wms',
                    'data'          => base64_encode(json_encode(['sub_order_no' => $order_no['sub_order_no'], 'order_type' => $orderInfo['order_type']])),
                );
                Log::write("订单推送WMS队列 2: " . json_encode($pushData));
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
            } else {
                //添加超时任务
                $pushData   = array(
                    'namespace' => 'orders',
                    'key'       => $main_order_no,
                    'data'      => base64_encode(json_encode(['main_order_no' => $main_order_no, 'order_type' => $orderInfo['order_type']])),
                    'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/order/timeOutOrderDeal',
                    'timeout'   => env('ORDERS.AFTER_SALES_ORDER_TIMEOUT'),
                );
                $addTimeOut = $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
                //任务日志
                $taskLog = '换货、补发超时任务日志：' . $main_order_no . '请求参数：' . json_encode($pushData, JSON_UNESCAPED_UNICODE) . '返回参数：' . json_encode($addTimeOut, JSON_UNESCAPED_UNICODE);
                file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'timingLog' . '.log', $taskLog . PHP_EOL, FILE_APPEND);
                if (!isset($addTimeOut['error_code']) || $addTimeOut['error_code'] != 0) $this->throwError('订单创建超时任务失败');
            }
            $backParams = array(
                'main_order_no' => $main_order_no,
                'sub_order_no'  => $order_no['sub_order_no']
            );
        }
        return $backParams;
    }

    /**
     * Description:订单自动退款
     * Author: zrc
     * Date: 2022/6/16
     * Time: 16:08
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException订单自动退款
     */
    public function orderAutomaticRefund($requestparams)
    {
        $params = $requestparams;
        if (!in_array($params['order_type'], [0, 1, 2, 3, 9])) $this->throwError('当前订单不支持自动退款');
        $order_type = config('config')['order_type'];//订单频道获取
        //获取订单信息
        $field = 'so.main_order_id,so.uid,so.refund_status,so.sub_order_no,so.package_id,so.payment_amount,so.cash_amount,so.bonus_balance,so.recharge_balance,so.refund_money,so.refund_bonus_balance,so.refund_recharge_balance,so.main_order_id,so.order_type,so.warehouse_code,om.main_order_no,om.payment_method,om.payment_subject';
        if($params['order_type'] == 2){
            $field .= ',so.payment_doc';
        }else{
            $field .= ',so.push_wms_status';
        }

        $orderInfo = Db::name($order_type[intval($params['order_type'])]['table'])
            ->alias('so')
            ->field($field)
            ->join('order_main om', 'om.id = so.main_order_id')
            ->where(['so.sub_order_no' => $params['sub_order_no']])
            ->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['refund_status'] == 2) $this->throwError('订单已退款，请勿重复操作');

        if ($params['order_type'] == 2) {
            if ($orderInfo['payment_doc'] == 1) $this->throwError('订单已向海关申报，请人工核实再手动退款');
        } else {
            if ($orderInfo['push_wms_status'] == 1) {//发货仓推送成功
                //获取仓库编码配置
                $config_warehouse = config('config')['warehouse'];
                $wms_id           = 1;
                foreach ($config_warehouse as &$val) {
                    if (in_array($orderInfo['warehouse_code'], explode(',', $val['code']))) $wms_id = $val['value'];
                }
                if ($wms_id == 2) {
                    $this->throwError('订单已推送前置仓，请人工核实再手动退款');
                }
            }
        }
        Db::startTrans();
        try {
            if ($params['order_type'] != 2) {
                //发货仓撤单
                if (isset($orderInfo['push_wms_status']) && $orderInfo['push_wms_status'] == 1) {
                    $revokeOrder = $this->httpPost(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order', ['sub_order_no' => $params['sub_order_no']]);
//                    if (!isset($revokeOrder['error_code'])) $this->throwError('发货仓撤单失败，请人工核实处理后退款');
//                    if ($revokeOrder['error_code'] != 0) $this->throwError('发货仓撤单失败:' . $revokeOrder['error_msg'] . '，请人工核实处理后退款');
                } else {//预防推送状态异常撤单
                    curlRequest(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order', json_encode(['sub_order_no' => $params['sub_order_no']]), [], 'POST', 5);
                }
            }
            $refundOrder = Db::name('refund_order')->where(['main_order_no' => $orderInfo['main_order_no'], 'sub_order_no' => $params['sub_order_no']])->count();
            if ($refundOrder > 0) $this->throwError('订单已退款，请勿重复操作');
            //生成退款订单
            $refund_order_no = creatOrderNo(env('ORDERS.REFUND'), $orderInfo['uid']);
            $refund_amount = isset($params['refund_money']) ? $params['refund_money'] : bcsub($orderInfo['cash_amount'], $orderInfo['refund_money'], 2);
            $refund_bonus_balance = bcsub($orderInfo['bonus_balance'], $orderInfo['refund_bonus_balance'], 2);
            $refund_recharge_balance = bcsub($orderInfo['recharge_balance'], $orderInfo['refund_recharge_balance'], 2);


            $insertData = array(
                'refund_order_no' => $refund_order_no,
                'main_order_no' => $orderInfo['main_order_no'],
                'refund_status' => 0,
                'sub_order_no' => $params['sub_order_no'],
                'payment_method' => $orderInfo['payment_method'],
                'payment_subject' => $orderInfo['payment_subject'],
                'refund_amount' => $refund_amount,
                'created_time' => time(),
            );
            $addRefund = Db::name('refund_order')->insert($insertData);
            if (empty($addRefund)) $this->throwError('生成退款订单失败');
            if ($refund_amount > 0) {
                if (in_array($orderInfo['payment_subject'], [1, 2])) {
                    //发起银联退款
                    $pushData = array(
                        'main_order_no' => $orderInfo['payment_method'] . $orderInfo['main_order_no'],//支付方式+主订单号为银联交易订单号
                        'payment_method' => $orderInfo['payment_method'],
                        'refund_amount' => isset($params['refund_money']) ? $params['refund_money'] : $orderInfo['cash_amount'],
                        'refund_order_no' => $refund_order_no,
                        'subject' => $orderInfo['payment_subject'],
                        'is_cross' => $orderInfo['order_type'] == 2 ? 1 : 0,
                        'order_type' => $orderInfo['order_type'],//子订单单个退传本身订单类型
                        'sub_order_no' => $params['sub_order_no']
                    );
                    $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/refund', $pushData);
                    if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) $this->throwError('发起退款失败：' . $orderRefund['error_msg']);
                } elseif (in_array($orderInfo['payment_subject'], [3, 4])) {
                    //支付宝微信退款
                    $method = 'alipay';
                    if (in_array($orderInfo['payment_method'], [3, 4, 5, 7, 8, 9])) $method = 'wechat';
                    $pushData = array(
                        'main_order_no' => $orderInfo['main_order_no'],
                        'refund_order_no' => $refund_order_no,
                        'payment_method' => $orderInfo['payment_method'],
                        'method' => $method,
                        'payment_amount' => $orderInfo['payment_amount'],
                        'refund_amount' => isset($params['refund_money']) ? $params['refund_money'] : $orderInfo['payment_amount'],
                        'refund_desc' => '申请售后急速退款'
                    );
                    $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/refund', $pushData);
                    Log::write("支付宝微信发起退款记录: " . env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/refund   ' . json_encode($pushData) . '   ' . json_encode($orderRefund));
                    if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) {
                        if (empty($orderRefund['error_msg'])) {
                            if ($method == "alipay" && $orderRefund['data']['code'] != "10000") {
                                $msg = '支付宝退款申请失败：' . $orderRefund['data']['sub_msg'];
                            } else {
                                $msg = '微信退款申请失败：' . $orderRefund['data']['err_code_des'];
                            }
                        } else {
                            $msg = $orderRefund['error_msg'];
                        }
                        $this->throwError($msg);
                    }
                } elseif (in_array($orderInfo['payment_subject'], [11])) {
                    $orderRefund = \Curl::kitSubRefund([
                        'main_order_no' => $orderInfo['main_order_no'],
                        'payment_method' => $orderInfo['payment_method'],
                        'refund_amount' => isset($params['refund_money']) ? $params['refund_money'] : $orderInfo['payment_amount'],
                        'refund_order_no' => $refund_order_no,
                        'sub_order_no' => $params['sub_order_no'],
                        'order_type' => $orderInfo['order_type'],//子订单单个退传本身订单类型
                    ]);
                    if ($orderRefund === false) {
                        $this->throwError('华为退款申请失败');
                    }
                } else {
                    $this->throwError('发起急速退款失败：不支持的支付主体: ' . ($orderInfo['payment_subject'] ?? ''));
                }
            }

            if (($refund_bonus_balance > 0) || ($refund_recharge_balance > 0)) {
                $balance_logs[] = [
                    'uid' => $orderInfo['uid'],
                    'main_order_no' => $orderInfo['main_order_no'],
                    'sub_order_no' => $orderInfo['sub_order_no'],
                    'recharge_balance' => $refund_recharge_balance,
                    'bonus_balance' => $refund_bonus_balance,
                    'created_time' => time(),
                    'updated_time' => time(),
                    'remark' => '订单自动退款退还余额',
                    'type' => 1,
                    'status' => 0,
                    'request' => json_encode([
                        'uid' => intval($orderInfo['uid']),
                        'type' => 1, // 增加
                        'related_no' => $orderInfo['main_order_no'],
                        'related_type' => 2, // 商品订单
                        'operation_type' => 5, // 商品购买
                        'recharge_balance' => floatval($refund_recharge_balance),
                        'bonus_balance' => floatval($refund_bonus_balance),
                        'change_time' => time(),
                        'change_name' => '系统',
                        'unique_code' => "REF" . $orderInfo['main_order_id'] . uniqid() . time()
                    ]),
                ];
                Db::name('balance_pay')->insertAll($balance_logs);
            }

            //修改订单状态
            $editOrder = Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->update([
                'refund_recharge_balance' => Db::raw('recharge_balance'),
                'refund_bonus_balance' => Db::raw('bonus_balance'),
                'sub_order_status' => 4, 'refund_status' => 2, 'update_time' => time()]);
            if (empty($editOrder)) $this->throwError('修改订单状态失败');
            //修改退款订单状态
            $updateRefund = Db::name('refund_order')->where(['refund_order_no' => $refund_order_no, 'main_order_no' => $orderInfo['main_order_no'], 'sub_order_no' => $params['sub_order_no']])->update(['refund_status' => 1, 'update_time' => time()]);
            if (empty($updateRefund)) $this->throwError('修改退款订单失败');
            //判断是否是订金订单，是处理订金记录状态
            $depositRecord = Db::name('deposit_inflation_record')->where(['sub_order_no' => $params['sub_order_no']])->find();
            if (!empty($depositRecord)) {
                Db::name('deposit_inflation_record')->where(['sub_order_no' => $params['sub_order_no']])->update(['status' => 3, 'update_time' => time()]);
            }
            $this->cancelGiftOrders($orderInfo['main_order_no']);
            Db::commit();
            $msg = '订单自动退款成功';
        } catch (\Exception $e) {
            Db::rollback();
            $msg = '订单售后自动退款失败:' . $e->getMessage();
            //添加订单备注
            $orderService = new OrderService();
            $remark       = array(
                'sub_order_no' => $params['sub_order_no'],
                'order_type'   => $params['order_type'],
                'content'      => $msg,
                'admin_id'     => 0
            );
            $orderService->createRemarks($remark);
            $this->throwError($msg, 1);
        }
        return $msg;
    }

    public function cancelGiftOrders($main_order_no)
    {
        $sub_orders = Es::name(Es::ORDERS)->where([
            ['main_order_no', '==', $main_order_no],
            ['is_gift', '==', 1],
            ['sub_order_status', '==', 1], //子订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
            ['is_delete', '==', 0],
        ])->select()->toArray();
        $order_type = config('config')['order_type'];//订单频道获取
        foreach ($sub_orders as $order) {
            Db::startTrans();
            try {
                $sub_order_refund_status = Db::name($order_type[intval($order['order_type'])]['table'])->where(['sub_order_no' => $order['sub_order_no']])->value('refund_status');
                if ($sub_order_refund_status == 2) throw new Exception('赠品订单已退款');

                curlRequest(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order', json_encode(['sub_order_no' => $order['sub_order_no']]), [], 'POST', 5);
                Db::name($order_type[intval($order['order_type'])]['table'])->where(['sub_order_no' => $order['sub_order_no']])->update(['sub_order_status' => 4, 'refund_status' => 2, 'update_time' => time()]);

                $user_name = \Curl::cryptionDeal([$order['consignee']])[$order['consignee']] ?? $order['nickname'];

                //创建完成的工单
                $main_data = [
                    'uid'                          => $order['uid'],
                    'work_order_no'                => generateTaskNo(),
                    'order_no'                     => $order['sub_order_no'],
                    'period'                       => $order['period'],
                    'pay_actual'                   => $order['payment_amount'],
                    'payment_method'               => $order['payment_method'],
                    'payment_subject'              => $order['payment_subject'],
                    'order_type'                   => $order['order_type'],
                    'sub_order_status'             => 4,
                    'gd_do_status'                 => 8,
                    'gd_status'                    => 4,
                    'end_type'                     => 0,
                    'work_order_type'              => 4,
                    'goods_status'                 => 1,
                    'description'                  => '产品订单取消或退款, 赠品订单自动取消',
                    'desc_img'                     => '',
                    'desc_video'                   => '',
                    'denial_reason'                => '',
                    'work_order_img'               => '',
                    'logistics_claims_img'         => '',
                    'is_application'               => '0',
                    'logistics_claims'             => '0',
                    'after_sales_reason'           => '产品订单取消或退款, 赠品订单自动取消',
                    'not_apply_reason'             => '产品订单取消或退款, 赠品订单自动取消',
                    'customer_service_description' => '产品订单取消或退款, 赠品订单自动取消',
                    'createdor'                    => '系统',
                    'created_id'                   => 0,
                    'work_order_manage'            => '系统',
                    'work_order_manage_uid'        => 0,
                    'emergency'                    => 1,
                    'refund_no'                    => '',
                    'created_time'                 => time(),
                    'handle_time'                  => time(),
                    'handle_end_time'              => time(),
                    'end_time'                     => time(),
                    'is_fast_refund'               => 1,
                    'deposit_no'                   => '',
                    'handle_finish_remark'         => '',
                    'claim_amount'                 => null,
                    'claim_time'                   => null,
                    'claim_img'                    => '',
                    'deposit_amount'               => 0.00,
                ];
                $gd_id     = Db::table('vh_customer_service.vh_work_order')->insertGetId($main_data);
                if (!$gd_id) throw new Exception('创建主工单失败!');

                $sub_data = [
                    'gd_id'               => $gd_id,
                    'baosun_num'          => null,
                    'is_return_stock'     => 1,
                    'period'              => $order['period'],
                    'return_reason'       => '产品订单取消或退款, 赠品订单自动取消',
                    'return_money'        => $order['payment_amount'],
                    'customer_name'       => $user_name,
                    'ts_time'             => '0',
                    'ts_money'            => '0',
                    'is_automatic_refund' => '1',
                    'is_return_coupon'    => '1',
                    'coupon_id'           => '0',
                    'main_order_no'       => $order['main_order_no'],
                ];
                $sub_id   = Db::table('vh_customer_service.vh_refunds')->insertGetId($sub_data);
                if (!$sub_id) throw new Exception('创建子工单失败!');

                Log::write('取消赠品订单: SUCCESS: ' . $order['sub_order_no']);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                Log::write('取消赠品订单: ERROR: ' . ($order['sub_order_no'] ?? json_encode($order)));
            }
        }
    }

    /**
     * Description:拍卖订单自动退款
     * Author: zrc
     * Date: 2023/3/16
     * Time: 15:36
     * @param $params
     * @return string
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function orderAutomaticRefundAuction($params)
    {
        $params['order_no'] = $params['sub_order_no'];
        $orderInfo          = Db::table('vh_auction.vh_orders')->where(['order_no' => $params['order_no']])->find();
        Db::startTrans();
        try {
            if (empty($orderInfo)) $this->throwError('未获取到订单信息');
            if (!in_array($orderInfo['order_status'], [1, 2, 3])) $this->throwError('订单未支付，不能退款');
            if ($orderInfo['refund_status'] == 2) $this->throwError('订单已退款，请勿重复操作');
            //发货仓撤单
            if (isset($orderInfo['push_wms_status']) && $orderInfo['push_wms_status'] == 1) {
                $revokeOrder = $this->httpPost(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order', ['sub_order_no' => $params['order_no']]);
                if (!isset($revokeOrder['error_code'])) $this->throwError('发货仓撤单失败，请人工核实处理后退款');
                if ($revokeOrder['error_code'] != 0) $this->throwError('发货仓撤单失败:' . $revokeOrder['error_msg'] . '，请人工核实处理后退款');
            } else {//预防推送状态异常撤单
                curlRequest(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order', json_encode(['sub_order_no' => $params['sub_order_no']]), [], 'POST', 5);
            }
            $refund_order_no = 'TK' . date('Ymd') . time();
            $after_sales     = Db::table('vh_auction.vh_order_after_sales')->field('refund_order_no')->where(['order_no' => $params['order_no']])->find();
            if (empty($after_sales)) {
                //添加售后记录
                $data   = array(
                    'refund_order_no' => $refund_order_no,
                    'order_no'        => $params['order_no'],
                    'order_money'     => $orderInfo['payment_amount'],
                    'goods_id'        => $orderInfo['goods_id'],
                    'goods_name'      => $orderInfo['goods_name'],
                    'goods_img'       => $orderInfo['goods_img'],
                    'uid'             => $orderInfo['uid'],
                    'nickname'        => $orderInfo['nickname'],
                    'seller_uid'      => $orderInfo['seller_uid'],
                    'service_type'    => 0,
                    'refund_money'    => $orderInfo['payment_amount'],
                    'refund_reason'   => '工单系统极速退款',
                    'describe'        => '工单系统极速退款',
                    'created_time'    => time(),
                    'status'          => 3,
                    'refund_time'     => time(),
                );
                $result = Db::table('vh_auction.vh_order_after_sales')->insert($data);
                if (empty($result)) $this->throwError('添加售后记录失败');
            } else {//已有售后记录使用已生成的退款单号
                $refund_order_no = $after_sales['refund_order_no'];
            }
            //修改订单
            $updateData  = array(
                'order_status'    => 4,
                'refund_status'   => 2,
                'refund_order_no' => $refund_order_no,
                'update_time'     => time(),
            );
            $updateOrder = Db::table('vh_auction.vh_orders')->where(['order_no' => $params['order_no']])->update($updateData);
            if (empty($updateOrder)) $this->throwError('修改订单状态失败');
            //添加退款记录
            $refundData        = array(
                'refund_order_no' => $refund_order_no,
                'refund_status'   => 1,
                'order_no'        => $params['order_no'],
                'payment_method'  => $orderInfo['payment_method'],
                'payment_subject' => $orderInfo['payment_subject'],
                'refund_amount'   => $orderInfo['payment_amount'],
                'operator'        => isset($params['operator']) ? $params['operator'] : 0,
                'created_time'    => time(),
            );
            $add_refund_record = Db::table('vh_auction.vh_order_refund')->insert($refundData);
            if (empty($add_refund_record)) $this->throwError('添加退款记录失败');
            //支付宝微信退款
            $method = 'alipay';
            if (in_array($orderInfo['payment_method'], [3, 4, 5, 7, 8, 9])) $method = 'wechat';
            $pushData    = array(
                'source'          => 2,
                'main_order_no'   => $params['order_no'],
                'refund_order_no' => $refund_order_no,
                'payment_method'  => $orderInfo['payment_method'],
                'method'          => $method,
                'payment_amount'  => $orderInfo['payment_amount'],
                'refund_amount'   => $orderInfo['payment_amount'],
                'refund_desc'     => '订单退款',
                'status'          => 3,
            );
            $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/refund', $pushData);
            if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) {
                if (empty($orderRefund['error_msg'])) {
                    if ($method == "alipay" && $orderRefund['data']['code'] != "10000") {
                        $msg = '支付宝退款申请失败：' . $orderRefund['data']['sub_msg'];
                    } else {
                        $msg = '微信退款申请失败：' . $orderRefund['data']['err_code_des'];
                    }
                } else {
                    $msg = $orderRefund['error_msg'];
                }
                $this->throwError($msg);
            }
            Db::commit();
            $msg = '订单自动退款成功';
        } catch (\Exception $e) {
            Db::rollback();
            $msg = '订单售后自动退款失败:' . $e->getMessage();
            //添加订单备注
            $remark = array(
                'order_no'     => $params['order_no'],
                'admin_id'     => 0,
                'remarks'      => $msg,
                'created_time' => time()
            );
            Db::table('vh_auction.vh_order_remarks')->insert($remark);
            $this->throwError($msg, 1);
        }
        return $msg;
    }

    /**
     * Description:手动创建订单
     * Author: zrc
     * Date: 2022/5/10
     * Time: 15:15
     * @param $requestparams
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function manualCreateOrder($requestparams)
    {
        $params = $requestparams;
        //查询订单信息
        $config_order_type = config('config')['order_type'];//订单频道获取
        $orderInfo         = Db::name($config_order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        switch ($params['type']) {
            case 1:
                if ($orderInfo['express_type'] == 3) {
                    $orderInfo['express_type'] = 31;
                }
                //修改快递方式创建补差价订单
                $data   = array(
                    'operator'           => $params['admin_id'],
                    'type'               => 1,
                    'order_info'         => $orderInfo,
                    'express_type'       => $params['express_type'],
                    'freight_difference' => $params['freight_difference'],
                    'work_order_id'      => isset($params['work_order_id']) ? $params['work_order_id'] : 0,
                );
                $result = $this->createOrder($data);
                break;
            case 2:
                //修改收货信息创建补差价订单
                $data   = array(
                    'operator'           => $params['admin_id'],
                    'type'               => 1,
                    'order_info'         => $orderInfo,
                    'consignee'          => $params['consignee'],
                    'consignee_phone'    => $params['consignee_phone'],
                    'province_id'        => $params['province_id'],
                    'city_id'            => $params['city_id'],
                    'district_id'        => $params['district_id'],
                    'address'            => $params['address'],
                    'freight_difference' => $params['freight_difference'],
                    'work_order_id'      => $params['work_order_id'],
                );
                $result = $this->createOrder($data);
                break;
            case 3:
                //换货/补发创建订单
                $data   = array(
                    'operator'        => $params['admin_id'],
                    'type'            => 2,
                    'order_info'      => $orderInfo,
                    'consignee'       => $params['consignee'],
                    'consignee_phone' => $params['consignee_phone'],
                    'province_id'     => $params['province_id'],
                    'city_id'         => $params['city_id'],
                    'district_id'     => $params['district_id'],
                    'address'         => $params['address'],
                    'new_package_id'  => $params['new_package_id'],
                    'nums'            => $params['nums'],
                    'different_money' => $params['different_money'],
                    'work_order_id'   => $params['work_order_id'],
                    'erp_push_amount' => $params['erp_push_amount'],
                );
                $result = $this->createOrder($data);
                break;
        }
        return $result;
    }

    /**
     * Description:创建工单记录售后信息(累计工单数，标记售后订单)
     * Author: zrc
     * Date: 2022/5/13
     * Time: 15:27
     * @param $requestparams
     * @return int
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addAfterSalesLog($requestparams)
    {
        $params = $requestparams;
        //查询订单信息
        $config_order_type = config('config')['order_type'];//订单频道获取
        $orderInfo         = Db::name($config_order_type[intval($params['order_type'])]['table'])
            ->field('sub_order_no,work_nums,is_after_sale')
            ->where(['sub_order_no' => $params['sub_order_no']])
            ->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        $updateData = array(
            'work_nums'   => $orderInfo['work_nums'] + 1,
            'update_time' => time(),
        );
        if (in_array($params['type'], [4, 5, 6])) $updateData['is_after_sale'] = 1;
        $result = Db::name($config_order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->update($updateData);
        return $result;
    }

    /**
     * Description:拍卖订单创建工单记录售后信息(累计工单数，标记售后订单)
     * Author: zrc
     * Date: 2023/3/16
     * Time: 16:08
     * @param $params
     * @return int
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function addAfterSalesLogAuction($params)
    {
        $orderInfo = Db::table('vh_auction.vh_orders')->field('order_no,work_nums,is_after_sale')->where(['order_no' => $params['sub_order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        $updateData = array(
            'work_nums'   => $orderInfo['work_nums'] + 1,
            'update_time' => time(),
        );
        if (in_array($params['type'], [4, 5, 6])) $updateData['is_after_sale'] = 1;
        $result = Db::table('vh_auction.vh_orders')->where(['order_no' => $params['sub_order_no']])->update($updateData);
        return $result;
    }
}