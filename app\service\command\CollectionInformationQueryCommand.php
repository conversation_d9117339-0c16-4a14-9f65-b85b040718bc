<?php


namespace app\service\command;


use app\service\PaymentStatistics;
use think\facade\Db;

class CollectionInformationQueryCommand
{
    public function exec()
    {
        try {
            echo '每日数据同步开始！' . PHP_EOL;
            $service = new PaymentStatistics();
            $service->syncRedisToDatabase();
            echo '每日数据同步完成！' . PHP_EOL;
        } catch (\Exception $e) {
            \think\facade\Log::error('每日收款统计数据同步失败: ' . $e->getMessage());
        }

        //查询中台制单数据
        $where     = array(
            ['settlement_status', '<', 3],
            ['document_type', '=', 1],
            ['corp', '=', '001'],
            ['push_t_status', '=', 1],
        );
        $orderInfo = Db::name('offline_order')->field('id,sub_order_no,corp,payment_amount')->where($where)->limit(1000)->order('id desc')->select()->toArray();
        if (count($orderInfo) > 0) {
            $pushData = [];
            foreach ($orderInfo as &$val) {
                if ($val['payment_amount'] == 0) {
                    Db::name('offline_order')->where(['id' => $val['id']])->update(['settlement_status' => 4, 'update_time' => time()]);
                } else {
                    $pushData[] = array(
                        'sub_order_no' => $val['sub_order_no'],
                        'corp'         => $val['corp'],
                    );
                }
            }
            $result = curlRequest(env('ITEM.ERP_URL') . '/erp/v3/saleOrder/settlement', json_encode($pushData), [], 'POST');
            if (!isset($result['error_code'])) {
                echo 'erp模块访问异常！' . PHP_EOL;
                return false;
            }
            if ($result['error_code'] != 0) {
                echo '查询订单收款信息失败：' . $result['error_msg'] . PHP_EOL;
                return false;
            }
            if (count($result['data']) > 0) {
                foreach ($result['data'] as $v) {
                    $updateData = array(
                        'settlement_money'  => $v['settlement_money'],
                        'settlement_time'   => time(),
                        'settlement_status' => $v['settlement_status'],
                    );
                    Db::name('offline_order')->where(['sub_order_no' => $v['sub_order_no']])->update($updateData);
                }
            }
        }
        echo '执行完成！' . PHP_EOL;
        return true;
    }
}