<?php


namespace app\service;


use app\BaseService;
use app\service\elasticsearch\ElasticSearchService;
use app\service\Notify as NotifyService;
use app\service\Push as PushService;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

class WeChat extends BaseService
{
    /**
     * Description:新增销售单创建审批
     * Author: zrc
     * Date: 2022/10/8
     * Time: 10:54
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function ordinarySaleOrderCreateVerify($params)
    {
        try {
        //日志记录
        file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'saleOrderLog' . '.log', json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND);
        //获取发起人信息
        $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
        if ($userInfo['error_code'] != 0 || !isset($userInfo['data'][$params['admin_id']])) throw new Exception('发起企业微信批失败：未获取到企业微信用户信息');
        $userid = $userInfo['data'][$params['admin_id']]['userid'];//企业微信userid
        //客户名称审批字段匹配处理
        $params['customerApproval'] = $params['customer'];
        $customer                   = env('ORDERS.CUSTOMER');
        $customerArr                = explode(',', $customer);
        if (!in_array($params['customer'], $customerArr)) $params['customerApproval'] = '其他';
        //审批表单数据处理
        $goodsInfo  = [];
        $totalMoney = 0;
        //商品详情处理
        foreach ($params['items_info'] as $key => $val) {
            $goodsInfo[$key] = array(
                array(
                    'name'  => '简码',
                    'value' => strval($val['short_code']),
                ),
                array(
                    'name'  => '商品名称',
                    'value' => $val['product_name'],
                ),
                array(
                    'name'  => '条码',
                    'value' => strval($val['bar_code']),
                ),
                array(
                    'name'  => '年份',
                    'value' => strval($val['year']),
                ),
                array(
                    'name'  => '协议价',
//                    'value' => $val['agreementPrice'] == 0 ? '0' : strval($val['agreementPrice']),
                    'value' => '',
                ),
                array(
                    'name'  => '含税单价',
                    'value' => $val['price'] == 0 ? '0' : strval($val['price']),
                ),
                array(
                    'name'  => '规格型号',
                    'value' => strval($val['unit']),
                ),
                array(
                    'name'  => '数量',
                    'value' => strval($val['nums']),
                ),
                array(
                    'name'  => '是否赠品',
                    'value' => $val['is_gift'] == 1 ? '是' : "否",
                ),
            );
            //合计金额处理
            $totalMoney += $val['total_price'];
        }
        //物料明细处理
        $materialInfo = [];
        if (!empty($params['material_info'])) {
            foreach ($params['material_info'] as $k => $v) {
                $materialInfo[$k] = array(
                    array(
                        'name'  => '简码',
                        'value' => strval($v['short_code']),
                    ),
                    array(
                        'name'  => '商品名称',
                        'value' => $v['product_name'],
                    ),
                    array(
                        'name'  => '数量',
                        'value' => strval($v['nums']),
                    ),
                );
            }
        }
        //附件处理
        $media_id_arr = [];
        if (isset($params['media_ids']) && !empty($params['media_ids'])) {
            $media_id = explode(',', $params['media_ids']);
            foreach ($media_id as &$v) {
                $media_id_arr[] = ['fileId' => $v];
            }
        }
        //协议价类型处理
        $priceSource = '未知';
        switch ($params['priceSource']) {
            case 0:
                $priceSource = '未知';
                break;
            case 1:
                $priceSource = '客户价';
                break;
            case 2:
                $priceSource = '批发价';
                break;
        }
        //是否萌牙发货处理
        $is_push_wms = '是';
        switch ($params['is_push_wms']) {
            case 0:
                $is_push_wms = '否';
                break;
            case 1:
                $is_push_wms = '是';
                break;
        }
        $form_component_values = array(
            array(
                'name'  => '模板ID',
                'value' => $params['order_no'],
            ),
            array(
                'name'  => '业务部门',
                'value' => isset($params['department']) ? $params['department'] : '',
            ),
            array(
                'name'  => '运输方式',
                'value' => $params['delivery_mode'],
            ),
            array(
                'name'  => '送货地址',
                'value' => ($params['province_name'] ?? '').($params['city_name'] ?? '').($params['district_name'] ?? '').$params['address'],
            ),
            array(
                'name'  => '收件人',
                'value' => $params['consignee'],
            ),
            array(
                'name'  => '收件人手机号',
                'value' => $params['consignee_phone'],
            ),
            array(
                'name'  => '仓库',
                'value' => $params['warehouse'],
            ),
            array(
                'name'  => '仓库编码',
                'value' => $params['warehouse_code'],
            ),
            array(
                'name'  => '有无赠品',
                'value' => $params['is_have_gift'],
            ),
            array(
                'name'  => '审批字段',
                'value' => $params['customerApproval'],
            ),
            array(
                'name'  => '客户名称',
                'value' => $params['customer'],
            ),
            array(
                'name'  => '收款方式',
                'value' => isset($params['settlement_method']) ? $params['settlement_method'] : '',
            ),
            array(
                'name'  => '业务员',
                'value' => $params['clerk'],
            ),
            array(
                'name'  => '数据来源',
                'value' => '线下业务',
            ),
            array(
                'name'  => '合计金额',
                'value' => $totalMoney == 0 ? '0' : strval($totalMoney),
            ),
            array(
                'name'  => '备注',
                'value' => isset($params['memo']) ? $params['memo'] : '',
            ),
            array(
                'name'  => '是否客情',
                'value' => $totalMoney == 0 ? '是' : '否',
            ),
            array(
                'name'  => '商品详情',
                'value' => $goodsInfo
            ),
            array(
                'name'  => '协议价类型',
                'value' => $priceSource,
            ),
            array(
                'name'  => '是否萌牙发货',
                'value' => $is_push_wms,
            ),
            array(
                'name'  => '单据日期',
                'value' => strtotime($params['voucher_date']),
            ),
        );
        if (!empty($media_id_arr)) {
            $form_component_values[] = array(
                'name'  => '附件',
                'value' => $media_id_arr
            );
        }
        if (!empty($materialInfo)) {
            $form_component_values[] = array(
                'name'  => '物料明细',
                'value' => $materialInfo
            );
        }
        if ($params['is_sample_liquor'] == 1) {//科技样酒
            $process_code = env('ORDERS.sample_liquor_verify_id_001');
            if ($params['corp'] == '515') {//兔子星球样酒
                $process_code = env('ORDERS.sample_liquor_verify_id_515', '3WMVrAzaEdQtYs6RQe87U6y2vUpzG7vwQMQkjo8C');
            } else if ($params['corp'] == '003') {//木兰朵样酒
                $process_code = env('ORDERS.sample_liquor_verify_id_003', '3WMVrAzaEebZMhBUR36v8BMjTeAbGuAbvkaA8nFj');
            }
            $form_component_values[] = [
                'name'  => '是否退回',
                'value' => $params['return_warehouse'] == 1 ? '是' : '否',
            ];
            $collection_type = [
                '013'    => '领用出库',
                '013-1'  => '样酒申请',
                '013-2'  => '客情领用',
                '013-3'  => '物料领用',
                '013-4'  => '酒会领用',
                '013-6'  => '市场活动',
                '013-7'  => '酒庄活动',
                '013-8'  => '培训用酒',
                '013-9'  => '品鉴用酒',
                '013-10'  => '调拨出库',
            ];
            $form_component_values[] = [
                'name'  => '领用类型',
                'value' => $params['collection_type'] . ' ' . ($collection_type[$params['collection_type']] ?? ''),
            ];

        } else if ($params['corp'] == '001') {//科技公司
            $process_code = env('ORDERS.ordinary_sale_order_verify_id_001');

            $approve_clerk_arr = Db::table('vh_supplychain.vh_staff')->where('dept_id', 'in', [62, 88])->column('realname');
            $approve_clerk     = "无";
            if (in_array($params['clerk'], $approve_clerk_arr)) {
                $approve_clerk = $params['clerk'];
            }
            $form_component_values[] = array(
                'name'  => '审核业务员',
                'value' => $approve_clerk
            );

            if (in_array(($params['settlement_method_code'] ?? ''), ["05", "06"])) {
                $form_component_values[] = array(
                    'name'  => '收款银行名称',
                    'value' => ($params['account_no'] ?? '') . "/" . ($params['bank_account_name'] ?? ''),
                );
            }
            $oo                      = Db::name('offline_order')->where('sub_order_no', $params['order_no'])->find();
            $form_component_values[] = array(
                'name'  => '业绩单',
                'value' => empty($oo['related_order_no']) ? "否" : "是",
            );
            $customer_id             = Db::table('vh_supplychain.vh_partner_entity')->where('code', $oo['customer_code'])->value('id');
            $collection_set          = Db::table('vh_supplychain.vh_partner_entity_collection_set')->where('peid', $customer_id)->find() ?? [];
            $hqyz = '一致';
            if (empty($collection_set)) {
                $hqyz = '与档案不一致';
            } else {
                if ($collection_set['customer_payment_way'] == 1) {
                    //现结
                    if (!in_array($oo['settlement_method_code'], ['05', '06'])) {
                        $hqyz = '与档案不一致';
                    }
                } elseif ($collection_set['customer_payment_way'] == 2) {
                    //月结
                    if (!in_array($oo['settlement_method_code'], ['03'])) {
                        $hqyz = '与档案不一致';
                    }
                } elseif ($collection_set['customer_payment_way'] == 3) {
                    //账期
                    if (!in_array($oo['settlement_method_code'], ['00'])) {
                        $hqyz = '与档案不一致';
                    } else {
                        $customer_payment_info = json_decode($collection_set['customer_payment_info'], true);
                        if ($customer_payment_info['days'] != $oo['settlement_days']) {
                            $hqyz = '与档案不一致';
                        }
                        if ($customer_payment_info['day_type'] != $oo['settlement_day_type']) {
                            $hqyz = '与档案不一致';
                        }
                        if ($customer_payment_info['sub_status'] != $oo['settlement_month_type']) {
                            $hqyz = '与档案不一致';
                        }

                    }
                }else{
                    $hqyz = '与档案不一致';
                }
            }
            $form_component_values[] = array(
                'name'  => '回款',
                'value' => $hqyz,
            );
            if ($hqyz != '一致') {
                $hthk       = $ddhk = "";
                $ddcode     = [
                    '05' => '现结/本月收款',
                    '06' => '现结/上月结余',
                    '03' => '月结',
                    '00' => '账期',
                ];
                $htcode     = [
                    '1' => '现结',
                    '2' => '月结',
                    '3' => '账期',
                ];
                $sub_status = [
                    '0' => '发货后',
                    '1' => '开票后',
                    '2' => '次月初',
                ];
                $day_type   = [
                    '0' => '次月月底',
                    '1' => '自然日',
                    '2' => '工作日',
                ];

                $ddhk .= $ddcode[$oo['settlement_method_code']] ?? '';
                $hthk .= $htcode[$collection_set['customer_payment_way'] ?? ''] ?? '';
                if (($collection_set['customer_payment_way'] ?? '') == 3 && $oo['settlement_method_code'] == '00') {
                    $cpi  = json_decode($collection_set['customer_payment_info'], true);
                    $ddhk .= '/' . ($sub_status[$oo['settlement_month_type']] ?? '') . '/' . ($day_type[$oo['settlement_day_type']] ?? '') . '/' . $oo['settlement_days'];
                    $hthk .= '/' . ($sub_status[($cpi['sub_status'] ?? '')] ?? '') . '/' . ($day_type[($cpi['day_type'] ?? '')] ?? '') . '/' . ($cpi['settlement_days'] ?? '');
                }

                $form_component_values[] = array(
                    'name'  => '合同回款',
                    'value' => $hthk,
                );
                $form_component_values[] = array(
                    'name'  => '订单回款',
                    'value' => $ddhk,
                );
            }
        } else if ($params['corp'] == '002') {//电子商务
            $process_code = env('ORDERS.ordinary_sale_order_verify_id_002');
        } else if ($params['corp'] == '515') {//兔子星球
            $process_code = env('ORDERS.ordinary_sale_order_verify_id_515');
        } else if ($params['corp'] == '003') {//木兰朵
            $process_code = env('ORDERS.ordinary_sale_order_verify_id_003','Zw31EPgjQL9Ue41dbXy3rEgAbaxkreDdA7bxYf');
        }
        $pushData = array(
            'form_component_values' => $form_component_values,
            'process_code'          => $process_code,
            'dept_id'               => 0,//部门传0默认获取自己主部门
            'originator_user_id'    => $userid
        );

//        print_r($pushData);die;


            //【新增】在发起审批前先验证审批是否已存在，避免重复发起审批
            $start_time    = time() - 86400;   // 审批创建时间范围的开始时间，10分钟前
            $end_time      = time();           // 当前时间为结束时间
            $existsPayload = [
                "start_time"   => $start_time,
                "end_time"     => $end_time,
//                "creator_uid"  => $userid,
                "template_id"  => $process_code,  // 审批模板ID（根据不同corp已经设置）
//                "department"   => 0,            // 此处默认0，可根据需要调整
                "unique_name"  => "模板ID",       // 唯一字段名称
                "unique_value" => $params['order_no']  // 使用bill_no作为唯一值
            ];
            $existsUrl     = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/exists';
            $existsResult  = httpPostString($existsUrl, json_encode($existsPayload, JSON_UNESCAPED_UNICODE),'',10);
            if (isset($existsResult['error_code']) && $existsResult['error_code'] == 0
                && isset($existsResult['exists']) && $existsResult['exists'] === true) {
                // 如果审批已存在，则直接返回成功，避免重复发起审批
                if (isset($params['old_dingtalk_status'])) {
                    if ($params['old_dingtalk_status'] == 0) $params['old_dingtalk_status'] = 1;
                    Db::name('offline_order')->where('sub_order_no', $params['order_no'])->update(['dingtalk_status' => $params['old_dingtalk_status']]);
                }
                return true;
            }

        $url = env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/create';
        $data_string = json_encode($pushData, JSON_UNESCAPED_UNICODE);
        $result   = httpPostString($url, $data_string);
        if ($result['error_code'] != 0) {
            Log::error($url . '调用失败：' . json_encode($result) . '调用参数：' . $data_string);
            throw new Exception('发起企业微信审批失败：' . $result['error_msg']);
        }

            try {
                $clerk_user   = Db::table('vh_authority.vh_admins')->where('realname', $params['clerk'])->value('userid');
                $oo           = Db::name('offline_order')->where('sub_order_no', $params['order_no'])->field('push_wms_status,is_ts')->find();
                $send_message = "销售单 {$params['order_no']} 已经发起企微审核，订单" . (($oo['push_wms_status'] == 3) ? '不' : '') . "需要萌牙发货，" . (($oo['is_ts'] == 1) ? '' : '没') . "有暂存；";
                \Curl::wecomSend($send_message, $clerk_user, 'text');
            } catch (\Exception $e) {
                Log::write('审批创建成功发送消息失败: ' . $params['order_no']);
            }
        return true;
        } catch (\Exception  $e) {
            if (!empty($params['order_no'])) Db::name('offline_order')->where('sub_order_no', $params['order_no'])->update(['dingtalk_status' => 3, 'update_time' => time()]);
            $this->throwError($e->getMessage());
        }
    }

    /**
     * Description:新增销售单审批回调处理
     * Author: zrc
     * Date: 2022/10/8
     * Time: 11:25
     * @param $params
     * @return int
     * @throws \think\db\exception\DbException
     */
    public function ordinarySaleOrderVerifyCallBack($requestparams)
    {
        Log::record('企业微信销售单审批日志', json_encode($requestparams));
        $params       = $requestparams['process_instance'];
        $sub_order_no = '';
        $skyh = '/'; //收款银行名称
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '模板ID') $sub_order_no = trim($val['value']);
                if ($val['name'] == '仓库编码') $warehouse_code = trim($val['value']);
                if ($val['name'] == '收款银行名称') $skyh = trim($val['value']);
            }
        }
        if (empty($sub_order_no) || empty($warehouse_code)) {
            $this->weChatSendText($params['originator_userid'], '未获取到订单号或仓库编码,审批流处理失败!');
            $this->throwError('未获取到订单号或仓库编码,审批流处理失败!');
        }
        //审批通过处理
        if ($params['result'] == 'agree') {
            $updateData    = array(
                'dingtalk_status' => 2,
                'approval_time'   => time(),
                'update_time'     => time()
            );
            $pushService   = new PushService();
            $offline_order = Db::name('offline_order')->field('id,delivery_time,push_wms_status,push_t_status,document_type,corp')->where(['sub_order_no' => $sub_order_no])->find();
            if ($offline_order['corp'] == '001' && $offline_order['document_type'] == 1) {
                $skyh = explode('/', $skyh);
                $updateData['bank_account_name'] = $skyh[1] ?? ''; // 收款银行名称
                $updateData['account_no'] = $skyh[0] ?? ''; //收款账号
            }
            //光环店/郎升仓/上海OT仓/北京OT仓/保税仓/佰酿美酒重庆办公室仓推erp
            if (in_array($warehouse_code, ['038', '035', '023', '024', '025', '188'])) {
                //推送erp
                $pushService = new PushService();
                $result      = $pushService->pushTplus(['sub_order_no' => $sub_order_no, 'order_type' => 8]);
            } else {
                //不推送萌牙的审批通过推erp
                if ($offline_order['push_wms_status'] == 3) {
                    if ($offline_order['push_t_status'] != 3) {
                        if ($offline_order['document_type'] == 0 && $offline_order['corp'] != '002') {//科技样酒销售单推送出库单
                            $result = $pushService->pushTplus(['sub_order_no' => $sub_order_no, 'order_type' => 8, 'corp' => $offline_order['corp']]);
                        } else {
                            $result = $pushService->pushTplus(['sub_order_no' => $sub_order_no, 'order_type' => 8]);
                        }
                    }
                } else {
                    $pushService = new PushService();
                    $result      = $pushService->pushWms(['sub_order_no' => $sub_order_no, 'order_type' => 8]);
                }
                //erp已推送或推送失败的，重推一次erp(弃审后需要重推)
                $isset_remarks = Db::name('order_remarks')->where([['sub_order_no', '=', $sub_order_no], ['remarks', 'like', "%erp推送%"]])->count();
                if ($isset_remarks > 0) {
                    if ($offline_order['document_type'] == 0 && $offline_order['corp'] != '002') {//科技样酒销售单推送出库单
                        $result = $pushService->pushTplus(['sub_order_no' => $sub_order_no, 'order_type' => 8, 'corp' => $offline_order['corp']]);
                    } else {
                        $result = $pushService->pushTplus(['sub_order_no' => $sub_order_no, 'order_type' => 8]);
                    }
                }
            }
            //推送失败发送企业微信中台消息
            if ($result['error_code'] != 0) {
                $userid  = $params['originator_userid'];
                $content = '#销售单推送异常提示：';
                $content .= "订单号：{$sub_order_no}，";
                $content .= "同步结果：失败，";
                $content .= "异常明细：{$result['error_msg']}。";
                $msgData = array(
                    'content' => $content,
                    'userid'  => $userid,
                    'msgtype' => 'text',
                    'agentid' => 0,
                );
                httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            }
            //更改订单钉钉审批状态
            if ($offline_order['push_wms_status'] == 3 && $offline_order['delivery_time'] <= 0) {
                $updateData['delivery_time'] = time();
            }
            Db::name('offline_order')->where(['sub_order_no' => $sub_order_no])->update($updateData);

            // 线下订单审批通过创建收款单
            pushQueue('orders', 'create_arap_ys_order', ['sub_order_no' => $sub_order_no]);

            try {
                $oo_info = Db::table('vh_orders.vh_offline_order')
                    ->where(['sub_order_no' => $sub_order_no])
                    ->field('id,sub_order_no,items_info,payment_amount,customer_code')->find();

                $oo_items = explode(',', $oo_info['items_info']);
                foreach ($oo_items as $oo_item) {
                    $oo_item                  = explode('*', $oo_item);
                    $short_codes[$oo_item[1]] = $oo_item[1];
                }

                $products = Db::table('vh_wiki.vh_products')->whereIn('short_code', array_values($short_codes))
                    ->column('cn_product_name', 'short_code');

                $oo_arr = Db::table('vh_supplychain.vh_pe_customer')->alias('t1')
                    ->join('vh_supplychain.vh_partner_entity t2', "t1.partner_entity_id = t2.id")
                    ->where('t2.code', $oo_info['customer_code'])
                    ->field('t1.id,t1.partner_entity_id,t1.total_amount')
                    ->find();

                $total_amount = bcadd($oo_arr['total_amount'], $oo_info['payment_amount'], 2);
                Db::table('vh_supplychain.vh_pe_customer')->where('id', $oo_arr['id'])->update(['total_amount' => $total_amount]);

                $has_sales = Db::table('vh_supplychain.vh_pe_customer_sales')
                    ->where('partner_entity_id', $oo_arr['partner_entity_id'])
                    ->where('short_code', 'in', array_keys($products))
                    ->column('id', 'short_code');

                foreach ($oo_items as $item) {
                    $item = explode('*', $item);
                    if (!empty($item)) {
                        $update = [
                            'partner_entity_id' => $oo_arr['partner_entity_id'],
                            'short_code'        => $item[1],
                            'name_cn'           => $products[$item[1]] ?? '',
                            'lately_unit_price' => $item[3],
                            'lately_nums'       => $item[2],
                            'sub_order_no'      => $oo_info['sub_order_no'],
                            'created_time'      => time(),
                            'update_time'       => time(),
                        ];
                        if (!empty($has_sales[$item[1]])) {
                            Db::table('vh_supplychain.vh_pe_customer_sales')->where('id', $has_sales[$item[1]])->update($update);
                        } else {
                            Db::table('vh_supplychain.vh_pe_customer_sales')->insert($update);
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::write('更新供应链客户数据 错误 ' . $e->getMessage());
            }
        }
        if ($params['result'] == 'refuse' || $params['status'] == 'TERMINATED') {
            $updateData = array(
                'dingtalk_status' => 3,
                'approval_time'   => time(),
                'update_time'     => time()
            );
            //更改订单钉钉审批状态
            Db::name('offline_order')->where(['sub_order_no' => $sub_order_no])->update($updateData);
        }
        //审批流处理
        $spNodeStatus          = $params['spNodeStatus'];
        $spNodeApprovers       = $params['spNodeApprovers'];
        $approval_process      = [];
        $approver              = "";
        $approval_process_info = Db::name('offline_order')->where(['sub_order_no' => $sub_order_no])->value('approval_process');
        $approval_processArr   = !empty($approval_process_info) ? json_decode($approval_process_info, true) : [];
        $change                = 0;//默认不更新审批节点
        foreach ($spNodeApprovers as &$v) {
            if (!empty($approval_processArr)) {
                foreach ($approval_processArr as &$vv) {
                    if ($vv['approver_id'] != $v) $change = 1;
                }
            } else {
                $change = 1;
            }
        }
        if ($change == 0) {
            foreach ($approval_processArr as $key => $val) {
                if ($val['status'] != $spNodeStatus[$key]) {
                    $approval_process[] = array(
                        'approver'    => $val['approver'],
                        'status'      => $spNodeStatus[$key],
                        'approver_id' => $val['approver_id'],
                        'check_time'  => $spNodeStatus[$key] > 1 ? date('Y-m-d H:i:s', time()) : '',
                    );
                } else {
                    $approval_process[] = array(
                        'approver'    => $val['approver'],
                        'status'      => $val['status'],
                        'approver_id' => $val['approver_id'],
                        'check_time'  => $val['check_time'],
                    );
                }
                if ($spNodeStatus[$key] > 1) $approver = $val['approver'];
            }
            $updateData = array(
                'approver'         => $approver,
                'approval_process' => json_encode($approval_process, JSON_UNESCAPED_UNICODE)
            );
        } else {
            foreach ($spNodeApprovers as $key => $val) {
                $uidArr      = explode('/', $val);
                $userNameArr = [];
                $userName    = '';
                foreach ($uidArr as &$v) {
                    $userInfo = $this->httpGet(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/contacts/userinfo/byuid', ['uid' => $v]);
                    if (isset($userInfo['data']['name'])) $userNameArr[] = $userInfo['data']['name'];
                }
                $userName           = implode('/', $userNameArr);
                $approval_process[] = array(
                    'approver'    => $userName,
                    'status'      => $spNodeStatus[$key],
                    'approver_id' => $val,
                    'check_time'  => $spNodeStatus[$key] > 1 ? date('Y-m-d H:i:s', time()) : '',
                );
                if ($spNodeStatus[$key] > 1) $approver = $userName;
            }
            $updateData = array(
                'approver'         => $approver,
                'approval_process' => json_encode($approval_process, JSON_UNESCAPED_UNICODE)
            );
        }
        //更改订单钉钉审批状态
        Db::name('offline_order')->where(['sub_order_no' => $sub_order_no])->update($updateData);
        return true;
    }

    /**
     * Description:企业微信发送消息
     * Author: zrc
     * Date: 2022/10/8
     * Time: 11:26
     * @param $userid
     * @param $content
     */
    public function weChatSendText($userid, $content)
    {
        $msgData = array(
            'content' => $content,
            'userid'  => $userid,
            'msgtype' => 'text',
            'agentid' => 0,
        );
        httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
    }

    /**
     * Description:预计发货时间修改审批回调处理
     * Author: zrc
     * Date: 2022/10/28
     * Time: 14:26
     * @param $requestparams
     * @return bool
     */
    public function predictTimeVerifyCallBack($requestparams)
    {
        Log::record('预计发货时间修改审批日志', json_encode($requestparams));
        $params        = $requestparams['process_instance'];
        $type          = 0;
        $periodOrderno = '';
        $predictTime   = '';
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '查询条件') {
                    if ($val['value'] == '根据期数') $type = 1;
                    if ($val['value'] == '根据子订单号') $type = 2;
                }
                if ($val['name'] == '期数/子订单号') {
                    $periodOrderno = explode(',', $val['value']);
                }
                if ($val['name'] == '新的预计发货日期') $predictTime = strtotime($val['value']) + 86399;
            }
        }
        //审批通过处理
        if ($params['result'] == 'agree') {
            $es         = new ElasticSearchService();
            $order_type = config('config')['order_type'];//订单频道获取
            foreach ($periodOrderno as &$val) {
                switch ($type) {
                    case 1:
                        //获取期数频道
                        $arr      = array(
                            'index'  => ['periods'],
                            'match'  => [['id' => $val]],
                            'source' => ['periods_type'],
                            'limit'  => 1
                        );
                        $esPeriod = $es->getDocumentList($arr);
                        if (!isset($esPeriod['data'][0]['periods_type'])) {
                            $this->weChatSendText($params['originator_userid'], $val . '修改预计发货时间失败!未获取到期数信息');
                            $this->throwError($val . '未获取到期数信息');
                        }
                        $periods_type = $esPeriod['data'][0]['periods_type'];
                        Db::name($order_type[intval($periods_type)]['table'])->where(['period' => $val, 'sub_order_status' => 1])->update(['predict_time' => $predictTime, 'update_time' => time()]);
                        break;
                    case 2:
                        //查询订单类型
                        $searchData  = array(
                            'sub_order_no' => $val,
                            'fields'       => 'order_type,sub_order_status'
                        );
                        $batchSearch = httpGet(env('ITEM.MYSQL_BATCH_SEARCH') . '/services/v3/batchsearch/orders/fields', $searchData);
                        if (!isset($batchSearch['error_code']) || $batchSearch['error_code'] != 0) {
                            $this->weChatSendText($params['originator_userid'], $val . '修改预计发货时间失败!未获取到订单信息');
                            $this->throwError($val . '修改预计发货时间失败!未获取到订单信息');
                        }
                        if ($batchSearch['data']['sub_order_status'] != 1) {
                            $this->weChatSendText($params['originator_userid'], $val . '修改预计发货时间失败!订单状态不是已支付');
                            $this->throwError($val . '修改预计发货时间失败!订单状态不是已支付');
                        }
                        Db::name($order_type[intval($batchSearch['data']['order_type'])]['table'])->where(['sub_order_no' => $val, 'sub_order_status' => 1])->update(['predict_time' => $predictTime, 'update_time' => time()]);
                        break;
                }
            }
        }
        return true;
    }

    /**
     * Description:销售退货批量导入创建审批
     * Author: zrc
     * Date: 2023/7/7
     * Time: 17:12
     * @param $params
     * @param $userid
     * @return bool
     * @throws \Exception
     */
    public function salesRetrunCreateVerify($params, $userid)
    {
        //退货明细处理
        $detail     = json_decode($params['detail_json'], true);
        $detailArr  = [];
        $totalMoney = 0;
        foreach ($detail as &$va) {
            $detailArr[] = array(
                array(
                    'name'  => '原单据号',
                    'value' => strval($va['sub_order_no']),
                ),
                array(
                    'name'  => '简码',
                    'value' => strval($va['short_code']),
                ),
                array(
                    'name'  => '数量',
                    'value' => strval($va['nums']),
                ),
                array(
                    'name'  => '总价',
                    'value' => strval($va['tax_total_price']),
                ),
            );
            $totalMoney  += $va['tax_total_price'];
        }
        $form_component_values = array(
            array(
                'name'  => '模板ID',
                'value' => $params['bill_no'],
            ),
            array(
                'name'  => '单据类型',
                'value' => $params['sale_bill_type'] == 1 ? '酒云线上' : ($params['sale_bill_type'] == 2 ? '线下销售' : '三方线上'),
            ),
            array(
                'name'  => '单据日期',
                'value' => date('Y-m-d', $params['bill_date']),
            ),
            array(
                'name'  => '业务部门',
                'value' => strval($params['department']),
            ),
            array(
                'name'  => '客户',
                'value' => strval($params['customer']),
            ),
            array(
                'name'  => '结算客户',
                'value' => strval($params['settle_customer']),
            ),
            array(
                'name'  => '业务员',
                'value' => strval($params['clerk']),
            ),
            array(
                'name'  => '退回仓库',
                'value' => strval($params['warehouse']),
            ),
            array(
                'name'  => '仓库编码',
                'value' => strval($params['warehouse_code']),
            ),
            array(
                'name'  => '合计金额',
                'value' => strval($totalMoney),
            ),
            array(
                'name'  => '退货明细',
                'value' => $detailArr,
            ),
        );
        //var_dump($form_component_values);exit;
        $process_code = env('ORDERS.sales_return_batch_import_id');
        $pushData     = array(
            'form_component_values' => $form_component_values,
            'process_code'          => $process_code,
            'dept_id'               => 0,//部门传0默认获取自己主部门
            'originator_user_id'    => $userid
        );
        $result       = httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/approval/create', json_encode($pushData, JSON_UNESCAPED_UNICODE));
        if ($result['error_code'] != 0) $this->throwError('发起企业微信审批失败：' . $result['error_msg']);
        return true;
    }

    /**
     * Description:对公转账-线下转账审批回调处理
     * Author: zrc
     * Date: 2023/8/16
     * Time: 17:54
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function offlineTransferVerifyCallBack($requestparams)
    {
        $params        = $requestparams['process_instance'];
        $main_order_no = '';
        $total_amount  = 0;
        $payment_subject = $attachment = '';
        foreach ($params['form_component_values'] as &$val) {
            if (isset($val['name'])) {
                if ($val['name'] == '主订单号') $main_order_no = trim($val['value']);
                if ($val['name'] == '支付金额') $total_amount = trim($val['value']);
                if ($val['name'] == '收款主体') $payment_subject = trim($val['value']);
                if ($val['name'] == '付款回执单附件') $attachment = trim($val['value']);
            }
        }
        if (empty($main_order_no) || empty($total_amount)) {
            $this->weChatSendText($params['originator_userid'], '未获取到订单号或支付金额,审批流处理失败!');
            $this->throwError('未获取到订单号或支付金额,审批流处理失败!');
        }
        //审批通过处理
        if ($params['result'] == 'agree') {
            $data          = array(
                'is_offline_transfer' => 1,
                'merOrderId'          => $main_order_no,
                'totalAmount'         => $total_amount,
                'seqId'               => '线下转账',
            );
            $notifyService = new NotifyService();
            $notifyService->notify(http_build_query($data));
            $transfer_status = 2;
        } elseif ($params['status'] == 'TERMINATED') {
            //撤销
            $transfer_status = 0;
        } elseif ($params['result'] == 'refuse') {
            //驳回
            $transfer_status = 3;
        }

        $transfer_log = Db::name('transfer_log')->where('main_order_no', $main_order_no)->find();
        if (isset($transfer_status) && !empty($transfer_log) && $transfer_log['status'] != 2) {
            Db::name('transfer_log')->where('id', $transfer_log['id'])->update([
                'status'      => $transfer_status,
                'update_time' => time(),
            ]);
        }
        return true;
    }

    //批量修改客户自动化审批回调处理
    public function changeStaffCallback($requestparams)
    {
        Log::record('批量修改客户自动化审批回调处理', json_encode($requestparams));
        $params = $requestparams['process_instance'];
        //审批通过处理
        if ($params['result'] == 'agree') {
            $notice_msg = "批量修改客户归属变更";
            $wx_files   = [];
            foreach ($params['form_component_values'] as &$val) {
                if (isset($val['name'])) {
                    if ($val['name'] == '附件') $wx_files = json_decode(trim($val['value']), true);
                }
            }
            $originator_userid = $params['originator_userid'];
            Db::startTrans();
            try {

                if (empty($wx_files)) {
                    throw new Exception('未获取到微信临时文件');
                }

                $download_path = app()->getRuntimePath() . 'download' . DIRECTORY_SEPARATOR;
                if (!is_dir($download_path)) {
                    if (!mkdir($download_path, 0755, true)) {
                        throw new Exception('创建附件目录失败,' . $download_path);
                    }
                }
                $up_data = [];
                foreach ($wx_files as $wx_file) {
                    $local_file      = \Curl::downloadTempFileToWechat($wx_file['fileId'], $download_path); // todo...
                    $local_file_name = basename($local_file);
                    $local_data      = getExcelData($local_file, '1')['data'];
                    $header          = $local_data[0] ?? [];
                    $header          = array_flip($header);
                    unset($local_data[0]);
                    $local_data = array_values($local_data);

                    if (!isset($header['公司编码'])) {
                        throw new Exception("文件 {$local_file_name} 公司编码列不存在!");
                    }
                    if (!isset($header['新分管人员'])) {
                        throw new Exception("文件 {$local_file_name} 新分管人员列不存在!");
                    }

                    $company_code = $header['公司编码'];
                    $staff_name   = $header['新分管人员'];

                    $line = 1;
                    foreach ($local_data as $local_row) {
                        if (empty($local_row[$company_code])) {
                            throw new Exception("文件 {$local_file_name}, 第{$line}行 公司编码 为空!");
                        }
                        if (empty($local_row[$staff_name])) {
                            throw new Exception("文件 {$local_file_name}, 第{$line}行 新分管人员 为空!");
                        }

                        $up_data[$local_row[$staff_name]][] = $local_row[$company_code];
                        $line++;
                    }
                    unlink($local_file);
                    unset($local_file);
                }

                $staffs = Db::table('vh_supplychain.vh_staff')->where('realname', 'in', array_keys($up_data))->column('id,dept_id,telephone', 'realname');
                if (count($staffs) != count($up_data)) {
                    throw new Exception('查询到的员工与传入数量不一致,请检查');
                }

                foreach ($up_data as $staff_name => $company_codes) {
                    Db::table('vh_supplychain.vh_partner_entity')->where('code', 'in', $company_codes)->update([
                        'maintainer_id'      => $staffs[$staff_name]['id'],
                        'department_id'      => $staffs[$staff_name]['dept_id'],
                        'maintainer_contact' => $staffs[$staff_name]['telephone'],
                    ]);
                }

                Db::commit();
                $notice_msg .= '成功';
            } catch (\Exception $e) {
                Db::rollback();
                if (isset($local_file)) {
                    unlink($local_file);
                }
                $notice_msg .= "失败: " . $e->getMessage();
            }

            \Curl::wecomSend($notice_msg, $originator_userid, 'text');
        }
        return true;
    }

}