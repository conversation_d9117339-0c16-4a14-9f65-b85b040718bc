<?php
declare (strict_types = 1);

namespace app\service\command;

use app\service\Order as OrderService;
use think\facade\Db;
use think\facade\Log;

class TripartiteOrderPushWMSCommand
{
    /**
     * Description:三方订单每天10：00到18：00自动推送（30分钟执行一次）
     * Author: zrc
     * Date: 2022/7/27
     * Time: 16:31
     * @return bool
     */
    public function exec()
    {
        $stime = strtotime(date('Y-m-d 10:00:00'));
        $etime = strtotime(date('Y-m-d 23:00:00'));
        if ($stime <= time() && time() <= $etime) {
            $dataStr          = [];
            $where            = [];
            $where[]          = ['t.push_wms_status', '=', 0];
//            $where[]          = ['push_wms_status', 'in', [0,2]];
            $where[]          = ['t.sub_order_status', 'in', [1]];
            $where[]          = ['t.refund_status', '=', 0];
            $where[]          = ['t.created_time', '>', strtotime('-7 days')];
            $where[]          = ['t.created_time', '<', time() - 30 * 60];
//            $where[] = ['store_id', 'not in', ['320552154', '650a60e17fa15200013acf16', '100620515',]]; //三方店铺 - 指定店铺暂时不推送萌牙
            $tripartite_orders = Db::name('tripartite_order')
                ->alias('t')
                ->leftJoin('order_main om','om.id=t.main_order_id')
                ->where($where)->column('om.main_order_no,t.sub_order_no,t.store_id');
            $tripartite_orders_group = array_chunk($tripartite_orders, 50);
            foreach ($tripartite_orders_group as $tripartite_order){
            Log::write("三方订单定时任务推送萌芽查询参数: " . json_encode($tripartite_order));
            //三方订单状态查询
            $data = [];
            foreach ($tripartite_order as &$v) {
                $data[] = array(
                    'main_order_no' => $v['main_order_no'],
                    'order_no' => $v['sub_order_no'],
                    'owner_id' => $v['store_id'],
                );
            }
            $orderStatusBatch = httpPostString(env('ITEM.ORDERS_MICRO_SERVICE_URL') . '/orders_server/v3/trilateral/get_trilateral_order_status', json_encode(['param' => $data]), '', 30);
            foreach ($tripartite_order as &$val) {
                //合并订单、赠品订单不验证三方订单状态
                $tripartite_order_status_search = (env("APP_DEBUG") === true) ? [] : config('config')['tripartite_order_status_search'];
                if (strpos($val['sub_order_no'], '-合') !== false || strpos($val['sub_order_no'], '-ZP') !== false || strpos($val['sub_order_no'], '补') !== false || strpos($val['sub_order_no'], '换') !== false) {
                    $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'order_type' => 7]));
                } else if (in_array($val['store_id'], $tripartite_order_status_search)) {
                    if (isset($orderStatusBatch['data']['list'][$val['sub_order_no']]['status']) && isset($orderStatusBatch['data']['list'][$val['sub_order_no']]['refund_status']) && $orderStatusBatch['data']['list'][$val['sub_order_no']]['status'] == 1 && $orderStatusBatch['data']['list'][$val['sub_order_no']]['refund_status'] == 0) {
                        $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'order_type' => 7]));
                    }
                } else {
                    $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'order_type' => 7]));
                }
                $status = isset($orderStatusBatch['data']['list'][$val['sub_order_no']]['status']) ? $orderStatusBatch['data']['list'][$val['sub_order_no']]['status'] : '未查询';
                echo '订单号:' . $val['sub_order_no'] . ',店铺ID:' . $val['store_id'] . ',三方订单状态:' . $status . PHP_EOL;
            }
            }
            //推送处理队列
            if (!empty($dataStr)) {
                $pushData = array(
                    'exchange_name' => 'orders',
                    'routing_key'   => 'push.wms',
                    'data'          => $dataStr,
                );
                Log::write("订单推送WMS队列 1: " . json_encode($pushData));
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
            }
        }
        return true;
    }
}