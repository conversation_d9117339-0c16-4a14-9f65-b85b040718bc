<?php

namespace app\service;

use app\BaseService;
use app\model\DailyPaymentStatistics;
use app\model\PaymentStatisticsLog;
use app\service\es\Es;
use think\facade\Cache;
use think\facade\Db;

class PaymentStatistics extends BaseService
{
    /**
     * Redis缓存前缀
     */
    const REDIS_PREFIX = 'payment_statistics:';

    /**
     * Redis缓存过期时间（7天）
     */
    const REDIS_EXPIRE = 604800;

    /**
     * 写入收款数据（新版本）
     * @param string $mainOrderNo 主订单号
     * @param float $amount 金额
     * @return bool
     */
    public function writePaymentDataV2($mainOrderNo, $amount)
    {
        try {
            // 验证金额
            if ($amount <= 0) {
                $this->throwError('金额必须大于0');
            }

            // 根据主订单号获取所有子订单的收款商户信息
            $merchantAmounts = $this->getPaymentMerchantsByMainOrder($mainOrderNo);

            if (empty($merchantAmounts)) {
                $this->throwError('未找到有效的子订单信息');
            }

            $date = date('Y-m-d');

            // 批量处理收款统计（以实际订单金额为准）
            $processedMerchants = $this->batchProcessPaymentStatistics($mainOrderNo, $merchantAmounts, $amount, $date);

            if (empty($processedMerchants)) {
                $this->throwError('没有有效的收款商户需要处理');
            }

            // 记录实际处理的金额信息
            $actualTotalAmount = array_sum($merchantAmounts);
            \think\facade\Log::info("收款处理完成: 主订单号={$mainOrderNo}, 传入金额={$amount}, 实际处理金额={$actualTotalAmount}, 处理商户=" . implode(',', $processedMerchants));

            return true;

        } catch (\Exception $e) {
            // 发送企业微信通知
            $msg = "收款统计数据写入失败: 主订单号:{$mainOrderNo}, 金额:{$amount}, 错误:{$e->getMessage()}";
            try {
                \Curl::wecomSend($msg, 'ChenChaoTao', 'text');
            } catch (\Exception $notifyException) {
                \think\facade\Log::error('企业微信通知失败: ' . $notifyException->getMessage());
            }

            throw $e;
        }
    }

    /**
     * 写入退款数据（新版本）
     * @param string $refundNo 退款单号
     * @param float $amount 退款金额
     * @return bool
     */
    public function writeRefundDataV2($refundNo, $amount)
    {
        try {
            // 验证金额
            if ($amount <= 0) {
                $this->throwError('金额必须大于0');
            }

            // 根据退款单号获取收款商户信息
            $merchantId = $this->getRefundMerchantByRefundNo($refundNo);

            if (!$merchantId) {
                $this->throwError('未找到退款单对应的收款商户信息');
            }

            // 检查是否已经处理过
            if (PaymentStatisticsLog::isProcessed($refundNo, $merchantId, 2)) {
                $this->throwError('该退款单已经处理过，请勿重复提交');
            }

            // 验证收款商户ID是否有效
            if (!DailyPaymentStatistics::isValidMerchantId($merchantId)) {
                $this->throwError('无效的收款商户ID');
            }

            $date = date('Y-m-d');

            // 更新Redis缓存
            $this->updateRedisCacheByMerchantId($merchantId, 2, $amount, $date);

            // 记录处理日志
            PaymentStatisticsLog::recordProcess($refundNo, $merchantId, 2, $amount);

            return true;

        } catch (\Exception $e) {
            // 发送企业微信通知
            $msg = "退款统计数据写入失败: 退款单号:{$refundNo}, 金额:{$amount}, 错误:{$e->getMessage()}";
            try {
                \Curl::wecomSend($msg, 'LongFei', 'text');
            } catch (\Exception $notifyException) {
                \think\facade\Log::error('企业微信通知失败: ' . $notifyException->getMessage());
            }

            throw $e;
        }
    }

    /**
     * 写入收款/退款数据（保持向后兼容）
     * @param string $companyCode 公司编码
     * @param int $operationType 操作类型 1:收款 2:退款
     * @param float $amount 金额
     * @return bool
     */
    public function writePaymentData($companyCode, $operationType, $amount)
    {
        try {
            // 验证公司编码
            if (!DailyPaymentStatistics::isValidCompanyCode($companyCode)) {
                $this->throwError('无效的公司编码');
            }

            // 验证操作类型
            if (!in_array($operationType, [1, 2])) {
                $this->throwError('无效的操作类型');
            }

            // 验证金额
            if ($amount <= 0) {
                $this->throwError('金额必须大于0');
            }

            $date = date('Y-m-d');

            // 直接更新Redis缓存
            $this->updateRedisCache($companyCode, $operationType, $amount, $date);

            return true;

        } catch (\Exception $e) {
            // 发送企业微信通知
            $msg = "收款统计写入失败: 公司编码:{$companyCode}, 操作类型:{$operationType}, 金额:{$amount}, 错误:{$e->getMessage()}";
            try {
                \Curl::wecomSend($msg, 'LongFei', 'text');
            } catch (\Exception $notifyException) {
                // 通知失败也记录日志
                \think\facade\Log::error('企业微信通知失败: ' . $notifyException->getMessage());
            }

            $this->throwError($e->getMessage());
        }
    }

    /**
     * 更新Redis缓存
     * @param string $companyCode
     * @param int $operationType
     * @param float $amount
     * @param string $date
     */
    private function updateRedisCache($companyCode, $operationType, $amount, $date)
    {
        $redis = $this->getRedisConnection();

        // 构建Redis key
        $key = self::REDIS_PREFIX . $companyCode . ':' . $date;

        // 根据操作类型更新对应字段
        if ($operationType == 1) { // 1=收款
            $redis->hIncrByFloat($key, 'payment_amount', $amount);
        } else { // 2=退款
            $redis->hIncrByFloat($key, 'refund_amount', $amount);
        }

        // 设置过期时间为7天
        $redis->expire($key, 7 * 24 * 3600);
    }

    /**
     * 获取Redis连接
     * @return \Redis
     */
    private function getRedisConnection()
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        if (env('CACHE.PASSWORD')) {
            $redis->auth(env('CACHE.PASSWORD'));
        }
        $redis->select(env('CACHE.db', 1));
        return $redis;
    }

    /**
     * 获取当日数据（从Redis）
     * @param string|null $companyCode 公司编码，为空则获取所有公司
     * @return array
     */
    public function getTodayData($companyCode = null)
    {
        $redis  = $this->getRedisConnection();
        $date   = date('Y-m-d');
        $result = [];

        $companyCodes = $companyCode ? [$companyCode] : DailyPaymentStatistics::getAllCompanyCodes();

        foreach ($companyCodes as $code) {
            $key  = self::REDIS_PREFIX . $code . ':' . $date;
            $data = $redis->hGetAll($key);

            $result[] = [
                'company_code'   => $code,
                'company_name'   => DailyPaymentStatistics::getCompanyName($code),
                'date'           => $date,
                'payment_amount' => round($data['payment_amount'] ?? '0.00',2),
                'refund_amount'  => round($data['refund_amount'] ?? '0.00',2)
            ];
        }

        return $result;
    }

    /**
     * 获取历史数据列表
     * @param array $params 查询参数
     * @return array
     */
    public function getHistoryList($params)
    {
        $page        = $params['page'] ?? 1;
        $limit       = $params['limit'] ?? 20;
        $companyCode = $params['company_code'] ?? '';
        $startDate   = $params['start_date'] ?? '';
        $endDate     = $params['end_date'] ?? '';

        $where = [];
        if ($companyCode) {
            $where[] = ['company_code', '=', $companyCode];
        }
        if ($startDate) {
            $where[] = ['date', '>=', $startDate];
        }
        if ($endDate) {
            $where[] = ['date', '<=', $endDate];
        }

        $query = DailyPaymentStatistics::where($where);

        // 获取总数
        $total = $query->count();

        // 获取列表数据
        $list = $query->order('date', 'desc')
            ->page($page, $limit)
            ->select()
            ->toArray();

        // 如果查询当日数据，需要合并Redis中的实时数据
        $today = date('Y-m-d');
        if ((!$startDate || $startDate <= $today) && (!$endDate || $endDate >= $today)) {
            $todayData = $this->getTodayData($companyCode);

            // 检查数据库中是否已有今日数据
            $existingTodayData = [];
            foreach ($list as $item) {
                if ($item['date'] == $today) {
                    $existingTodayData[$item['company_code']] = $item;
                }
            }

            // 合并或添加今日数据
            foreach ($todayData as $todayItem) {
                $code = $todayItem['company_code'];
                if (isset($existingTodayData[$code])) {
                    // 更新现有数据
                    foreach ($list as &$item) {
                        if ($item['date'] == $today && $item['company_code'] == $code) {
                            $item['payment_amount'] = $todayItem['payment_amount'];
                            $item['refund_amount']  = $todayItem['refund_amount'];
                            break;
                        }
                    }
                } else {
                    // 添加新数据
                    array_unshift($list, $todayItem);
                    $total++;
                }
            }
        }

        return [
            'list'  => $list,
            'total' => $total,
            'page'  => $page,
            'limit' => $limit
        ];
    }

    /**
     * 将前一天的Redis数据写入数据库
     * @param string|null $date 指定日期，为空则处理昨天的数据
     * @return bool
     */
    public function syncRedisToDatabase($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d', strtotime('-1 day'));
        }

        $redis        = $this->getRedisConnection();
        $companyCodes = DailyPaymentStatistics::getAllCompanyCodes();
        $successCount = 0;

        // 一次性统计所有公司的订单数量，避免重复查询
        $orderCounts = $this->getAllOrderCountsByDate($date);
        $refundOrderCounts = $this->getAllRefundOrderCountsByDate($date);

        Db::startTrans();
        try {
            foreach ($companyCodes as $companyCode) {
                $key  = self::REDIS_PREFIX . $companyCode . ':' . $date;
                $data = $redis->hGetAll($key);

                // 获取Redis中的数据，没有则默认为0
                $paymentAmount = $data['payment_amount'] ?? 0;
                $refundAmount  = $data['refund_amount'] ?? 0;

                // 从统计结果中获取订单数量
                $orderCount       = $orderCounts[$companyCode] ?? 0;
                $refundOrderCount = $refundOrderCounts[$companyCode] ?? 0;

                // 检查是否已存在记录
                $existing = DailyPaymentStatistics::where([
                    'company_code' => $companyCode,
                    'date'         => $date
                ])->find();

                if ($existing) {
                    // 更新现有记录
                    $existing->payment_amount      = $paymentAmount;
                    $existing->refund_amount       = $refundAmount;
                    $existing->order_count         = $orderCount;
                    $existing->refund_order_count  = $refundOrderCount;
                    $existing->save();
                } else {
                    // 为所有公司创建记录，包括金额为0的
                    DailyPaymentStatistics::create([
                        'company_code'        => $companyCode,
                        'company_name'        => DailyPaymentStatistics::getCompanyName($companyCode),
                        'date'                => $date,
                        'payment_amount'      => $paymentAmount,
                        'refund_amount'       => $refundAmount,
                        'order_count'         => $orderCount,
                        'refund_order_count'  => $refundOrderCount
                    ]);
                }

                $successCount++;

                // 不删除Redis中的数据，让其自动过期
            }

            Db::commit();

            return true;

        } catch (\Exception $e) {
            Db::rollback();

            // 发送失败通知
            $msg = "收款统计数据同步失败: 日期:{$date}, 错误:{$e->getMessage()}";
            try {
                \Curl::wecomSend($msg, 'LongFei', 'text');
            } catch (\Exception $notifyException) {
                \think\facade\Log::error('企业微信通知失败: ' . $notifyException->getMessage());
            }

            throw $e;
        }
    }

    /**
     * 根据主订单号获取所有子订单的收款商户信息
     * @param string $mainOrderNo 主订单号
     * @return array [merchantId => cashAmount, ...]
     */
    private function getPaymentMerchantsByMainOrder($mainOrderNo)
    {
        // 获取主订单信息
        $mainOrder = Db::name('order_main')->where('main_order_no', $mainOrderNo)->find();
        if (!$mainOrder) {
            $this->throwError('主订单不存在');
        }

        // 获取订单类型配置
        $orderTypeConfig = array_column(config('config.order_type'), null, 'value');
        $orderTypes      = explode(',', $mainOrder['order_type']);

        // 验证订单类型（只支持指定的类型）
        $validOrderTypes = ['0', '1', '2', '3']; // 闪购、秒发、跨境、尾货
        foreach ($orderTypes as $orderType) {
            if (!in_array($orderType, $validOrderTypes)) {
                $this->throwError('不支持的订单类型: ' . ($orderTypeConfig[$orderType]['label'] ?? $orderType));
            }
        }

        // 获取所有子订单
        $allSubOrders = [];
        foreach ($orderTypes as $orderType) {
            if (!isset($orderTypeConfig[$orderType])) {
                continue;
            }

            $table     = $orderTypeConfig[$orderType]['table'];
            $subOrders = Db::name($table)->where([
                ['main_order_id', '=', $mainOrder['id']],
                ['uid', '=', $mainOrder['uid']],
                ['is_delete', '=', 0]
            ])->field('period,cash_amount,sub_order_no,sub_order_status')->select()->toArray();


            foreach ($subOrders as $subOrder) {
                // 只处理已支付的订单
                if ($subOrder['sub_order_status'] == 0) {
                    $this->throwError("子订单 {$subOrder['sub_order_no']} 状态错误，订单未支付");
                }
                $allSubOrders[] = $subOrder;
            }
        }

        if (empty($allSubOrders)) {
            $this->throwError('未找到有效的子订单');
        }

        // 批量获取期数信息，查询收款商户ID
        $periods           = array_unique(array_column($allSubOrders, 'period'));
        $periodMerchantMap = $this->batchGetMerchantIdsByPeriods($periods);

        // 优化的金额汇总计算
        $merchantAmounts = $this->calculateMerchantAmounts($allSubOrders, $periodMerchantMap);

        return $merchantAmounts;
    }

    /**
     * 根据退款单号获取收款商户ID
     * @param string $refundNo 退款单号
     * @return int|null
     */
    private function getRefundMerchantByRefundNo($refundNo)
    {
        if (strpos($refundNo, 'GD') === 0) {
            // 工单退款，查询工单表
            return $this->getMerchantIdFromWorkOrder($refundNo);
        } else {
            // 普通退款，查询退款表
            return $this->getMerchantIdFromRefundOrder($refundNo);
        }
    }

    /**
     * 从工单表获取收款商户ID
     * @param string $workOrderNo 工单号
     * @return int|null
     */
    private function getMerchantIdFromWorkOrder($workOrderNo)
    {
        try {
            // 查询工单表获取期数
            $workOrder = Db::query("SELECT period FROM vh_customer_service.vh_work_order WHERE work_order_no = ?", [$workOrderNo]);

            if (empty($workOrder) || !isset($workOrder[0]['period']) || !$workOrder[0]['period']) {
                return null;
            }

            return $this->getMerchantIdByPeriod($workOrder[0]['period']);
        } catch (\Exception $e) {
            \think\facade\Log::error('查询工单表失败: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 从退款订单表获取收款商户ID
     * @param string $refundOrderNo 退款订单号
     * @return int|null
     */
    private function getMerchantIdFromRefundOrder($refundOrderNo)
    {
        // 查询退款订单表获取子订单号
        $refundOrder = Db::name('refund_order')
            ->where('refund_order_no', $refundOrderNo)
            ->field('sub_order_no')
            ->find();

        if (!$refundOrder) {
            return null;
        }

        // 通过ES查询子订单获取期数
        $orderInfo = Es::name(Es::ORDERS)->where([['sub_order_no', '=', $refundOrder['sub_order_no']]])->field('period')->find();

        if (!$orderInfo || !isset($orderInfo['period'])) {
            return null;
        }

        return $this->getMerchantIdByPeriod($orderInfo['period']);
    }

    /**
     * 根据期数获取收款商户ID（带缓存）
     * @param int $period 期数
     * @return int
     */
    private function getMerchantIdByPeriod($period)
    {
        // 使用缓存避免重复查询
        $cacheKey   = "period_merchant_id:{$period}";
        $merchantId = Cache::get($cacheKey);

        if ($merchantId === null) {
            $periodInfo = Es::name(Es::PERIODS)->where([['id', '=', $period]])->field('payee_merchant_id')->find();
            $merchantId = ($periodInfo && isset($periodInfo['payee_merchant_id']))
                ? $periodInfo['payee_merchant_id']
                : 2; // 默认返回2

            // 缓存1小时
            Cache::set($cacheKey, $merchantId, 3600);
        }

        return $merchantId;
    }

    /**
     * 根据收款商户ID更新Redis缓存
     * @param int $merchantId 收款商户ID
     * @param int $operationType 操作类型 1:收款 2:退款
     * @param float $amount 金额
     * @param string $date 日期
     */
    private function updateRedisCacheByMerchantId($merchantId, $operationType, $amount, $date)
    {
        $companyName = DailyPaymentStatistics::getCompanyNameByMerchantId($merchantId);
        if (!$companyName) {
            return;
        }

        // 为了兼容现有的Redis结构，我们需要找到对应的公司编码
        $companyCode = '';
        foreach (DailyPaymentStatistics::COMPANY_MAP as $code => $name) {
            if ($name === $companyName) {
                $companyCode = $code;
                break;
            }
        }

        if ($companyCode) {
            $this->updateRedisCache($companyCode, $operationType, $amount, $date);
        }
    }

    /**
     * 批量获取期数对应的收款商户ID
     * @param array $periods 期数数组
     * @return array [period => merchantId, ...]
     */
    private function batchGetMerchantIdsByPeriods($periods)
    {
        if (empty($periods)) {
            return [];
        }

        $periods = array_values($periods);
        // 批量查询ES获取期数信息
        $periodInfos = Es::name(Es::PERIODS)
            ->where([['id', 'in', $periods]])
            ->field('id,payee_merchant_id')
            ->select()
            ->toArray();

        // 构建期数到收款商户ID的映射
        $periodMerchantMap = [];
        foreach ($periods as $period) {
            $periodMerchantMap[$period] = 2; // 默认值
        }

        foreach ($periodInfos as $periodInfo) {
            $merchantId                           = $periodInfo['payee_merchant_id'] ?? 2;
            $periodMerchantMap[$periodInfo['id']] = $merchantId;
        }

        return $periodMerchantMap;
    }

    /**
     * 批量获取子订单信息（优化版本）
     * @param array $orderTypes 订单类型数组
     * @param int $mainOrderId 主订单ID
     * @param int $uid 用户ID
     * @return array
     */
    private function batchGetSubOrders($orderTypes, $mainOrderId, $uid)
    {
        $orderTypeConfig = array_column(config('config.order_type'), null, 'value');
        $allSubOrders    = [];

        // 构建批量查询条件
        $queries = [];
        foreach ($orderTypes as $orderType) {
            if (!isset($orderTypeConfig[$orderType])) {
                continue;
            }
            $queries[] = [
                'table' => $orderTypeConfig[$orderType]['table'],
                'type'  => $orderType
            ];
        }

        // 并行查询所有子订单表
        foreach ($queries as $query) {
            $subOrders = Db::name($query['table'])->where([
                ['main_order_id', '=', $mainOrderId],
                ['uid', '=', $uid],
                ['is_delete', '=', 0]
            ])->field('period,cash_amount,sub_order_no,sub_order_status')->select()->toArray();

            foreach ($subOrders as $subOrder) {
                // 只处理已支付的订单
                if ($subOrder['sub_order_status'] == 0) {
                    $this->throwError("子订单 {$subOrder['sub_order_no']} 状态错误，订单未支付");
                }
                $subOrder['order_type'] = $query['type']; // 添加订单类型标识
                $allSubOrders[]         = $subOrder;
            }
        }

        return $allSubOrders;
    }

    /**
     * 优化的金额汇总计算
     * @param array $subOrders 子订单数组
     * @param array $periodMerchantMap 期数商户映射
     * @return array [merchantId => amount, ...]
     */
    private function calculateMerchantAmounts($subOrders, $periodMerchantMap)
    {
        $merchantAmounts  = [];
        $validMerchantIds = DailyPaymentStatistics::getAllMerchantIds();

        foreach ($subOrders as $subOrder) {
            $merchantId = $periodMerchantMap[$subOrder['period']] ?? 2;

            // 只处理有效的收款商户ID
            if (!in_array($merchantId, $validMerchantIds)) {
                continue;
            }

            if (!isset($merchantAmounts[$merchantId])) {
                $merchantAmounts[$merchantId] = '0.00';
            }
            $merchantAmounts[$merchantId] = bcadd($merchantAmounts[$merchantId], $subOrder['cash_amount'], 2);
        }

        return $merchantAmounts;
    }

    /**
     * 批量处理收款统计（以实际订单金额为准）
     * @param string $mainOrderNo 主订单号
     * @param array $merchantAmounts 商户金额映射（实际子订单金额）
     * @param float $inputAmount 传入的总金额（用于验证）
     * @param string $date 日期
     * @return array 处理成功的商户ID列表
     */
    private function batchProcessPaymentStatistics($mainOrderNo, $merchantAmounts, $inputAmount, $date)
    {
        $processedMerchants = [];
        $actualTotalAmount  = array_sum($merchantAmounts);

        if ($actualTotalAmount <= 0) {
            return $processedMerchants;
        }

        // 金额验证：如果传入金额与实际订单金额差异过大，记录警告
        $amountDiff     = abs($inputAmount - $actualTotalAmount);
        $diffPercentage = $actualTotalAmount > 0 ? ($amountDiff / $actualTotalAmount) * 100 : 0;

        if ($diffPercentage > 5) { // 差异超过5%记录警告
            \think\facade\Log::warning("收款金额差异较大: 主订单号={$mainOrderNo}, 传入金额={$inputAmount}, 实际订单金额={$actualTotalAmount}, 差异={$diffPercentage}%");
        }

        // 使用事务确保数据一致性
        Db::startTrans();
        try {
            foreach ($merchantAmounts as $merchantId => $actualAmount) {
                // 检查是否已经处理过
                if (PaymentStatisticsLog::isProcessed($mainOrderNo, $merchantId, 1)) {
                    continue;
                }

                // 直接使用实际的子订单金额，不进行比例分配
                $merchantAmount = $actualAmount;

                // 更新Redis缓存
                $this->updateRedisCacheByMerchantId($merchantId, 1, $merchantAmount, $date);

                // 记录处理日志
                PaymentStatisticsLog::recordProcess($mainOrderNo, $merchantId, 1, $merchantAmount);

                $processedMerchants[] = $merchantId;
            }

            Db::commit();
            return $processedMerchants;

        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 批量查询退款订单信息
     * @param array $refundNos 退款单号数组
     * @return array [refundNo => subOrderNo, ...]
     */
    private function batchGetRefundOrders($refundNos)
    {
        if (empty($refundNos)) {
            return [];
        }

        $refundOrders = Db::name('refund_order')
            ->where('refund_order_no', 'in', $refundNos)
            ->field('refund_order_no,sub_order_no')
            ->select()
            ->toArray();

        return array_column($refundOrders, 'sub_order_no', 'refund_order_no');
    }

    /**
     * 批量查询工单信息
     * @param array $workOrderNos 工单号数组
     * @return array [workOrderNo => period, ...]
     */
    private function batchGetWorkOrders($workOrderNos)
    {
        if (empty($workOrderNos)) {
            return [];
        }

        try {
            $workOrders = Db::query(
                "SELECT work_order_no, period FROM vh_customer_service.vh_work_order WHERE work_order_no IN (" .
                str_repeat('?,', count($workOrderNos) - 1) . "?)",
                $workOrderNos
            );

            return array_column($workOrders, 'period', 'work_order_no');
        } catch (\Exception $e) {
            \think\facade\Log::error('批量查询工单表失败: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 优化的Redis缓存更新（支持管道操作）
     * @param array $updates 更新数据 [['merchantId' => 1, 'operationType' => 1, 'amount' => 100, 'date' => '2024-12-04'], ...]
     */
    private function batchUpdateRedisCache($updates)
    {
        if (empty($updates)) {
            return;
        }

        $redis = $this->getRedisConnection();
        $pipe  = $redis->multi(\Redis::PIPELINE);

        foreach ($updates as $update) {
            $companyName = DailyPaymentStatistics::getCompanyNameByMerchantId($update['merchantId']);
            if (!$companyName) {
                continue;
            }

            // 找到对应的公司编码
            $companyCode = '';
            foreach (DailyPaymentStatistics::COMPANY_MAP as $code => $name) {
                if ($name === $companyName) {
                    $companyCode = $code;
                    break;
                }
            }

            if ($companyCode) {
                $key   = self::REDIS_PREFIX . $companyCode . ':' . $update['date'];
                $field = $update['operationType'] == 1 ? 'payment_amount' : 'refund_amount';
                $pipe->hIncrByFloat($key, $field, $update['amount']);
                $pipe->expire($key, self::REDIS_EXPIRE);
            }
        }

        $pipe->exec();
    }

    /**
     * 内存优化的数据处理
     * @param array $data 大数据集
     * @param callable $processor 处理函数
     * @param int $batchSize 批次大小
     * @return array
     */
    private function processInBatches($data, $processor, $batchSize = 100)
    {
        $results = [];
        $chunks  = array_chunk($data, $batchSize);

        foreach ($chunks as $chunk) {
            $batchResults = call_user_func($processor, $chunk);
            $results      = array_merge($results, $batchResults);

            // 释放内存
            unset($batchResults);
        }

        return $results;
    }

    /**
     * 按月统计数据
     * @param array $params 查询参数
     * @return array
     */
    public function getMonthlyStatistics($params = [])
    {
        $companyCode = $params['company_code'] ?? null;
        $year        = $params['year'] ?? null;
        $month       = $params['month'] ?? null;

        // 构建查询条件
        $where = [];

        if ($companyCode) {
            // 验证公司编码
            if (!DailyPaymentStatistics::isValidCompanyCode($companyCode)) {
                $this->throwError('无效的公司编码');
            }
            $where[] = ['company_code', '=', $companyCode];
        }

        // 年份和月份独立筛选
        if ($year) {
            // 指定年份，例如：year=2025
            $where[] = ['YEAR(date)', '=', $year];
        }

        if ($month) {
            // 指定年月，例如：month=2025-08
            if (preg_match('/^(\d{4})-(\d{1,2})$/', $month, $matches)) {
                $monthYear = $matches[1];
                $monthNum = $matches[2];
                $where[] = ['YEAR(date)', '=', $monthYear];
                $where[] = ['MONTH(date)', '=', $monthNum];
            }
        }

        // 查询数据并按月分组统计
        $query = DailyPaymentStatistics::where($where);

        // 按月分组统计，返回每个月的数据
        $list = $query->field([
            'company_code',
            'company_name',
            'DATE_FORMAT(date, "%Y-%m") as month',
            'SUM(payment_amount) as payment_amount',
            'SUM(refund_amount) as refund_amount',
            'SUM(order_count) as order_count',
            'SUM(refund_order_count) as refund_order_count'
        ])
        ->group('company_code, DATE_FORMAT(date, "%Y-%m")')
        ->order('month DESC, company_code')
        ->select()
        ->toArray();

        // 格式化数据
        foreach ($list as &$item) {
            $item['payment_amount']     = round($item['payment_amount'], 2);
            $item['refund_amount']      = round($item['refund_amount'], 2);
            $item['order_count']        = intval($item['order_count']);
            $item['refund_order_count'] = intval($item['refund_order_count']);
        }

        return $list;
    }

    /**
     * 一次性统计所有公司在指定日期的付款订单数量
     * @param string $date 日期
     * @return array 返回格式：['001' => 10, '002' => 5, ...]
     */
    private function getAllOrderCountsByDate($date)
    {
        $startTime = $date . ' 00:00:00';
        $endTime   = $date . ' 23:59:59';

        $tables = [
            'flash_order',
            'second_order',
            'cross_order',
            'tail_order'
        ];

        $companyCounts = [];

        // 初始化所有公司的计数为0
        foreach (DailyPaymentStatistics::getAllCompanyCodes() as $companyCode) {
            $companyCounts[$companyCode] = 0;
        }

        foreach ($tables as $table) {
            // 查询该表中指定日期的所有付款订单
            $list = Db::name($table)
                ->whereBetweenTime('payment_time', $startTime, $endTime)
                ->column('period');

            if (empty($list)) {
                continue;
            }

            // 分批查询期数对应的商户ID
            $periods = array_values(array_unique($list));
            $periods_g = array_chunk($periods, 9000);

            $periodMerchantMap = [];
            foreach ($periods_g as $pg) {
                $pgs = Es::name(Es::PERIODS)->where([
                    ['_id', 'in', $pg],
                ])->field('id,payee_merchant_id')->select()->toArray();

                foreach ($pgs as $pg_item) {
                    $periodMerchantMap[$pg_item['id']] = $pg_item['payee_merchant_id'];
                }
            }

            if (!empty($periodMerchantMap)) {
                // 查询订单并按商户分组统计
                $orders = Db::name($table)
                    ->whereBetweenTime('payment_time', $startTime, $endTime)
                    ->where('period', 'in', array_keys($periodMerchantMap))
                    ->column('period');

                // 按商户统计订单数量
                $merchantCounts = [];
                foreach ($orders as $period) {
                    $merchantId = $periodMerchantMap[$period] ?? null;
                    if ($merchantId) {
                        $merchantCounts[$merchantId] = ($merchantCounts[$merchantId] ?? 0) + 1;
                    }
                }

                // 将商户统计转换为公司统计
                foreach ($merchantCounts as $merchantId => $count) {
                    $companyCode = $this->getCompanyCodeByMerchantId($merchantId);
                    if ($companyCode) {
                        $companyCounts[$companyCode] += $count;
                    }
                }
            }
        }

        return $companyCounts;
    }

    /**
     * 一次性统计所有公司在指定日期的退款订单数量
     * @param string $date 日期
     * @return array 返回格式：['001' => 3, '002' => 1, ...]
     */
    private function getAllRefundOrderCountsByDate($date)
    {
        $startTime = $date . ' 00:00:00';
        $endTime   = $date . ' 23:59:59';

        $tables = [
            'flash_order',
            'second_order',
            'cross_order',
            'tail_order'
        ];

        $companyCounts = [];

        // 初始化所有公司的计数为0
        foreach (DailyPaymentStatistics::getAllCompanyCodes() as $companyCode) {
            $companyCounts[$companyCode] = 0;
        }

        foreach ($tables as $table) {
            // 查询该表中指定日期的所有退款订单
            $list = Db::name($table)
                ->where('refund_status', 2) // 退款成功
                ->where(function ($q) use ($startTime, $endTime) {
                    $q->whereOr(function ($q) use ($startTime, $endTime) {
                        $q->whereBetweenTime('payment_time', $startTime, $endTime);
                    });
                    $q->whereOr(function ($q) use ($startTime, $endTime) {
                        $q->whereBetweenTime('refund_end_time', $startTime, $endTime);
                    });
                })
                ->column('period');

            if (empty($list)) {
                continue;
            }

            // 分批查询期数对应的商户ID
            $periods = array_values(array_unique($list));
            $periods_g = array_chunk($periods, 9000);

            $periodMerchantMap = [];
            foreach ($periods_g as $pg) {
                $pgs = Es::name(Es::PERIODS)->where([
                    ['_id', 'in', $pg],
                ])->field('id,payee_merchant_id')->select()->toArray();

                foreach ($pgs as $pg_item) {
                    $periodMerchantMap[$pg_item['id']] = $pg_item['payee_merchant_id'];
                }
            }

            if (!empty($periodMerchantMap)) {
                // 查询退款订单并按商户分组统计
                $orders = Db::name($table)
                    ->where('refund_status', 2) // 退款成功
                    ->where(function ($q) use ($startTime, $endTime) {
                        $q->whereOr(function ($q) use ($startTime, $endTime) {
                            $q->whereBetweenTime('payment_time', $startTime, $endTime);
                        });
                        $q->whereOr(function ($q) use ($startTime, $endTime) {
                            $q->whereBetweenTime('refund_end_time', $startTime, $endTime);
                        });
                    })
                    ->where('period', 'in', array_keys($periodMerchantMap))
                    ->column('period');

                // 按商户统计退款订单数量
                $merchantCounts = [];
                foreach ($orders as $period) {
                    $merchantId = $periodMerchantMap[$period] ?? null;
                    if ($merchantId) {
                        $merchantCounts[$merchantId] = ($merchantCounts[$merchantId] ?? 0) + 1;
                    }
                }

                // 将商户统计转换为公司统计
                foreach ($merchantCounts as $merchantId => $count) {
                    $companyCode = $this->getCompanyCodeByMerchantId($merchantId);
                    if ($companyCode) {
                        $companyCounts[$companyCode] += $count;
                    }
                }
            }
        }

        return $companyCounts;
    }

    /**
     * 根据公司编码获取对应的商户ID
     * @param string $companyCode 公司编码
     * @return int|null
     */
    private function getMerchantIdByCompanyCode($companyCode)
    {
        $companyName = DailyPaymentStatistics::getCompanyName($companyCode);
        if (!$companyName) {
            return null;
        }

        // 根据公司名称查找对应的商户ID
        foreach (DailyPaymentStatistics::MERCHANT_COMPANY_MAP as $merchantId => $name) {
            if ($name === $companyName) {
                return $merchantId;
            }
        }

        return null;
    }

    /**
     * 根据商户ID获取对应的公司编码
     * @param int $merchantId 商户ID
     * @return string|null
     */
    private function getCompanyCodeByMerchantId($merchantId)
    {
        $companyName = DailyPaymentStatistics::getCompanyNameByMerchantId($merchantId);
        if (!$companyName) {
            return null;
        }

        // 根据公司名称查找对应的公司编码
        foreach (DailyPaymentStatistics::COMPANY_MAP as $companyCode => $name) {
            if ($name === $companyName) {
                return $companyCode;
            }
        }

        return null;
    }
}
