<?php


namespace app\service;


use app\BaseService;
use app\service\Cross as CrossService;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\Order as OrderService;
use app\service\Push as PushService;
use app\service\Wms as WmsService;
use think\facade\Db;
use think\facade\Log;

class Push extends BaseService
{
    private $apiUrl            = 'https://www.umatou.com/api_order/umatou';
    private $secret            = 'b9e1539cb63741cd';
    private $iv                = '0102030405060708';
    private $ebcCode           = '504096022T'; // 电商平台海关备案十位代码
    private $ebcName           = '重庆云酒佰酿电子商务有限公司'; // 电商平台海关备案名称
    private $technologyEbcCode = '50039609JR'; // 科技公司电商平台海关备案十位代码
    private $technologyEbcName = '佰酿云酒（重庆）科技有限公司'; // 科技公司电商平台海关备案名称
    private $appId             = '8c8c310527a044b09978c3d59b67d19c';
    private $method            = 'com.umatou.order.create';
    private $repush_method     = 'com.umatou.logistics.waybill';
    private $houseCode         = '136007';//仓库编码
    private $format            = 'JSON';
    private $charset           = 'utf8';
    private $signType          = 'AES_1';
    private $timestamp         = '';
    private $version           = '1.0';
    private $notifyUrl         = '';
    private $bizcontent        = '';

    /**
     * Description:获取订单推送萌牙/T+数据
     * Author: zrc
     * Date: 2022/4/20
     * Time: 15:37
     * @param $requestparams
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderPushData($requestparams)
    {
        $params = $requestparams;
        $result = [];
        $es     = new ElasticSearchService();
        if (in_array($params['order_type'], [0, 1, 2, 3, 4, 9])) {
            //获取订单信息
            $order_type = config('config')['order_type'];//订单频道获取
            $orderInfo  = Db::name($order_type[intval($params['order_type'])]['table'])
                ->alias('so')
                ->field('so.*,om.main_order_no,om.payment_method,om.province_id,om.city_id,om.district_id,om.address,om.consignee,om.consignee_phone,om.payment_subject')
                ->leftJoin('order_main om', 'om.id=so.main_order_id')
                ->where(['so.sub_order_no' => $params['sub_order_no']])
                ->find();

            $deposit = $goods_deposit = 0;
            if (($params['order_type'] == 0) && !empty($orderInfo['coupon_id']) && (!empty($orderInfo['delivery_time']) && ($orderInfo['delivery_time'] >= 1704038400)))
                $deposit = $goods_deposit = Db::name('deposit_inflation_record')->where('coupon_issue_id', '=', $orderInfo['coupon_id'])->where('period', '=', $orderInfo['period'])->where('status', '=', 2)->value('deposit') ?? 0;

            if (empty($orderInfo)) $this->throwError('未获取到订单信息');
            $receiveInfo = Db::name('sub_order_receive_information')->where(['sub_order_no' => $orderInfo['sub_order_no'], 'main_order_no' => $orderInfo['main_order_no'], 'uid' => $orderInfo['uid']])->find();
            if (!empty($receiveInfo)) {
                $orderInfo['province_id']     = $receiveInfo['province_id'];
                $orderInfo['city_id']         = $receiveInfo['city_id'];
                $orderInfo['district_id']     = $receiveInfo['district_id'];
                $orderInfo['address']         = $receiveInfo['address'];
                $orderInfo['consignee']       = $receiveInfo['consignee'];
                $orderInfo['consignee_phone'] = $receiveInfo['consignee_phone'];
            }
            //省市区处理
            $orderInfo['province_name'] = !empty($orderInfo['province_id']) ? getRegionalInfo($orderInfo['province_id']) : '';
            $orderInfo['city_name']     = !empty($orderInfo['city_id']) ? getRegionalInfo($orderInfo['city_id']) : '';
            $orderInfo['district_name'] = !empty($orderInfo['district_id']) ? getRegionalInfo($orderInfo['district_id']) : '';
            //兔头商品数据处理
            if ($params['order_type'] == 4) {
                $orderInfo['payment_amount'] = 0;
                $orderInfo['is_ts']          = 0;
            }
            //获取商品信息
            $periodsArr    = array(
                'index'  => ['periods'],
                'match'  => [['id' => $orderInfo['period']]],
                'source' => ['is_presell','payee_merchant_id', 'is_supplier_delivery', 'is_parcel_insurance', 'instruction', 'uninstruction', 'uninstruction']
            );
            $periodsEsData = $es->getDocumentList($periodsArr);
            if (!isset($periodsEsData['data'][0])) $this->throwError('未获取到商品信息');
            $periodsInfo = $periodsEsData['data'][0];
            //获取商品套餐信息
            $periodsSetArr    = array(
                'index'  => ['periods_set'],
                'match'  => [['id' => $orderInfo['package_id']]],
                'source' => ['associated_products', 'is_original_package', 'is_mystery_box']
            );
            $periodsSetEsData = $es->getDocumentList($periodsSetArr);
            if (!isset($periodsSetEsData['data'][0])) $this->throwError('未获取到商品套餐信息');
            $periodsSetInfo = $periodsSetEsData['data'][0];
            if ($periodsSetInfo['is_mystery_box'] == 1) {//盲盒产品信息处理
                $mystery_box_log = Db::name('order_mystery_box_log')->where(['main_order_no' => $orderInfo['main_order_no'], 'period' => $orderInfo['period'], 'package_id' => $orderInfo['package_id']])->find();
                if (empty($mystery_box_log)) $this->throwError('未获取到订单盲盒套餐信息');
                $associated_products = json_decode($mystery_box_log['product_info'], true);
            } else {
                $associated_products = json_decode($periodsSetInfo['associated_products'], true);
            }
            //获取产品信息
            $product_ids = array_column($associated_products, 'product_id');
            $wikiData    = array(
                'ids'    => $product_ids,
                'fields' => 'id,bar_code,short_code,grape_picking_years,capacity,jd_eclp_code,product_type,product_category',
            );
            $productInfo = $this->httpPost(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/fieldsdatarr', $wikiData);
            if ($productInfo['error_code'] != 0 || count($productInfo['data']['list']) == 0) $this->throwError('未获取到产品信息');
            //商品信息处理
            $prod_num = 0;
            foreach ($associated_products as &$v) {
                foreach ($productInfo['data']['list'] as &$val) {
                    if ($val['id'] == $v['product_id']) {
                        $v['bar_code']            = $val['bar_code'];
                        $v['short_code']          = $val['short_code'];
                        $v['grape_picking_years'] = $val['grape_picking_years'];
                        $v['capacity']            = $val['capacity'];
                        $v['jd_eclp_code']        = !empty($val['jd_eclp_code']) ? $val['jd_eclp_code'] : '';
                        $v['goods_attr']          = $val['product_type'];
                        $v['product_category']    = $val['product_category'];
                    }
                }
                if (!isset($v['isGift'])) $v['isGift'] = 0;
                if ($v['isGift'] == 0) {
                    $prod_num += $v['nums'] * $orderInfo['order_qty'];
                }
            }
            //重新排序，保证最后一条在均分金额时加上不能整除的余数
            $is_gift = array_column($associated_products, 'isGift');
            array_multisort($is_gift, SORT_DESC, $associated_products);
            $goodsInfo = array();
            //换货补发订单ERP推送金额处理
            if (strpos($params['sub_order_no'], 'VHG') !== false) {
                $erp_push_amount             = Db::name('sub_order_extend')->where(['sub_order_no' => $params['sub_order_no'], 'order_type' => $params['order_type']])->value('erp_push_amount');
                $orderInfo['payment_amount'] = !empty($erp_push_amount) ? $erp_push_amount : 0;
            }
            foreach ($associated_products as $kk => $vv) {
                $goodsInfo[$kk]['goods_id']   = $orderInfo['period'];
                $goodsInfo[$kk]['goods_code'] = $vv['bar_code'];
                $goodsInfo[$kk]['number']     = $vv['nums'] * $orderInfo['order_qty'];
                $goodsInfo[$kk]['short_code'] = $vv['short_code'];
                $divisible_price              = round((($orderInfo['payment_amount'] * 100) % $prod_num) / 100, 2); //整除金额  ,如果不能整除，最后一个商品加上余数金额
                if ($divisible_price > 0) {
                    $tprice = round(($orderInfo['payment_amount'] - $divisible_price) / $prod_num * $goodsInfo[$kk]['number'], 2);
                } else {
                    $tprice = round($orderInfo['payment_amount'] / $prod_num * $goodsInfo[$kk]['number'], 2);
                }
                if ($vv['isGift'] == 1) {
                    $goodsInfo[$kk]['money'] = 0;
                } else {
                    $goodsInfo[$kk]['money'] = $kk + 1 == count($associated_products) ? $tprice + $divisible_price : $tprice;  //最后一个产品加上余数金额
                    if ($goods_deposit > 0) {
                        $goodsInfo[$kk]['money'] = bcadd($goodsInfo[$kk]['money'], $goods_deposit, 2);
                        $goods_deposit           = 0;
                    }
                }
                $goodsInfo[$kk]['goods_years']      = $vv['grape_picking_years'];
                $goodsInfo[$kk]['volume']           = $vv['capacity'];
                $goodsInfo[$kk]['jd_emg_code']      = $vv['jd_eclp_code'];
                $goodsInfo[$kk]['goods_attr']       = $vv['goods_attr'];
                $goodsInfo[$kk]['product_category'] = $vv['product_category'];
            }
            //平台ID处理 订单所属平台(0自营(WYS) 1京东 2淘宝 3拼多多 4天猫 5礼品卡 6兔头实物 7兔头虚拟 8学院 9酒会 10代发 11跨境）
            $platform_id = 0;
            if ($orderInfo['order_type'] == 2) $platform_id = 11;
            if ($orderInfo['order_type'] == 4) $platform_id = 6;
            if ($periodsInfo['is_supplier_delivery'] == 1) $platform_id = 10;
            //频道处理
            $channel_type = 0;
            if ($orderInfo['order_type'] == 0) $channel_type = 1;
            if ($orderInfo['order_type'] == 1) $channel_type = 2;
            //指令处理(原箱、冰袋、打保温)
            $instruction_add = $orderInfo['instructions'];
            //如果订单是原箱，指令追加原箱指令ID,写死ID(longfei)等前端上线，老数据跑完再用
            if (isset($orderInfo['is_original_package']) && $orderInfo['is_original_package'] == 1) {
                //if ($periodsSetInfo['is_original_package'] == 1) {
                $instruction_add = $orderInfo['instructions'] . ',11675';
            }
            $instruction_remove = $periodsInfo['uninstruction'];
            if (empty($orderInfo['warehouse_code'])) {
                //获取订单归属虚拟仓(仓库编码为空再获取)
                $ppData          = array(
                    'period'     => $orderInfo['period'],
                    'product_id' => $product_ids[0],
                    'field'      => 'erp_id',
                );
                $periods_product = $this->httpGet(env('ITEM.COMMODITIES_URL') . '/commodities/v3/getPackageProductInfo', $ppData);
                if ($periods_product['error_code'] != 0) $this->throwError('未获取订单归属虚拟仓');
                $orderInfo['warehouse_code'] = $periods_product['data']['erp_id'];
            }
            //用户加密信息处理
            if (!empty($orderInfo['consignee'])) {
                //用户信息加密处理
                $consignee              = $orderInfo['consignee'];
                $encrypt                = cryptionDeal(2, [$consignee], $orderInfo['uid'], '前端用户');
                $consignee              = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                $orderInfo['consignee'] = $consignee;
            }
            if (!empty($orderInfo['consignee_phone'])) {
                //用户信息加密处理
                $phone                        = $orderInfo['consignee_phone'];
                $encrypt                      = cryptionDeal(2, [$phone], $orderInfo['uid'], '前端用户');
                $phone                        = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
                $orderInfo['consignee_phone'] = $phone;
            }
            //参数组装
            if (empty($periodsInfo['payee_merchant_id']) && ($params['order_type'] == 9)) {
                $periodsInfo['payee_merchant_id'] = 2;
            }
            $result = array(
                'latest_delivery_time'=> $orderInfo['predict_time'] ?? 0,
                'payee_merchant_id'   => $periodsInfo['payee_merchant_id'],
                'main_order_no'       => $orderInfo['main_order_no'],
                'sub_order_no'        => $params['sub_order_no'],
                'uid'                 => $orderInfo['uid'],
                'platform_id'         => $platform_id,
                'is_storage'          => $orderInfo['is_ts'],
                'is_SF_cold_chain'    => in_array($orderInfo['express_type'],[3,31]) ? 1 : 0,
                'push_type'           => 0,
                'order_type'          => 0,
                'channel_type'        => $channel_type,
                'is_expedited'        => 0,
                'is_to_pay'           => 0,
                'order_status'        => $orderInfo['sub_order_status'],
                'instruction_add'     => $instruction_add,
                'instruction_remove'  => $instruction_remove,
                'warehouse_code'      => $orderInfo['warehouse_code'],
                'pay_money'           => bcadd($orderInfo['payment_amount'], $deposit, 2),
                'pay_method'          => $orderInfo['payment_method'],
                'payment_subject'     => $orderInfo['payment_subject'],
                'owner_id'            => '',
                'store_name'          => '',
                'express_type'        => $orderInfo['express_type'],
                'waybill_no'          => $orderInfo['express_number'],
                'predict_time'        => $orderInfo['predict_time'],
                'delivery_time'       => $orderInfo['delivery_time'],
                'payment_time'        => $orderInfo['payment_time'],
                'is_parcel_insurance' => $periodsInfo['is_parcel_insurance'],
                'created_time'        => $orderInfo['created_time'],
                'freeze_status'       => isset($orderInfo['freeze_status']) ? $orderInfo['freeze_status'] : 0,
                'push_wms_status'     => isset($orderInfo['push_wms_status']) ? $orderInfo['push_wms_status'] : 1,
                'addressInfo'         => [
                    'receiver_name'  => $orderInfo['consignee'],
                    'receiver_phone' => $orderInfo['consignee_phone'],
                    'province'       => $orderInfo['province_name'],
                    'city'           => $orderInfo['city_name'],
                    'town'           => $orderInfo['district_name'],
                    'address'        => $orderInfo['address'],
                    'remark'         => ''
                ],
                'goodsInfo'           => $goodsInfo
            );
            //商家秒发参数
            if ($params['order_type'] == 9) {
                $result['merchant_id']     = $orderInfo['merchant_id'];
                $result['product_channel'] = $orderInfo['product_channel'];
            }
        } else if ($params['order_type'] == 7) {
            //获取订单信息
            $orderInfo = Db::name('tripartite_order')
                ->alias('to')
                ->field('to.*,om.payment_method,om.consignee,om.consignee_phone,om.main_order_no')
                ->join('order_main om', 'om.id=to.main_order_id')
                ->where(['sub_order_no' => $params['sub_order_no']])
                ->find();
            //子订单数量
            $sub_order_nums = Db::name('tripartite_order')
                ->where('main_order_id', $orderInfo['main_order_id'])
                ->count();

            if (empty($orderInfo)) $this->throwError('未获取到订单信息');

            $receiveInfo = Db::name('sub_order_receive_information')->where(['sub_order_no' => $params['sub_order_no'], 'main_order_no' => $orderInfo['main_order_no'], 'uid' => 0])->find();
            if (!empty($receiveInfo)) {
                $orderInfo['address']         = $receiveInfo['address'];
                $orderInfo['province']        = $receiveInfo['province_name'];
                $orderInfo['city']            = $receiveInfo['city_name'];
                $orderInfo['district']        = $receiveInfo['district_name'];
                $orderInfo['consignee']       = $receiveInfo['consignee'];
                $orderInfo['consignee_phone'] = $receiveInfo['consignee_phone'];
            }

            //用户加密信息处理
            if (!empty($orderInfo['consignee'])) {
                //用户信息加密处理
                $consignee              = $orderInfo['consignee'];
                $encrypt                = cryptionDeal(2, [$consignee], '15736175219', '宗仁川');
                $consignee              = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                $orderInfo['consignee'] = $consignee;
            }
            if (!empty($orderInfo['consignee_phone'])) {
                //用户信息加密处理
                $phone                        = $orderInfo['consignee_phone'];
                $encrypt                      = cryptionDeal(2, [$phone], '15736175219', '宗仁川');
                $phone                        = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
                $orderInfo['consignee_phone'] = $phone;
            }
            //获取产品信息
            $items_info = explode(',', $orderInfo['items_info']);
            $goodsInfo  = [];
            foreach ($items_info as $k => $v) {
                $details     = explode('*', $v);
                $wikiData    = array(
                    'short_code' => $details[0],
                    'field'      => 'bar_code,short_code,grape_picking_years,capacity,jd_eclp_code,product_type,product_category',
                );
                $productInfo = $this->httpGet(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/query', $wikiData);
                if ($productInfo['error_code'] != 0 || count($productInfo['data']) == 0) $this->throwError('未获取到产品信息');
                $goodsInfo[$k]['goods_id']         = '';
                $goodsInfo[$k]['goods_code']       = $productInfo['data'][0]['bar_code'];
                $goodsInfo[$k]['number']           = $details[1] * $orderInfo['order_qty'];
                $goodsInfo[$k]['short_code']       = $productInfo['data'][0]['short_code'];
                $goodsInfo[$k]['money']            = $details[2];
                $goodsInfo[$k]['goods_years']      = $productInfo['data'][0]['grape_picking_years'];
                $goodsInfo[$k]['volume']           = $productInfo['data'][0]['capacity'];
                $goodsInfo[$k]['jd_emg_code']      = $productInfo['data'][0]['jd_eclp_code'];
                $goodsInfo[$k]['goods_attr']       = $productInfo['data'][0]['product_type'];
                $goodsInfo[$k]['product_category'] = $productInfo['data'][0]['product_category'];
            }
            $payee_merchant_id = SalesReturn::getOrderCrop([
                'sale_bill_type' => 3,
                'sub_order_no' => $params['sub_order_no'],
            ]);
            //参数组装
            $result = array(
                'latest_delivery_time'=> $orderInfo['predict_time'],
                'payee_merchant_id'=> $payee_merchant_id,
                'main_order_no'    => $orderInfo['main_order_no'],
                'sub_order_no'     => $params['sub_order_no'],
                'sub_order_nums'   => $sub_order_nums,
                'related_order_no' => $orderInfo['related_order_no'],
                'platform_id'      => $orderInfo['order_from_thirdparty'],
                'is_storage'       => $orderInfo['is_ts'],
                'is_SF_cold_chain' => in_array($orderInfo['express_type'],[3,31]) ? 1 : 0,
                'push_type'        => 0,
                'order_type'       => 0,
                'channel_type'     => 0,
                'is_expedited'     => 0,
                'is_to_pay'        => 0,
                'order_status'     => $orderInfo['sub_order_status'],
                'attach_info'      => [],
                'warehouse_code'   => $orderInfo['warehouse_id'],
                'pay_money'        => $orderInfo['payment_amount'],
                'pay_method'       => $orderInfo['payment_method'],
                'owner_id'         => $orderInfo['store_id'],
                'store_name'       => $orderInfo['store_name'],
                'express_type'     => $orderInfo['express_type'],
                'waybill_no'       => $orderInfo['express_number'],
                'predict_time'     => '',
                'delivery_time'    => $orderInfo['delivery_time'],
                'freeze_status'    => 0,
                'created_time'     => $orderInfo['created_time'],
                'push_wms_status'  => $orderInfo['push_wms_status'],
                'is_supplier_delivery'  => $orderInfo['is_supplier_delivery'] ?? 0,
                'addressInfo'      => [
                    'receiver_name'  => $orderInfo['consignee'],
                    'receiver_phone' => $orderInfo['consignee_phone'],
                    'province'       => $orderInfo['province'],
                    'city'           => $orderInfo['city'],
                    'town'           => $orderInfo['district'],
                    'address'        => $orderInfo['address'],
                    'remark'         => ''
                ],
                'goodsInfo'        => $goodsInfo
            );
            if (isset($params['is_wms']) && $params['is_wms'] == 1) {//推送萌牙数据
                $zt2wms_express_type    = [
                    '67' => '24', //壹米滴答
                ];
                $result['express_type'] = $zt2wms_express_type[$result['express_type']] ?? $result['express_type'];
            }
        } else if ($params['order_type'] == 8) {
            //获取订单信息
            $orderInfo = Db::name('offline_order')
                ->alias('oo')
                ->field('oo.*,om.payment_method,om.province_id,om.city_id,om.district_id,om.address,om.consignee,om.consignee_phone')
                ->join('order_main om', 'om.id=oo.main_order_id')
                ->where(['sub_order_no' => $params['sub_order_no']])
                ->find();
            if (empty($orderInfo)) $this->throwError('未获取到订单信息');
            //用户加密信息处理
            if (!empty($orderInfo['consignee'])) {
                //用户信息加密处理
                $consignee              = $orderInfo['consignee'];
                $encrypt                = cryptionDeal(2, [$consignee], '15736175219', '宗仁川');
                $consignee              = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
                $orderInfo['consignee'] = $consignee;
            }
            if (!empty($orderInfo['consignee_phone'])) {
                //用户信息加密处理
                $phone                        = $orderInfo['consignee_phone'];
                $encrypt                      = cryptionDeal(2, [$phone], '15736175219', '宗仁川');
                $phone                        = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
                $orderInfo['consignee_phone'] = $phone;
            }
            //省市区处理
            $orderInfo['province'] = !empty($orderInfo['province_id']) ? getRegionalInfo($orderInfo['province_id']) : '';
            $orderInfo['city']     = !empty($orderInfo['city_id']) ? getRegionalInfo($orderInfo['city_id']) : '';
            $orderInfo['town']     = !empty($orderInfo['district_id']) ? getRegionalInfo($orderInfo['district_id']) : '';
            $payee_merchant_id = Db::name('collecting_company')->where('corp', $orderInfo['corp'])->value('id');

            $oss_info = Db::name('offline_sealed_source')->where('main_order_id', $orderInfo['main_order_id'])->find();
            if (!empty($oss_info)) {
                $zl_id                     = Db::table('vh_pushorders.vh_attach_product')->where('short_code', 'SYM-08')->value('product_id');
                $orderInfo['instructions'] = trim($orderInfo['instructions'] . ',' . $zl_id, ',');
            }
            if (isset($params['is_wms']) && $params['is_wms'] == 1) {//推送萌牙数据
                //获取产品信息
                $items_info = explode(',', $orderInfo['items_info']);
                $goodsInfo  = [];
                $short_code = [];
                foreach ($items_info as &$vv) {
                    $details      = explode('*', $vv);
                    $short_code[] = $details[1];
                }
                $wikiData    = array(
                    'short_code' => implode(',', $short_code),
                    'field'      => 'bar_code,short_code,grape_picking_years,capacity,jd_eclp_code,product_type,product_category',
                );
                $productInfo = $this->httpGet(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/query', $wikiData);
                if ($productInfo['error_code'] != 0 || count($productInfo['data']) == 0) $this->throwError('未获取到产品信息');
                foreach ($items_info as $k => $v) {
                    $details = explode('*', $v);
                    foreach ($productInfo['data'] as &$vvv) {
                        if ($vvv['short_code'] == $details[1]) {
                            $goodsInfo[$k]['goods_id']         = '';
                            $goodsInfo[$k]['goods_code']       = $vvv['bar_code'];
                            $goodsInfo[$k]['number']           = $details[2] * $orderInfo['order_qty'];
                            $goodsInfo[$k]['short_code']       = $vvv['short_code'];
                            $goodsInfo[$k]['money']            = $details[3] * $goodsInfo[$k]['number'];
                            $goodsInfo[$k]['goods_years']      = $vvv['grape_picking_years'];
                            $goodsInfo[$k]['volume']           = $vvv['capacity'];
                            $goodsInfo[$k]['jd_emg_code']      = $vvv['jd_eclp_code'];
                            $goodsInfo[$k]['goods_attr']       = $vvv['product_type'];
                            $goodsInfo[$k]['product_category'] = $vvv['product_category'];
                        }
                    }
                }

                //物料明细
                $material_info = json_decode($orderInfo['material_info'], true);
                if (!empty($material_info)) {
                    foreach ($material_info as $kk => $vv) {
                        $wikiData    = array(
                            'short_code' => $vv['short_code'],
                            'field'      => 'bar_code,short_code,grape_picking_years,capacity,jd_eclp_code,product_type,product_category',
                        );
                        $productInfo = $this->httpGet(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/query', $wikiData);
                        if ($productInfo['error_code'] != 0 || count($productInfo['data']) == 0) $this->throwError('未获取到物料产品信息');
                        $goodsInfo[] = array(
                            'goods_id'         => '',
                            'goods_code'       => $productInfo['data'][0]['bar_code'],
                            'number'           => $vv['nums'],
                            'short_code'       => $vv['short_code'],
                            'money'            => 0,
                            'goods_years'      => $productInfo['data'][0]['grape_picking_years'],
                            'volume'           => $productInfo['data'][0]['capacity'],
                            'jd_emg_code'      => $productInfo['data'][0]['jd_eclp_code'],
                            'goods_attr'       => $productInfo['data'][0]['product_type'],
                            'product_category' => $productInfo['data'][0]['product_category'],
                        );
                    }
                }

                $push_pay_money = $orderInfo['payment_amount'];
                if (strpos($params['sub_order_no'], 'VHG') === false) {

                    $work = Db::table('vh_customer_service.vh_work_order')->where([
                        ['work_order_type', 'in', [6, 7]],
                        ['order_no', '=', $params['sub_order_no']],
                    ])->order('id', 'desc')->find();
                    if (!empty($work)) {
                        if ($work['work_order_type'] == 6) {
                            $erp_push_amount = Db::table('vh_customer_service.vh_exchange_goods')->where([
                                ['gd_id', '=', $work['id']]
                            ])->value('erp_push_amount');
                        } else {
                            //7 补发
                            $erp_push_amount = Db::table('vh_customer_service.vh_reissue')->where([
                                ['gd_id', '=', $work['id']]
                            ])->value('erp_push_amount');
                        }
                        if ($erp_push_amount !== null) {
                            $push_pay_money = $erp_push_amount;
                        }
                    }
                }
                //参数组装
                $result = array(
                    'sealed_source_code'     => $oss_info['sealed_source_code'] ?? '',
                    'payee_merchant_id'     => $payee_merchant_id,
                    'sub_order_no'     => $params['sub_order_no'],
                    'platform_id'      => 14,
                    'is_storage'       => $orderInfo['is_ts'],
                    'is_SF_cold_chain' => in_array($orderInfo['express_type'],[3,31]) ? 1 : 0,
                    'push_type'        => 0,
                    'order_type'       => 0,
                    'channel_type'     => 0,
                    'is_expedited'     => 0,
                    'is_to_pay'        => 0,
                    'order_status'     => $orderInfo['sub_order_status'],
                    'attach_info'      => [],
                    'warehouse_code'   => $orderInfo['warehouse_code'],
                    'department_code'  => $orderInfo['department_code'],
                    'pay_money'        => $push_pay_money,
                    'pay_method'       => 0,
                    'owner_id'         => '',
                    'store_name'       => '',
                    'express_type'     => $orderInfo['express_type'],
                    'waybill_no'       => $orderInfo['express_number'],
                    'predict_time'     => '',
                    'delivery_time'    => $orderInfo['delivery_time'],
                    'created_time'     => $orderInfo['created_time'],
                    'corp'             => $orderInfo['corp'],
                    'document_type'    => $orderInfo['document_type'],
                    'instruction_add'  => $orderInfo['instructions'],
                    'push_wms_status'  => $orderInfo['push_wms_status'],
                    'addressInfo'      => [
                        'receiver_name'  => $orderInfo['consignee'],
                        'receiver_phone' => $orderInfo['consignee_phone'],
                        'province'       => $orderInfo['province'],
                        'city'           => $orderInfo['city'],
                        'town'           => $orderInfo['town'],
                        'address'        => $orderInfo['address'],
                        'remark'         => ''
                    ],
                    'goodsInfo'        => $goodsInfo
                );
            } else {//推送T+数据
                //获取产品信息
                $items_info       = explode(',', $orderInfo['items_info']);
                $saleOrderDetails = [];
                foreach ($items_info as $k => $v) {
                    $details            = explode('*', $v);
                    $saleOrderDetails[] = array(
                        'Warehouse'        => ['code' => $orderInfo['warehouse_code']],
                        'InventoryBarCode' => $details[0],
                        'Unit'             => ['Name' => '瓶'],
                        'IsPresent'        => $details[4] == 1 ? true : false,
                        'Quantity'         => $details[2] * $orderInfo['order_qty'],
                        'OrigTaxAmount'    => $orderInfo['document_type'] == 1 ? isset($details[12]) ? $details[12] : $details[3] * $details[2] * $orderInfo['order_qty'] : $details[3] * $details[2] * $orderInfo['order_qty'],
                    );
                }
                //参数组装
                $result = array(
                    'payee_merchant_id'     => $payee_merchant_id,
                    't_plus_code'     => $orderInfo['corp'],
                    'vh_order_type'   => 8,
                    'sub_order_no'    => $orderInfo['sub_order_no'],
                    'waybill_no'      => $orderInfo['express_number'],
                    'freeze_status'   => 0,
                    'created_time'    => $orderInfo['created_time'],
                    'delivery_time'   => $orderInfo['delivery_time'],
                    'approval_time'   => $orderInfo['approval_time'],
                    'collection_type' => $orderInfo['collection_type'],
                    't_plus_param'    => [
                        'VoucherDate'           => date('Y-m-d H:i:s', $orderInfo['voucher_date']),
                        'ExternalCode'          => $orderInfo['sub_order_no'],
                        'Code'                  => $orderInfo['sub_order_no'],
                        'Customer'              => ['Code' => $orderInfo['customer_code']],
                        'SettleCustomer'        => ['Code' => $orderInfo['settle_customer_code']],
                        'Clerk'                 => ['Code' => $orderInfo['clerk_code']],
                        'Department'            => ['Code' => $orderInfo['department_code']],
                        'ReciveType'            => ['Code' => $orderInfo['settlement_method_code']],
                        'DeliveryMode'          => ['Code' => $orderInfo['delivery_mode_code']],
                        'DataSource'            => ['Code' => '02'],
                        'Address'               => $orderInfo['address'],
                        'LinkMan'               => $orderInfo['consignee'],
                        'ContactPhone'          => $orderInfo['consignee_phone'],
                        'SaleOrderDetails'      => $saleOrderDetails,
                        'DynamicPropertyKeys'   => ["isautoaudit", "priuserdefnvc3", "priuserdefnvc5", "priuserdefnvc1"],
                        'DynamicPropertyValues' => [true, '线下业务', '', $orderInfo['express_pay_method']],
                        'Memo'                  => $orderInfo['memo']
                    ]
                );
            }
        } else if ($params['order_type'] == 11) {//拍卖订单
            $auctionInfo = $this->httpPost(env('ITEM.AUCTION_ORDERS_URL') . '/auction-order/v3/order/getPushData', ['order_no' => $params['sub_order_no']]);
            if (!isset($auctionInfo['error_code']) || $auctionInfo['error_code'] != 0) $this->throwError('未获取到订单信息!' . isset($auctionInfo['error_msg']) ? $auctionInfo['error_msg'] : '');
            $result = $auctionInfo['data'];
        }

        if (!empty($result)) {
            $result['force_merge_orders'] = '';
            if (!empty($result['sub_order_no'])) {
                $result['force_merge_orders'] = implode(',', array_unique(Db::name('merge_order')->where('sub_order_no', 'in', Db::name('merge_order')->where('sub_order_no', $result['sub_order_no'])->column('merge_order_no'))->column('merge_order_no') ?? []));
            }

            $result['special_type'] = 0;
            if (strpos($params['sub_order_no'], 'VHG') === false) {
            if (!empty($orderInfo['period'])) {
                if ($orderInfo['is_gift'] ?? 0) $result['special_type'] = 1;

                if ($result['special_type'] == 0) {
                    if (Db::table('vh_commodities.vh_periods_add_purchase')->where('period', $orderInfo['period'])->where('is_force_merge', 1)->count() > 0) {
                        $result['special_type'] = 2;
                    }
                }
            }
            }

        }
        return $result;
    }

    /**
     * Description:订单推送萌牙
     * Author: zrc
     * Date: 2022/4/28
     * Time: 11:47
     * @param $requestparams
     * @return bool
     * @throws \Exception
     */
    public function pushWms($requestparams)
    {
        //main_order_no
        $params = $requestparams;
        $sub_order_field = 'main_order_no,period,sub_order_no,is_gift,order_type';
        $sub_order       = Es::name(Es::ORDERS)->where([['_id', '=', $params['sub_order_no']]])->field($sub_order_field)->find();
        try {
            if (!isset($params['order_type'])) {
                //查询订单类型
                $searchData  = array(
                    'sub_order_no' => $params['sub_order_no'],
                    'fields'       => 'order_type,sub_order_status,refund_status,freeze_status,is_reject,dingtalk_status,store_id'
                );
                $batchSearch = httpGet(env('ITEM.MYSQL_BATCH_SEARCH') . '/services/v3/batchsearch/orders/fields', $searchData);
                if (!isset($batchSearch['error_code']) || $batchSearch['error_code'] != 0) $this->throwError('未获取到订单类型');
                if (!isset($batchSearch['data']['type'])) $this->throwError('未获取到订单信息');
                switch ($batchSearch['data']['type']) {
                    case 0:
                    case 1:
                    case 3:
                        if (in_array($batchSearch['data']['sub_order_status'], [0, 4]) || in_array($batchSearch['data']['refund_status'], [1, 2])) {
                            $this->throwError('订单状态异常，不允许推送');
                        }
                        if (isset($batchSearch['data']['freeze_status']) && $batchSearch['data']['freeze_status'] == 1) {
                            $this->throwError('订单已冻结，不允许推送');
                        }
                        break;
                    case 2:
                        $this->throwError('跨境订单不允许推送');
                        break;
                    case 7:
                        if (in_array($batchSearch['data']['sub_order_status'], [0, 4])) {
                            $this->throwError('订单状态异常，不允许推送');
                        }
                        $tripartite_order_status_search = (env("APP_DEBUG") === true) ? [] : config('config')['tripartite_order_status_search'];
                        //三方订单状态查询(过滤合并订单、赠品订单)
                        if (in_array($batchSearch['data']['store_id'], $tripartite_order_status_search) && (strpos($params['sub_order_no'], '-合') === false && strpos($params['sub_order_no'], '-ZP') === false && strpos($params['sub_order_no'], '补') === false && strpos($params['sub_order_no'], '换') === false)) {
                            // 查询主订单号
                            $main_order_no = Db::name('tripartite_order')->alias('t')
                                ->leftJoin('order_main om','om.id=t.main_order_id')
                                ->where('t.sub_order_no', $params['sub_order_no'])
                                ->value('main_order_no');
                            $orderStatusBatch = httpPostString(env('ITEM.ORDERS_MICRO_SERVICE_URL') . '/orders_server/v3/trilateral/get_trilateral_order_status', json_encode(['param' => [['main_order_no' => $main_order_no ?? '', 'order_no' => $params['sub_order_no'], 'owner_id' => $batchSearch['data']['store_id']]]]));
                            if (!isset($orderStatusBatch['error_code']) || $orderStatusBatch['error_code'] != 0 || !isset($orderStatusBatch['data']['list'][$params['sub_order_no']])) {
                                $this->throwError('未查询到三方订单状态');
                            }
                            if ($orderStatusBatch['data']['list'][$params['sub_order_no']]['status'] != 1 || $orderStatusBatch['data']['list'][$params['sub_order_no']]['refund_status'] != 0) {
                                $this->throwError('三方订单状态查询异常：订单不是已支付状态，不允许推送');
                            }
                        }
                        break;
                    case 8:
                        if (in_array($batchSearch['data']['sub_order_status'], [0, 4])) {
                            $this->throwError('订单状态异常，不允许推送');
                        }
                        if ($batchSearch['data']['dingtalk_status'] != 2) {
                            $this->throwError('订单审批未通过，不允许推送');
                        }
                        if ($batchSearch['data']['is_reject'] == 1) {
                            $this->throwError('订单已弃审，不允许推送');
                        }
                        break;
                }
                $params['order_type'] = $batchSearch['data']['type'];
            }
        } catch (\Exception $e) {
            //后台期数批量推送回执信息处理
            if (isset($params['period'])) {
                $this->wmsPeriodPushReceipt($params, 10002, $e->getMessage());
            }
            $this->throwError($params['sub_order_no'] . $e->getMessage());
        }

        $sub_order_group = [];
        if ( in_array($params['order_type'], [0,1,3]) && !empty($sub_order) && !empty($sub_order['main_order_no'])) {
            $sub_order_list  = Es::name(Es::ORDERS)->where([['main_order_no', '==', $sub_order['main_order_no']]])->field($sub_order_field)->select()->toArray();
            $order_type_conf = config('config')['order_type'];
            $all_periods     = array_column($sub_order_list, 'period');
            $ss_periods      = Db::table('vh_commodities.vh_periods_add_purchase')->where('period', 'in', $all_periods)->where('is_force_merge', 1)->column('period');
            $erp_ids         = Db::table('vh_commodities.vh_periods_product_inventory')->where('period', 'in', $all_periods)->column('erp_id', 'period');

            foreach ($sub_order_list as $sbo_item) {
                $vv_erp_id                   = $erp_ids[$sbo_item['period']];
                $sbo_item['erp_id']          = $vv_erp_id;
                $sbo_item['push_wms_status'] = Db::name($order_type_conf[intval($sbo_item['order_type'])]['table'])->where(['sub_order_no' => $sbo_item['sub_order_no']])->value('push_wms_status');
                if ($sbo_item['is_gift'] == 1) {
                    $sub_order_group[$sbo_item['is_gift']][] = $sbo_item;
                } elseif (in_array($sbo_item['period'], $ss_periods)) {
                    $sub_order_group[2][] = $sbo_item;
                } else {
                    $sub_order_group[0][] = $sbo_item;
                }
            }
        }

        $is_gift          = $sub_order['is_gift'] ?? 0;
        $sub_order_erp_id = $erp_ids[$sub_order['period'] ?? ''] ?? null;
        $allow_push       = true;
        $auto_push_arr    = [];
        $all_mo_data      = [];
        $now_time         = time();
        if (!empty($sub_order_group[1])) {
            $cp_erp_ids       = array_column($sub_order_group[0] ?? [], 'erp_id');
            $cp_push_wms_done = implode(',', array_values(array_unique(array_column($sub_order_group[0] ?? [], 'push_wms_status')))) == '1';
            $merge_group      = [];
            foreach ($sub_order_group[1] as $zp_order) {
                if (in_array($zp_order['erp_id'], $cp_erp_ids)) {
                    $merge_group[$zp_order['erp_id']][1][] = $zp_order;
                    foreach ($sub_order_group[0] ?? [] as $cp_order) {
                        if ($zp_order['erp_id'] == $cp_order['erp_id']) {
                            if ($cp_order['push_wms_status'] != '1') {
                                $merge_group[$zp_order['erp_id']][0][] = $cp_order;
                            }
                        }
                    }
                }
            }

            if (!empty($merge_group[$sub_order_erp_id])) {
                if ($is_gift) {
                    if (!empty($merge_group[$sub_order_erp_id][0] ?? [])) {
                        $allow_push = false;
                    }
                } else {
                    if (count($merge_group[$sub_order_erp_id][0] ?? []) == 1) {
                        if (implode(',', array_column($merge_group[$sub_order_erp_id][0] ?? [], 'sub_order_no')) == $sub_order['sub_order_no']) {
                            $auto_push_arr = $merge_group[$sub_order_erp_id][1];
                        }
                        $mo_arr = array_unique(array_column(array_merge(...($merge_group[$sub_order_erp_id])), 'sub_order_no'));
                        if(count( $mo_arr)> 1){
                            foreach ($mo_arr as $o1) {
                                $all_mo_data[] = $o1;
                                foreach ($mo_arr as $o2) {
                                    $force_merge_data[] = ['sub_order_no' => $o1, 'merge_order_no' => $o2, 'type' => 2, 'created_time' => $now_time,];
                                }
                            }
                            if (!empty($force_merge_data)) {
                                Db::name('merge_order')->insertAll($force_merge_data);
                            }
                        }
                    }
                }
            } else {
                if ($is_gift) {
                    $allow_push = $cp_push_wms_done;
                } else {
                    $c_or = [];
                    foreach ($sub_order_group[0] ?? [] as $cporder) {
                        if ($cporder['push_wms_status'] != '1') {
                            $c_or[] = $cporder['sub_order_no'];
                        }
                    }
                    if (implode(',', $c_or) == $sub_order['sub_order_no']) {
                        $auto_push_arr = $sub_order_group[1];
                    }
                }
            }
        }
        if (!empty($sub_order_group[2]) && in_array($sub_order['sub_order_no'], array_column(($sub_order_group[0] ?? []), 'sub_order_no'))) {
            $first = true;
            foreach ($sub_order_group[0] ?? [] as $sog0) {
//                if (($sog0['erp_id'] == $sub_order_erp_id) && ($sog0['push_wms_status'] == 1)) {
                if (($sog0['push_wms_status'] == 1)) {
                    $first = false;
                }
            }
            if ($first) {
                $ssmo_arr_merge_data = [];
                $ssmo_arr            = [$sub_order['sub_order_no']];
                foreach ($sub_order_group[2] as $sog2) {
//                    if ($sog2['erp_id'] == $sub_order_erp_id) {
                        $ssmo_arr[] = $sog2['sub_order_no'];
//                    }
                }
                if (count($ssmo_arr) > 1) {
                    foreach ($ssmo_arr as $o1) {
                        $all_mo_data[] = $o1;
                        foreach ($ssmo_arr as $o2) {
                            $ssmo_arr_merge_data[] = ['sub_order_no' => $o1, 'merge_order_no' => $o2, 'type' => 1, 'created_time' => $now_time,];
                        }
                    }
                    if (!empty($ssmo_arr_merge_data)) {
                        Db::name('merge_order')->insertAll($ssmo_arr_merge_data);
                    }
                }
            }
        }

        //retry  5-重试优先 1-自动重试 0-下单推送
        if ($allow_push) {
            //已有运单号, 已发货状态  禁止推送萌芽
            if (in_array($params['order_type'], [0, 1, 3, 4, 7, 8, 9])) {
                //获取订单信息
                $order_type = config('config')['order_type'];//订单频道获取
                $orderInfo  = Db::name($order_type[intval($params['order_type'])]['table'])
                    ->alias('so')
                    ->field('so.express_number,so.sub_order_status')
                    ->leftJoin('order_main om', 'om.id=so.main_order_id')
                    ->where(['so.sub_order_no' => $params['sub_order_no']])
                    ->find();
                if (empty($orderInfo) || (intval($params['order_type']) != 8 && !empty($orderInfo['express_number'])) || ($orderInfo['sub_order_status'] != 1)) {
                    $allow_push = false;
                    $pushWms    = array(
                        'error_code' => 10002,
                        'error_msg'  => '未找到订单或订单不是未发货状态,不能推送',
                    );
                }
            }

            if ($allow_push) {
                $pushWms = httpPostString(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/order/push', json_encode(['sub_order_no' => $params['sub_order_no'], 'order_type' => $params['order_type'], 'retry' => isset($params['retry']) ? $params['retry'] : 0]));
                if (!isset($pushWms['error_code'])) {
                    $pushWms = array(
                        'error_code' => 10002,
                        'error_msg'  => '推送发货仓失败',
                    );
                }
                if ($pushWms['error_code'] == 0) {
                    if (isset($pushWms['data']['wms_type']) && $pushWms['data']['wms_type'] == 1) $pushWms['error_msg'] = '推送萌牙成功';
                    if (isset($pushWms['data']['wms_type']) && $pushWms['data']['wms_type'] == 2) $pushWms['error_msg'] = '推送京东成功';
                }
            }
        } else {
            $pushWms = array(
                'error_code' => 10002,
                'error_msg'  => '赠品订单不允许在产品秒发订单未全部推送完成的情况下推送萌芽',
            );
        }
        //后台期数批量推送回执信息处理
        if (isset($params['period'])) {
            $this->wmsPeriodPushReceipt($params, $pushWms['error_code'], $pushWms['error_msg']);
        }
        $wmsService = new WmsService();
        //推送萌牙结果处理
        $resultData = array(
            'push_wms_status' => $pushWms['error_code'] == 0 ? 1 : 2,
            'sub_order_no'    => $params['sub_order_no'],
            'order_type'      => $params['order_type'],
            'msg'             => isset($pushWms['error_msg']) ? $pushWms['error_msg'] : '推送发货仓失败',
            'operator'        => isset($params['operator']) ? $params['operator'] : 0
        );
//        if ($pushWms['error_code'] != 0 && stripos($pushWms['error_msg'], 'tp6-pushorders') !== false) {
//            \Curl::sendWechatSender([
//                'msg'          => "订单[{$resultData['sub_order_no']}]推送萌芽失败:  {$resultData['msg']}",
//                'at'           => '15922995135',//lf
//                'access_token' => env('PUSH_ORDERS.SECONDS_FAIL_ROBOT_TOKEN'),
//            ]);
//        }
        $wmsService->pushWmsResultDeal($resultData);

        if (!empty($all_mo_data) && ($resultData['push_wms_status'] != 1)) {
            Db::name('merge_order')->where('created_time', $now_time)->where('sub_order_no', 'in', $all_mo_data)->delete();
        }

        if (!empty($auto_push_arr) && ($resultData['push_wms_status'] == 1)) {
            foreach ($auto_push_arr as $gift_order) {
                if (in_array($gift_order['push_wms_status'], [0, 2])) {
                    $this->pushWms(['sub_order_no' => $gift_order['sub_order_no'], 'order_type' => $gift_order['order_type']]);
                }
            }
        }
        return $pushWms;
    }

    /**
     * Description:后台期数批量推送萌牙回执信息redis处理
     * Author: zrc
     * Date: 2022/7/26
     * Time: 14:21
     * @param $params
     * @param $error_code
     * @param $msg
     * @return bool
     */
    public function wmsPeriodPushReceipt($params, $error_code, $msg)
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        if (isset($params['mark']) && $params['mark'] == 0) {
            $redis->del('wms.push.temporary.log' . $params['period']);
            $redis->del('wms.push.log' . $params['period']);
        }
        $redis->rPush('wms.push.temporary.log' . $params['period'], json_encode([$params['sub_order_no'], $error_code, $msg], JSON_UNESCAPED_UNICODE));
        $redis->expireAt('wms.push.temporary.log' . $params['period'], time() + 300);
        if (isset($params['mark']) && $params['mark'] == 1) {
            $data = $redis->lrange('wms.push.temporary.log' . $params['period'], 0, -1);
            foreach ($data as &$val) {
                $val = json_decode($val);
                $redis->rPush('wms.push.log' . $params['period'], json_encode($val, JSON_UNESCAPED_UNICODE));
            }
            $redis->expireAt('wms.push.log' . $params['period'], time() + 300);
            $redis->del('wms.push.temporary.log' . $params['period']);
        }
        return true;
    }

    /**
     * Description:获取后台期数批量推送萌牙回执信息
     * Author: zrc
     * Date: 2022/7/26
     * Time: 14:54
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getWmsPeriodPushReceipt($params)
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        $data = $redis->lrange('wms.push.log' . $params['orderOrPeriod'], 0, -1);
        if (empty($data)) $this->throwError('暂无推送回执信息','50009');
        $result = [];
        foreach ($data as $key => $val) {
            $val      = json_decode($val);
            $result[] = array(
                'sub_order_no' => $val[0],
                'status'       => $val[1],
                'msg'          => $val[2],
            );
        }
        $redis->del('wms.push.log' . $params['orderOrPeriod']);
        return $result;
    }

    /**
     * Description:订单推送T+
     * Author: zrc
     * Date: 2022/5/5
     * Time: 16:29
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function pushTplus($requestparams)
    {
        $params = $requestparams;
        try {
            if (!isset($params['order_type'])) {
                //查询订单类型
                $searchData  = array(
                    'sub_order_no' => $params['sub_order_no'],
                    'fields'       => 'order_type,sub_order_status,refund_status,freeze_status,is_reject,dingtalk_status,express_number,store_id,corp,document_type'
                );
                $batchSearch = httpGet(env('ITEM.MYSQL_BATCH_SEARCH') . '/services/v3/batchsearch/orders/fields', $searchData);
                if (!isset($batchSearch['error_code']) || $batchSearch['error_code'] != 0) $this->throwError('未获取到订单类型');
                if (!isset($batchSearch['data']['type'])) $this->throwError('未获取到订单信息');
                //订单推送验证
                switch ($batchSearch['data']['type']) {
                    case 0:
                    case 1:
                    case 2:
                    case 3:
                    case 9:
                        if (isset($batchSearch['data']['express_number']) && empty($batchSearch['data']['express_number'])) {
                            $this->throwError('订单未发货，不允许推送');
                        }
                        if (isset($batchSearch['data']['freeze_status']) && $batchSearch['data']['freeze_status'] == 1) {
                            $this->throwError('订单已冻结，不允许推送');
                        }
                        break;
                    case 7:
                        //天猫国际不推送erp
                        if (isset($batchSearch['data']['store_id']) && $batchSearch['data']['store_id'] == '2807304908') $this->throwError('天猫国际订单不允许推送erp');
                        if (isset($batchSearch['data']['express_number']) && empty($batchSearch['data']['express_number'])) {
                            $this->throwError('订单未发货，不允许推送');
                        }
                        break;
                    case 8:
                        if (in_array($batchSearch['data']['sub_order_status'], [0, 4])) {
                            $this->throwError('订单状态异常，不允许推送');
                        }
                        if ($batchSearch['data']['dingtalk_status'] != 2) {
                            $this->throwError('订单审批未通过，不允许推送');
                        }
                        if ($batchSearch['data']['is_reject'] == 1) {
                            $this->throwError('订单已弃审，不允许推送');
                        }
                        //判断是否是科技样酒销售单推送出库单
                        if ($batchSearch['data']['document_type'] == 0 && $batchSearch['data']['corp'] != '002') {
                            $params['corp'] = $batchSearch['data']['corp'];
                        }
                        break;
                }
                $params['order_type'] = $batchSearch['data']['type'];
            }
        } catch (\Exception $e) {
            //后台期数批量推送回执信息处理
            if (isset($params['period'])) {
                $this->erpPeriodPushReceipt($params, 10002, $e->getMessage());
            }
            $this->throwError($params['sub_order_no'] . $e->getMessage());
        }
        $push_status = 1;
        if (isset($params['corp'])) {//科技样酒推送出库单
            $url           = env('ITEM.ERP_URL') . '/erp/v3/icOtherOut/createHandle';
            $pushTplusData = ['corp' => $params['corp'], 'sub_order_no' => $params['sub_order_no'], 'vh_order_type' => $params['order_type']];
            $pushTplus     = curlRequest($url, json_encode($pushTplusData, true), [], 'POST', 30);
            if (!isset($pushTplus['error_code'])) {
                $pushTplus = array(
                    'error_code' => 10002,
                    'error_msg'  => '推送出库单失败,erp模块访问异常'
                );
            }
            //推送T+结果处理
            $msg = $pushTplus['error_msg'];
            if ($pushTplus['error_code'] == 0) $msg = '推送出库单成功';
        } else {

            $is_support_invoicing = 1;
            if(isset($params['sub_order_no'])){
                $period = Es::name(Es::ORDERS)->where([['_id','==',$params['sub_order_no']]])->value('period');
                if(!empty($period)){
                    $period_es            = Es::name(Es::PERIODS)->where([['id', '==', $period]])->field('id,is_support_invoicing,periods_type')->find();
                    if (!in_array($period_es['periods_type'], [2])) {
                    if (isset($period_es['is_support_invoicing'])) {
                        $is_support_invoicing = $period_es['is_support_invoicing'];
                    } else {
                        if (in_array($period_es['periods_type'], [0, 1, 3])) {
                            $table                = [0 => 'vh_periods_flash', 1 => 'vh_periods_second', 2 => 'vh_periods_cross', 3 => 'vh_periods_leftover'];
                            $is_support_invoicing = Db::table('vh_commodities.' . $table[$period_es['periods_type']])->where('id', $period_es['id'])->value('is_support_invoicing');
                        }
                    }
                    }
                }
            }
            if ($is_support_invoicing == 1) {
            //erp推送
            $pushTplus = httpPostString(env('ITEM.PUSH_T_PLUS_URL') . '/pushtplus/v3/push/order', json_encode(['push_type' => 2, 'sub_order_no' => $params['sub_order_no'], 'vh_order_type' => $params['order_type']]), '', 30);
            if (!isset($pushTplus['error_code'])) {
                $pushTplus = array(
                    'error_code' => 10002,
                    'error_msg'  => '推送erp失败,erp模块访问异常'
                );
            }
            }else{
                $pushTplus = array(
                    'error_code' => 10002,
                    'error_msg'  => '商品不允许开票，不允许推送'
                );
            }
            //后台期数批量推送回执信息处理
            if (isset($params['period'])) {
                $this->erpPeriodPushReceipt($params, $pushTplus['error_code'], $pushTplus['error_msg']);
            }
            //推送T+结果处理
            $msg = $pushTplus['error_msg'];
            if ($pushTplus['error_code'] == 0) {
                if (isset($pushTplus['data']['push_t_status'])) {
                    $msg         = $pushTplus['data']['remark'];
                    $push_status = $pushTplus['data']['push_t_status'];
                } else {
                    $msg = '推送erp成功';
                    if (isset($pushTplus['data']['erp_type']) && $pushTplus['data']['erp_type'] == 1) $msg = '推送T+成功';
                    if (isset($pushTplus['data']['erp_type']) && $pushTplus['data']['erp_type'] == 2) $msg = '推送U8C成功';
                }
            }
        }
        //修改订单推送T+状态
        $order_type    = config('config')['order_type'];//订单频道获取
        $push_t_status = Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->value('push_t_status');
        if ($push_t_status != 1) {
            Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->update(['push_t_status' => $pushTplus['error_code'] == 0 ? $push_status : 2, 'update_time' => time()]);
        }
        // todo 推送T+状态回写
        //添加订单备注
        $remarks      = array(
            'sub_order_no' => $params['sub_order_no'],
            'order_type'   => $params['order_type'],
            'content'      => 'erp推送：' . $msg,
            'admin_id'     => isset($params['operator']) ? $params['operator'] : 0
        );
        $orderService = new OrderService();
        $orderService->createRemarks($remarks);
        return $pushTplus;
    }

    /**
     * Description:后台期数批量推送萌牙回执信息redis处理
     * Author: zrc
     * Date: 2022/7/26
     * Time: 14:21
     * @param $params
     * @param $error_code
     * @param $msg
     * @return bool
     */
    public function erpPeriodPushReceipt($params, $error_code, $msg)
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        if (isset($params['mark']) && $params['mark'] == 0) {
            $redis->del('erp.push.temporary.log' . $params['period']);
            $redis->del('erp.push.log' . $params['period']);
        }
        $redis->rPush('erp.push.temporary.log' . $params['period'], json_encode([$params['sub_order_no'], $error_code, $msg], JSON_UNESCAPED_UNICODE));
        $redis->expireAt('erp.push.temporary.log' . $params['period'], time() + 300);
        if (isset($params['mark']) && $params['mark'] == 1) {
            $data = $redis->lrange('erp.push.temporary.log' . $params['period'], 0, -1);
            foreach ($data as &$val) {
                $val = json_decode($val);
                $redis->rPush('erp.push.log' . $params['period'], json_encode($val, JSON_UNESCAPED_UNICODE));
            }
            $redis->expireAt('erp.push.log' . $params['period'], time() + 300);
            $redis->del('erp.push.temporary.log' . $params['period']);
        }
        return true;
    }

    /**
     * Description:获取后台期数批量推送T+回执信息
     * Author: zrc
     * Date: 2022/7/26
     * Time: 14:54
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getErpPeriodPushReceipt($params)
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        $data = $redis->lrange('erp.push.log' . $params['orderOrPeriod'], 0, -1);
        if (empty($data)) $this->throwError('暂无推送回执信息');
        $result = [];
        foreach ($data as $key => $val) {
            $val      = json_decode($val);
            $result[] = array(
                'sub_order_no' => $val[0],
                'status'       => $val[1],
                'msg'          => $val[2],
            );
        }
        $redis->del('erp.push.log' . $params['orderOrPeriod']);
        return $result;
    }

    /**
     * Description:跨境推送南沙代发仓
     * Author: zrc
     * Date: 2022/4/20
     * Time: 15:52
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function crossPushNanSha($requestparams)
    {
        $params    = $requestparams;
        $orderInfo = $params['order_info'];
        try {
            //用户信息解密处理
            $encrypt                      = cryptionDeal(2, [$orderInfo['consignee'], $orderInfo['consignee_phone'], $orderInfo['realname'], $orderInfo['id_card_no']], $orderInfo['uid'], '前端用户');
            $orderInfo['consignee']       = isset($encrypt[$orderInfo['consignee']]) ? $encrypt[$orderInfo['consignee']] : '';
            $orderInfo['consignee_phone'] = isset($encrypt[$orderInfo['consignee_phone']]) ? $encrypt[$orderInfo['consignee_phone']] : '';
            $orderInfo['realname']        = isset($encrypt[$orderInfo['realname']]) ? $encrypt[$orderInfo['realname']] : '';
            $orderInfo['id_card_no']      = isset($encrypt[$orderInfo['id_card_no']]) ? $encrypt[$orderInfo['id_card_no']] : '';
            //省市区处理
            $orderInfo['province'] = !empty($orderInfo['province_id']) ? getRegionalInfo($orderInfo['province_id']) : '';
            $orderInfo['city']     = !empty($orderInfo['city_id']) ? getRegionalInfo($orderInfo['city_id']) : '';
            $orderInfo['town']     = !empty($orderInfo['district_id']) ? getRegionalInfo($orderInfo['district_id']) : '';
            //金额计算
            $acturalPaid = $orderInfo['payment_amount'];//实际支付金额(货款+运费+税款-优惠减免金额，与支付保持一致（精确到分 ）)
            //默认投保
            $insuredFee                  = 1;
            $ifInsure                    = 1;
            $tpl                         = '0000017';//第三方物流商编码 顺丰编码：0000017；京配：1000053568
            $orderInfo['payment_amount'] = $orderInfo['payment_amount'] - 1;
            $comprehensive_tax           = round($orderInfo['payment_amount'] * 0.1518, 5);//税费
            $unpaidPrice                 = $orderInfo['payment_amount'] - $comprehensive_tax;//实际售价
            //支付方式: 0支付宝APP 1支付宝H5 2支付宝PC 3微信APP 4微信小程序 5微信H5 6抖音支付宝 7微信JSAPI(公众号支付) 8抖音微信 201兔头 202礼品卡
            if (in_array($orderInfo['payment_subject'], [3, 4])) {
                if (in_array($orderInfo['payment_method'], [0, 1, 2, 6])) {
                    $payCopNo    = '31222699S7';
                    $payPcomName = '支付宝(中国)网络技术有限公司';
                } else {
                    $payCopNo    = '4403169D3W';
                    $payPcomName = '财付通支付科技有限公司';
                }
            } else {
                $payCopNo    = '312228036U';
                $payPcomName = '银联商务股份有限公司';
            }
            //推送报文组装
            $pushData = array(
                'orderId'        => $orderInfo['main_order_no'],//订单号
                'orderDate'      => date('Y-m-d H:i:s', $orderInfo['created_time']),//订单生成时间
                'warehouseId'    => 'WMS_360_04',//仓库ID(卓志提供)
                'tpl'            => $tpl,//第三方物流商编码 顺丰编码：0000017；京配：1000053568
                'orderType'      => 2,//是否自运营订单1：非自运营；2：自运营
                'customsType'    => 1,//海关类型1：总署版；2：2.0版
                'electricCode'   => '1100001146',//电商企业编码(确认推单信息后，卓志提供)
                'cbepcomCode'    => '1100004450',//电商平台编码(确认推单信息后，卓志提供)
                'freightFcy'     => 0,//运费
                'freightFcode'   => 'CNY',//运费币制(进口默认为"CNY")
                'ifInsure'       => $ifInsure,//是否投保(0-否 1-是)
                'insuredFee'     => $insuredFee,//保费
                'taxFcy'         => $comprehensive_tax,//税费
                'buyerName'      => $orderInfo['realname'],//订购人姓名
                'buyerIdType'    => 1,//订购人证件类型(默认：1身份证)
                'buyerIdNumber'  => strtoupper($orderInfo['id_card_no']),//订购人证件号码
                'buyerTelephone' => $orderInfo['consignee_phone'],//订购人电话
                'buyerRegNo'     => $orderInfo['uid'],//订购人注册号
                'orderStatus'    => 'S',//订单状态(默认S)
                'busiMode'       => 10,//进口模式10（即BBC业务）,20（即BC业务），11（即跨境自提），12（即岛内免税）
                'customsCode'    => '5165',//申报关区  埔开发区：5208南沙旅检：5165 连保税港：0910洋浦港区：6408；沈阳综合保税区海关：0808
                'ciqbCode'       => '0',//申报国检
                'shippernCode'   => '156',//发货人所在国
                'taxFcode'       => 'CNY',//税费币种(默认为"CNY")
                'otherPayment'   => 0.00000,//抵付金额(5位小数)
                'payNo'          => $orderInfo['tradeno'],//支付流水号
                'payPcomName'    => $payPcomName,//支付企业名称
                'payCopNo'       => $payCopNo,//支付企业备案代码
                'agentCode'      => $payCopNo,//报关企业代码
                'acturalPaid'    => $acturalPaid,//实际支付金额(货款+运费+税款-优惠减免金额，与支付保持一致（精确到分 ）)
                'fromEplat'      => 0,//来源第e仓 默认值：0 0：否 1：是
                'vmiFlag'        => 0,//是否VMI模式 默认值：0 0：否 1：是
                'ownerFlag'      => 0,//是否一单多业主 默认值：0 0：否 1：是
                'recipient'      => array(
                    'name'        => $orderInfo['consignee'],//收货人姓名
                    'receiveType' => 1,//证件类型
                    'receiveNo'   => strtoupper($orderInfo['id_card_no']),//收件人证件号
                    'mobilePhone' => $orderInfo['consignee_phone'],//手机号码
                    'country'     => '中国',//国家
                    'province'    => $orderInfo['province'],//省份
                    'city'        => $orderInfo['city'],//城市
                    'district'    => $orderInfo['town'],//区/县
                    'address'     => $orderInfo['province'] . '&nbsp;' . $orderInfo['city'] . '&nbsp;' . $orderInfo['town'] . $orderInfo['address'],//地址
                    'postCode'    => ''
                ),//收货信息
                'goodList'       => array(
                    array(
                        'source'    => null,
                        'gnum'      => 1,//序号
                        'goodId'    => $orderInfo['goods_itemno'],//商品货号
                        'barCode'   => $orderInfo['goods_barcode'],//条形码
                        'amount'    => $orderInfo['order_qty'] * $orderInfo['nums'],//数量（套餐份数*套餐内产品数量）
                        'price'     => round($unpaidPrice, 5),//售价（实际售价）
                        'goodPrice' => $unpaidPrice,//商品售价（展示售价）
                        'copGName'  => $orderInfo['goods_item_name'],//商品名称
                        'decTotal'  => $orderInfo['payment_amount'],//商品总价
                        'gQty2'     => $orderInfo['qty2'] * $orderInfo['order_qty'] * $orderInfo['nums'],//商品第二数量
                        'shopid'    => '1',//店铺编码
                    )
                )//货品信息
            );
            $result   = httpPostString(env('ORDERS.nan_sha_url') . '/big/receiveOrder', json_encode($pushData, JSON_UNESCAPED_UNICODE));
            //$result   = httpPostString('http://106.75.165.2/ocp/rest/big/receiveOrder', json_encode($pushData, JSON_UNESCAPED_UNICODE));
            if (empty($result)) $this->throwError('推送南沙仓失败');
            if ($result['status'] != 1) $this->throwError('推送南沙仓失败:' . $result['notes']);
            //推送代发仓结果处理
            $resultData = array(
                'order_info'         => $orderInfo,
                'store_type'         => 2,
                'push_result_status' => 1,
                'result_msg'         => '推送南沙仓成功',
                'push_data'          => json_encode($pushData, JSON_UNESCAPED_UNICODE),
                'push_back_data'     => isset($result) ? json_encode($result, JSON_UNESCAPED_UNICODE) : ''
            );
            $this->pushResultDeal($resultData);
            $excess_id = $params['excess_id'] ?? 0;
            if ($excess_id) {
                Db::name('cross_excess')->where('id', $excess_id)->update([
                    'status'      => 2,
                    'update_time' => time(),
                    'remark'      => $orderInfo['main_order_no'] ?? '',
                ]);
            }
        } catch (\Exception $e) {
            //推送代发仓结果处理
            $resultData = array(
                'order_info'         => $orderInfo,
                'store_type'         => 2,
                'push_result_status' => 0,
                'result_msg'         => $e->getMessage(),
                'push_data'          => json_encode($pushData, JSON_UNESCAPED_UNICODE),
                'push_back_data'     => isset($result) ? json_encode($result, JSON_UNESCAPED_UNICODE) : ''
            );
            $this->pushResultDeal($resultData);
            $this->throwError($params['main_order_no'] . $e->getMessage());
        }
        return true;
    }

    /**
     * Description:跨境代发仓推送结果处理
     * Author: zrc
     * Date: 2022/5/5
     * Time: 14:21
     * @param $params
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function pushResultDeal($params)
    {
        $orderInfo          = $params['order_info'];
        $store_type         = $params['store_type'];
        $push_result_status = $params['push_result_status'];
        $result_msg         = $params['result_msg'];
        $push_data          = $params['push_data'];
        $push_back_data     = $params['push_back_data'];
        //推送日志记录
        $log = array(
            'main_order_no'  => $orderInfo['main_order_no'],
            'store_type'     => $store_type,
            'push_result'    => $push_result_status,
            'result_msg'     => $result_msg,
            'push_data'      => $push_data,
            'push_back_data' => $push_back_data,
            'created_time'   => time()
        );
        Db::name('cross_push_warehouse_log')->insert($log);
        //查询跨境海关申报记录
        $declare_record = Db::name('cross_customs_declare_record')->where(['main_order_no' => $orderInfo['main_order_no']])->find();
        if (isset($declare_record) && $declare_record['customs_status'] < 2 && $declare_record['abnormal_node'] < 3) {
            $data = array(
                'store_type'          => $params['store_type'],
                'abnormal_node'       => $params['push_result_status'] == 1 ? 0 : 2,
                'error_des'           => $params['result_msg'],
                'customs_status'      => $params['push_result_status'] == 1 ? 0 : 1,
                'update_time'         => time(),
                'push_warehouse_time' => time(),
            );
            Db::name('cross_customs_declare_record')->where(['main_order_no' => $orderInfo['main_order_no']])->update($data);
        }
        $push_store_status = 2;//默认推送失败
        if ($push_result_status == 1) $push_store_status = 1;//推送成功
        //推送状态修改
        Db::name('cross_order')->where(['main_order_id' => $orderInfo['id']])->update(['push_store_status' => $push_store_status, 'update_time' => time()]);
        return true;
    }

    /**
     * Description:跨境订单推送前验证
     * Author: zrc
     * Date: 2022/4/14
     * Time: 14:27
     * @param $orderInfo
     * @return bool
     */
    public function checkCrossOrder($orderInfo)
    {
        if ($orderInfo['is_ts'] == 1) $this->throwError('暂存订单推送失败');
        if ($orderInfo['freeze_status'] == 1) $this->throwError('订单已冻结推送失败');
        if ($orderInfo['sub_order_status'] != 1 || in_array($orderInfo['refund_status'], [1, 2])) $this->throwError('订单状态异常推送失败');
        return true;
    }

    /**
     * Description:跨境商品获取订单、商品备案信息
     * Author: zrc
     * Date: 2022/4/14
     * Time: 14:02
     * @param $main_order_no
     * @return array|Db|\think\Model|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function crossGetOrderInfo($main_order_no)
    {
        $field     = 'om.*,co.realname,co.id_card_no,co.period,co.package_id,co.order_qty,co.express_type,co.is_ts,co.payment_doc,co.push_store_status,co.store_type,co.sub_order_status,co.express_fee,co.refund_status,co.freeze_status';
        $orderData = Db::name('order_main')->alias('om')
            ->field($field)
            ->join('cross_order co', 'co.main_order_id = om.id')
            ->where(['om.main_order_no' => $main_order_no])
            ->find();
        if (empty($orderData)) $this->throwError('未获取到订单信息');
        $receiveInfo = Db::name('sub_order_receive_information')->where(['main_order_no' => $orderData['main_order_no'], 'uid' => $orderData['uid']])->find();
        if (!empty($receiveInfo)) {
            $orderData['province_id']     = $receiveInfo['province_id'];
            $orderData['city_id']         = $receiveInfo['city_id'];
            $orderData['district_id']     = $receiveInfo['district_id'];
            $orderData['address']         = $receiveInfo['address'];
            $orderData['consignee']       = $receiveInfo['consignee'];
            $orderData['consignee_phone'] = $receiveInfo['consignee_phone'];
        }
        //套餐ID获取商品套餐信息
        $packageInfo = esGetOne($orderData['package_id'], 'vinehoo.periods_set');
        if (empty($packageInfo)) $this->throwError('未获取到产品套餐信息');
        $associated_products = json_decode($packageInfo['associated_products'], true);
        if (empty($associated_products)) $this->throwError('未获取到产品json');
        $orderData['nums'] = $associated_products[0]['nums'];//套餐内产品数量
        //获取商品条码
        $wikiData    = array(
            'id'     => $associated_products[0]['product_id'],
            'fields' => 'bar_code',
        );
        $productInfo = $this->httpGet(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/info', $wikiData);
        if ($productInfo['error_code'] != 0 || count($productInfo['data']) == 0) $this->throwError('未获取到商品条码');
        $bar_code = $productInfo['data']['bar_code'];//条码
        //获取商品备案信息
        $record_information = Db::name('cross_record_information')->field('goods_item_name,goods_itemno,goods_barcode,qty2,hs_code')->where(['goods_itemno' => $bar_code])->find();
        if (empty($record_information)) $this->throwError('未获取到商品备案信息');
        $orderData['goods_item_name'] = $record_information['goods_item_name'];//企业商品中文名
        $orderData['goods_itemno']    = $record_information['goods_itemno'];//商品货号(SKU)
        $orderData['goods_barcode']   = $record_information['goods_barcode'];//商品国际条码
        $orderData['qty2']            = $record_information['qty2'];//第二数量
        $orderData['hs_code']         = $record_information['hs_code'];//hs编码
        return $orderData;
    }

    /**
     * Description:跨境南沙仓清关状态回传
     * Author: zrc
     * Date: 2022/4/24
     * Time: 11:26
     * @param $requestparams
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function borderStatusPushBack($requestparams)
    {
        $params        = $requestparams;
        $main_order_no = $params['orderNo'];
        $jsonData      = json_encode($params, JSON_UNESCAPED_UNICODE);
        //日志记录
        $log = array(
            'main_order_no'  => $main_order_no,
            'store_type'     => 2,
            'call_back_data' => $jsonData,
            'created_time'   => time(),
        );
        Db::name('cross_callback_log')->insert($log);
        //报关回执处理
        $declareRecord = Db::name('cross_customs_declare_record')->where(['main_order_no' => $params['orderNo']])->find();
        if (empty($declareRecord)) {
            return json_encode(['status' => 2, 'notes' => '未获取到跨境海关申报记录'], JSON_UNESCAPED_UNICODE);
        }
        $updateData['update_time'] = time();
        //报关异常处理
        if (in_array($params['status'], [23, 25, 41]) && $declareRecord['customs_status'] != 2) {
            $updateData['abnormal_node']  = 3;
            $updateData['error_des']      = $params['notes'];
            $updateData['customs_status'] = 1;
            //添加订单备注
            $main_order_id = Db::name('order_main')->where(['main_order_no' => $params['orderNo']])->value('id');
            $subOrderInfo  = Db::name('cross_order')->field('uid,sub_order_no,id_card_no')->where(['main_order_id' => $main_order_id])->find();
            if ($main_order_id && $subOrderInfo) {
                $orderService = new OrderService();
                $remark       = array(
                    'sub_order_no' => $subOrderInfo['sub_order_no'],
                    'order_type'   => 2,
                    'content'      => $params['notes'],
                    'admin_id'     => 0
                );
                $orderService->createRemarks($remark);
            }
            //黑名单处理
            if (strpos($params['notes'], '超过年度限额') !== false) {
                // 超额;
                $pushData   = array(
                    'namespace' => 'excess_auto_refund',
                    'key'       => $subOrderInfo['sub_order_no'],
                    'data'      => base64_encode(json_encode(['sub_order_no' => $subOrderInfo['sub_order_no']])),
                    'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/cross/excessAutoRefund',
                    'timeout'   => '5s',
                );
                $timing_res = httpPostString(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', json_encode($pushData));
                Log::write("添加海关超额自动退款任务: " . json_encode($pushData) . '   ' . json_encode($timing_res));
                $encrypt                    = cryptionDeal(2, [$subOrderInfo['id_card_no']], $subOrderInfo['uid'], '前端用户');
                $subOrderInfo['id_card_no'] = isset($encrypt[$subOrderInfo['id_card_no']]) ? $encrypt[$subOrderInfo['id_card_no']] : '';
                $crossService               = new CrossService();
                $crossService->inputBlackList(['id_card_no' => $subOrderInfo['id_card_no'], 'type' => 1, 'note' => $params['notes']]);
            }
            //自动推单统计处理
            $autoPushLog = Db::name('cross_auto_push_log')->where(['main_order_no' => $params['orderNo']])->order('id desc')->find();
            if ($autoPushLog) {
                $updateLogData = array(
                    'status'      => 2,
                    'error_msg'   => $params['notes'],
                    'update_time' => time(),
                );
                Db::name('cross_auto_push_log')->where(['main_order_no' => $params['orderNo']])->update($updateLogData);
            }
            //异常提示
            $store      = '南沙';
            $short_code = isset($autoPushLog['short_code']) ? $autoPushLog['short_code'] : '';
            $content    = "# 跨境自动推单异常提示\n";
            $content    .= "-主订单号：" . $params['orderNo'] . "\n";
            $content    .= "-订单仓库：" . $store . "\n";
            $content    .= "-商品条码：" . $short_code . "\n";
            $content    .= "-异常信息：" . $params['notes'] . "\n";
            $queueData  = array(
                'access_token' => env('ORDERS.cross_token'),
                'type'         => 'text',
                'at'           => '***********,***********,***********',
                'content'      => base64_encode($content),
            );
            $data       = base64_encode(json_encode($queueData));
            $pushData   = array(
                'exchange_name' => 'dingtalk',
                'routing_key'   => 'dingtalk_sender',
                'data'          => $data,
            );
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        }
        //海关放行处理
        if (in_array($params['status'], [22, 42])) {
            $updateData['abnormal_node']  = 0;
            $updateData['customs_status'] = 2;
            //添加订单备注
            $main_order_id = Db::name('order_main')->where(['main_order_no' => $params['orderNo']])->value('id');
            $subOrderInfo  = Db::name('cross_order')->field('uid,sub_order_no,id_card_no')->where(['main_order_id' => $main_order_id])->find();
            if ($main_order_id && $subOrderInfo) {
                $orderService = new OrderService();
                $remark       = array(
                    'sub_order_no' => $subOrderInfo['sub_order_no'],
                    'order_type'   => 2,
                    'content'      => $params['notes'],
                    'admin_id'     => 0
                );
                $orderService->createRemarks($remark);
            }
        }
        Db::name('cross_customs_declare_record')->where(['main_order_no' => $main_order_no])->update($updateData);
        return json_encode(['orderId' => $main_order_no, 'status' => 1], JSON_UNESCAPED_UNICODE);
    }

    /**
     * Description:跨境南沙仓物流信息回推接口
     * Author: zrc
     * Date: 2022/4/24
     * Time: 11:40
     * @param $requestparams
     * @return int[]
     * @throws \think\db\exception\DbException
     */
    public function logisticsBack($requestparams)
    {
        $params        = $requestparams;
        $main_order_no = $params['orderId'];
        $jsonData      = json_encode($params, JSON_UNESCAPED_UNICODE);
        //日志记录
        $log = array(
            'main_order_no'  => $main_order_no,
            'store_type'     => 2,
            'call_back_data' => $jsonData,
            'created_time'   => time(),
        );
        Db::name('cross_callback_log')->insert($log);
        $es            = new ElasticSearchService();
        $arr           = array(
            'index' => ['orders'],
            'match' => [['main_order_no' => $main_order_no]],
            'range' => [['created_time' => ['gte' => date('Y-m-d H:i:s', 1602777600)]]],
            'limit' => 1,
        );
        $data          = $es->getDocumentList($arr);
        $orderInfo     = $data['data'][0];
        $main_order_id = Db::name('order_main')->where(['main_order_no' => $main_order_no])->value('id');
        if (empty($main_order_id)) {
            return json_encode(['status' => 2, 'notes' => '未获取到订单信息'], JSON_UNESCAPED_UNICODE);
        }
        //快递方式处理
        $express_type = 2;
        if (strpos($params['wayBillNo'], 'JD') !== false) $express_type = 5;
        //物流信息处理
        $updateData = array(
            'sub_order_status' => 2,
            'express_type'     => $express_type,
            'express_number'   => $params['wayBillNo'],
            'is_ts'            => 0,
            'delivery_time'    => time(),
            'update_time'      => time(),
        );
        $result     = Db::name('cross_order')->where(['main_order_id' => $main_order_id])->update($updateData);
        if (empty($result)) {
            return json_encode(['status' => 2, 'notes' => '修改子订单信息失败'], JSON_UNESCAPED_UNICODE);
        }
        //订单推送T+
        $sub_order_no = Db::name('cross_order')->where(['main_order_id' => $main_order_id])->value('sub_order_no');
        if ($sub_order_no) {
            $pushService = new PushService();
            $pushService->pushTplus(['sub_order_no' => $sub_order_no, 'order_type' => 2]);
        }
        //小程序订阅消息推送+app消息推送
        if (isset($orderInfo['uid']) && !empty($orderInfo['uid']) && empty($orderInfo['express_number'])) {
            $userInfo = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $orderInfo['uid'], 'field' => 'nickname,applet_openid']);
            if ($userInfo['error_code'] == 0 && isset($userInfo['data']['list'][0])) {
                //access_token获取
                $getToken = $this->httpGet(env('ITEM.WECHART_URL') . '/wechat/v3/minapp/accesstoken');
                if ($getToken['error_code'] == 0 && isset($userInfo['data']['list'][0]['applet_openid'])) {
                    $pushData = array(
                        'touser'            => $userInfo['data']['list'][0]['applet_openid'],
                        'template_id'       => '0tiG9uQEuyT4OqvLl5Z6_tJ2P58dyxin_zyOzQBLhuY',
                        'page'              => 'packageB/pages/order-detail/order-detail?orderNo=' . $orderInfo['sub_order_no'],
                        'miniprogram_state' => env('ORDERS.MINIPROGRAM_STATE'),
                        "topcolor"          => "#FF0000",
                        'data'              => [
                            'amount3'           => [
                                'value' => $orderInfo['payment_amount'] . '元',
                                'color' => '#173177'
                            ],
                            'thing2'            => [
                                'value' => truncate_utf8_string($orderInfo['title'], 15),
                                'color' => '#173177'
                            ],
                            'character_string1' => [
                                'value' => $orderInfo['sub_order_no'],
                                'color' => '#173177'
                            ]
                        ]
                    );
                    curlRequest('https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=' . $getToken['access_token'], json_encode($pushData));
                }
                //app推送消息
                $single = array(
                    'is_push'      => 1,
                    'uid'          => $orderInfo['uid'],
                    'title'        => "物流通知",
                    'content'      => "物流通知 您的" . isset($orderInfo['title']) ? $orderInfo['title'] : '商品' . "已经发货，物流单号是：" . $params['wayBillNo'] . "，点击查看物流信息",
                    'data_type'    => 20,
                    'data'         => [
                        'title'        => "物流通知",
                        'content'      => "物流通知 您的" . isset($orderInfo['title']) ? $orderInfo['title'] : '商品' . "已经发货，物流单号是：" . $params['wayBillNo'] . "，点击查看物流信息",
                        'cover'        => isset($orderInfo['banner_img']) ? $orderInfo['banner_img'] : '',
                        'logisticCode' => $params['wayBillNo'],
                        'expressType'  => $express_type
                    ],
                    'label'        => "LogisticsDetails",
                    'custom_param' => ['cover' => isset($orderInfo['banner_img']) ? $orderInfo['banner_img'] : '', 'logisticCode' => $params['wayBillNo'], 'expressType' => $express_type]
                );
                httpPostString(env('ITEM.APPPUSH_URL') . '/apppush/v3/push/single', json_encode($single, JSON_UNESCAPED_UNICODE));
            }
        }
        return json_encode(['status' => 1, 'notes' => '成功'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * Description:跨境推送古斯缇代发仓
     * Author: zrc
     * Date: 2022/5/5
     * Time: 14:27
     * @param $requestparams
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DbException
     */
    public function crossPushGuSiTi($requestparams)
    {
        $params    = $requestparams;
        $orderInfo = $params['order_info'];
        try {
            //用户信息解密处理
            $encrypt                      = cryptionDeal(2, [$orderInfo['consignee'], $orderInfo['consignee_phone'], $orderInfo['realname'], $orderInfo['id_card_no']], $orderInfo['uid'], '前端用户');
            $orderInfo['consignee']       = isset($encrypt[$orderInfo['consignee']]) ? $encrypt[$orderInfo['consignee']] : '';
            $orderInfo['consignee_phone'] = isset($encrypt[$orderInfo['consignee_phone']]) ? $encrypt[$orderInfo['consignee_phone']] : '';
            $orderInfo['realname']        = isset($encrypt[$orderInfo['realname']]) ? $encrypt[$orderInfo['realname']] : '';
            $orderInfo['id_card_no']      = isset($encrypt[$orderInfo['id_card_no']]) ? $encrypt[$orderInfo['id_card_no']] : '';
            //省市区处理
            $orderInfo['province'] = !empty($orderInfo['province_id']) ? getRegionalInfo($orderInfo['province_id']) : '';
            $orderInfo['city']     = !empty($orderInfo['city_id']) ? getRegionalInfo($orderInfo['city_id']) : '';
            $orderInfo['town']     = !empty($orderInfo['district_id']) ? getRegionalInfo($orderInfo['district_id']) : '';
            //完税价格、税金、单价、总价计算
            $taxRate = Db::name('cross_gst_tax_rate')->where(['hs_code' => $orderInfo['hs_code']])->find();
            //快递方式为顺丰冷链，单独给标识给古斯缇
            if (in_array($orderInfo['express_type'], [3, 31])) {
                $logisticsCode = 'sfll';
            } else {
                $logisticsCode = '';
            }
            $goodsValue  = round($orderInfo['payment_amount'] / (1 + $taxRate['tax_rate']), 2);//完税价格
            $taxTotal    = round($orderInfo['payment_amount'] / (1 + $taxRate['tax_rate']) * $taxRate['tax_rate'], 2);//税金
            $orderAmount = $goodsValue + $taxTotal;//商品价格+运杂费+代扣税款-非现金抵扣金额，与支付凭证的支付金额一致。
            $qty         = intval($orderInfo['nums'] * $orderInfo['order_qty']);//数量
            $price       = round($goodsValue / $qty, 2);//单价
            //支付方式: 0支付宝APP 1支付宝H5 2支付宝PC 3微信APP 4微信小程序 5微信H5 6抖音支付宝 7微信JSAPI(公众号支付) 8抖音微信 201兔头 202礼品卡
            if (in_array($orderInfo['payment_subject'], [3, 4])) {
                if (in_array($orderInfo['payment_method'], [0, 1, 2, 6])) {
                    $pay_code = '31222699S7';
                    $pay_name = '支付宝(中国)网络技术有限公司';
                } else if (in_array($orderInfo['payment_method'], [3, 4, 5, 7, 8, 9])) {
                    $pay_code = '4403169D3W';
                    $pay_name = '财付通支付科技有限公司';
                }
            } else {
                $pay_code = '312228036U';
                $pay_name = '银联商务股份有限公司';
            }
            $public = array(
                'appid'      => $this->appId,
                'method'     => $this->method,
                'format'     => $this->format,
                'charset'    => $this->charset,
                'signtype'   => $this->signType,
                'timestamp'  => $this->timestamp ? $this->timestamp : date('Y-m-d H:i:s'),
                'version'    => $this->version,
                'notifyurl'  => $this->notifyUrl,
                'bizcontent' => $this->bizcontent,
            );
            $data   = array(
                'sellerOrderNo'      => $params['main_order_no'],//主订单号
                'orderGoodsType'     => 'I20',//订单类型
                'ebcCode'            => $this->ebcCode,//电商企业海关备案十位编码
                'ebcName'            => $this->ebcName,//电商企业海关备案名称
                'payTransactionId'   => $orderInfo['tradeno'],//支付的唯一流水号。注：此字段不为空时，定为我司不推送支付单，由商家自行推送支付单
                'logisticsCode'      => $logisticsCode,//嘉创物流插件自定义编码
                'goodsValue'         => $goodsValue,//商品总价格
                'freight'            => '0',//运杂费
                'discount'           => '0',//非现金抵扣金额
                'taxTotal'           => $taxTotal,//代扣税款
                'acturalPaid'        => $orderAmount,//实际支付金额
                'buyerRegNo'         => $orderInfo['consignee'],//订购人注册号
                'buyerName'          => str_replace(array("\r\n", "\r", "\n"), "", $orderInfo['realname']),//订购人姓名
                'buyerIdNumber'      => str_replace(array("\r\n", "\r", "\n"), "", strtoupper($orderInfo['id_card_no'])),//订购人证件号码
                'consignee'          => $orderInfo['consignee'],//收货人姓名
                'consigneeTelephone' => $orderInfo['consignee_phone'],//收货人电话
                'consigneeAddress'   => str_replace(array("\r\n", "\r", "\n"), "", $orderInfo['address']),//收货人地址
                'areaName'           => "{$orderInfo['province']} {$orderInfo['city']} {$orderInfo['town']}",//省市区全名，中间以空格分割开
                'orderList'          => array(
                    array(
                        'itemNum'    => 1,//序号，如：1,2,3…
                        'itemNo'     => $orderInfo['goods_itemno'],//商品货号
                        'itemName'   => $orderInfo['goods_item_name'],//商品名称
                        'qty'        => $qty,//数量（整数）
                        'price'      => $price,//单价，单位为元，精确到小数
                        'totalPrice' => $goodsValue,//总价，单位为元，精确到小数
                    )
                ),//订单商品明细
                'isWaybill'          => 0,//是否只申报运单(0:为否,1:为是)
                'ebpCode'            => $this->ebcCode,//电商平台海关备案十位代码
                'ebpName'            => $this->ebcName,//电商平台海关备案名称
                'customsOrderSn'     => $params['main_order_no'],//申报订单号
                'payCustomsCode'     => $pay_code,//支付企业备案代码
                'payCustomsName'     => $pay_name,//支付企业备案名称
            );
            if ($orderInfo['payment_subject'] == 4) {
                $data['ebcCode'] = $this->technologyEbcCode;
                $data['ebcName'] = $this->technologyEbcName;
                $data['ebpCode'] = $this->technologyEbcCode;
                $data['ebpName'] = $this->technologyEbcName;
            }
            $aes                  = new Aes();
            $public['bizcontent'] = $aes->encrypt($this->jsonEncode($data), $this->secret, $this->iv);
            $result               = json_decode($this->curlPost($this->apiUrl, $public), true);
            if (empty($result)) $this->throwError('推送古斯缇失败');
            if ($result['type'] != 'success') {
                if (strpos($result['content'], '订单已存在') == false) {
                    $this->throwError('推送古斯缇失败:' . $result['content']);
                }
            }
            //推送代发仓结果处理
            $resultData = array(
                'order_info'         => $orderInfo,
                'store_type'         => 1,
                'push_result_status' => 1,
                'result_msg'         => '推送古斯缇成功',
                'push_data'          => json_encode($data, JSON_UNESCAPED_UNICODE),
                'push_back_data'     => isset($result) ? json_encode($result, JSON_UNESCAPED_UNICODE) : ''
            );
            $this->pushResultDeal($resultData);
            $excess_id = $params['excess_id'] ?? 0;
            if ($excess_id) {
                Db::name('cross_excess')->where('id', $excess_id)->update([
                    'status'      => 2,
                    'update_time' => time(),
                    'remark'      => $orderInfo['main_order_no'] ?? '',
                ]);
            }
        } catch (\Exception $e) {
            //推送代发仓结果处理
            $resultData = array(
                'order_info'         => $orderInfo,
                'store_type'         => 1,
                'push_result_status' => 0,
                'result_msg'         => $e->getMessage(),
                'push_data'          => isset($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : '',
                'push_back_data'     => isset($result) ? json_encode($result, JSON_UNESCAPED_UNICODE) : ''
            );
            $this->pushResultDeal($resultData);
            $this->throwError($params['main_order_no'] . $e->getMessage());
        }
    }

    public function jsonEncode($data)
    {
        return urldecode(json_encode($this->urlEncode($data)));
    }

    public function urlEncode($data)
    {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[urlencode($key)] = $this->urlEncode($value);
            }
        } else {
            $data = urlencode($data);
        }
        return $data;
    }

    public function curlPost($url, $data)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        //设置header
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        $content = curl_exec($curl);
        if ($content) {
            curl_close($curl);
            return $content;
        } else {
            $errorCode    = curl_errno($curl);
            $errorMessage = curl_error($curl);
            curl_close($curl);
            throw new Exception("请求出错，错误码：{$errorCode}，错误内容：{$errorMessage}");
        }
    }

    /**
     * Description:v2微信/支付宝推送支付单
     * Author: zrc
     * Date: 2022/5/7
     * Time: 16:50
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function customsDeclare($requestparams)
    {
        $params      = $requestparams;
        $mainOrderNo = $params['main_order_no'];
        //查询报关记录
        $declareLog = Db::name('cross_declare_log')->where(['main_order_no' => $params['main_order_no']])->order('id desc')->find();
        //查询订单信息
        $orderInfo = Db::name('order_main')
            ->alias('om')
            ->field('om.payment_subject,om.payment_method,om.payment_amount,om.main_order_status,om.tradeno,co.sub_order_no,co.payment_request_raw_data,co.payment_response_raw_data,co.store_type')
            ->leftJoin('cross_order co', 'co.main_order_id=om.id')
            ->where(['om.main_order_no' => $params['main_order_no']])
            ->find();
        if (empty($orderInfo)) $this->throwError($params['main_order_no'] . '未获取到订单信息');
        try {
            if (empty($declareLog)) {//首推
                if ($orderInfo['main_order_status'] != 1) $this->throwError('当前订单状态不允许推送');
                //if (empty($orderInfo['tradeno']) || empty($orderInfo['payment_request_raw_data']) || empty($orderInfo['payment_request_raw_data'])) $this->throwError('缺少海关需要的支付信息');
                if (in_array($orderInfo['payment_method'], [0, 1, 2, 6])) {//支付宝
                    if ($orderInfo['payment_subject'] == 4) {
                        require_once(root_path() . "lib/alipay.acquire.customs/function.php");
                        $outRequestNo     = md5(date('YmdHis') . uniqid(rand(), true));
                        $declareResultXml = custom_declare($outRequestNo, $orderInfo['tradeno'], $orderInfo['payment_amount']);
                    } else {
                        require_once(root_path() . "lib/alipay_customs/function.php");
                        $outRequestNo     = md5(date('YmdHis') . uniqid(rand(), true));
                        $declareResultXml = custom_declare($outRequestNo, $orderInfo['tradeno'], $orderInfo['payment_amount']);
                    }
                    $declareResult = json_decode(json_encode(simplexml_load_string($declareResultXml)), true);
                    //支付单推送记录添加
                    $addLog = Db::name('cross_declare_log')
                        ->insert(array(
                            'platform'                   => 1,
                            'main_order_no'              => $params['main_order_no'],
                            'trade_no'                   => $orderInfo['tradeno'],
                            'submit_data'                => $declareResultXml,
                            'request_status'             => $declareResult['is_success'],
                            'request_msg'                => isset($declareResult['response']['alipay']['detail_error_des']) ? $declareResult['response']['alipay']['detail_error_des'] : '',
                            'response_data'              => json_encode($declareResult, JSON_UNESCAPED_UNICODE),
                            'result_code'                => $declareResult['response']['alipay']['result_code'],
                            'ver_dept'                   => isset($declareResult['response']['alipay']['ver_dept']) ? $declareResult['response']['alipay']['ver_dept'] : 3,
                            'verify_department_trade_id' => isset($declareResult['response']['alipay']['pay_transaction_id']) ? $declareResult['response']['alipay']['pay_transaction_id'] : '',
                            'out_request_no'             => $outRequestNo,
                            'declare_no'                 => isset($declareResult['response']['alipay']['alipay_declare_no']) ? $declareResult['response']['alipay']['alipay_declare_no'] : '',
                            'created_time'               => time()
                        ));
                    if (empty($addLog)) $this->throwError('保存支付单推送记录失败');
                    $result = array(
                        'push_status' => 1,
                        'msg'         => '推送成功',
                    );
                    if ($declareResult['response']['alipay']['result_code'] == 'FAIL') {
                        $result = array(
                            'push_status' => 0,
                            'msg'         => isset($declareResult['response']['alipay']['detail_error_des']) ? $declareResult['response']['alipay']['detail_error_des'] : '推送失败',
                        );
                    }
                } else {//微信
                    if ($orderInfo['payment_subject'] == 4) {
                        require_once(root_path() . "lib/wechat.acquire.customs/function.php");
                        $declareResultXml = custom_declare($params['main_order_no'], $orderInfo['tradeno']);
                    } else {
                        require_once(root_path() . "lib/wechat_customs/function.php");
                        $declareResultXml = custom_declare($params['main_order_no'], $orderInfo['tradeno']);
                    }
                    $declareResult = json_decode(json_encode(simplexml_load_string($declareResultXml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
                    if (!isset($declareResult['out_trade_no'])) $this->throwError('未获取到微信回执信息，请重推');
                    $verDeptMaps = array(
                        'UNIONPAY'  => 1,
                        'NETSUNION' => 2,
                        'OTHERS'    => 3,
                    );
                    $log         = array(
                        'platform'                   => 2,
                        'main_order_no'              => $declareResult['out_trade_no'],
                        'trade_no'                   => $declareResult['transaction_id'],
                        'submit_data'                => $declareResultXml,
                        'request_status'             => $declareResult['return_code'],
                        'response_data'              => json_encode($declareResult, JSON_UNESCAPED_UNICODE),
                        'result_code'                => $declareResult['result_code'],
                        'ver_dept'                   => isset($declareResult['verify_department']) ? $verDeptMaps[$declareResult['verify_department']] : 3,
                        'verify_department_trade_id' => isset($declareResult['verify_department_trade_id']) ? $declareResult['verify_department_trade_id'] : '',
                        'state'                      => $declareResult['state'],
                        'identity_check'             => $declareResult['cert_check_result'],
                        'created_time'               => time()
                    );
                    if ($declareResult['return_code'] == 'SUCCESS' && $declareResult['result_code'] == 'SUCCESS') {
                        $log['request_msg'] = '成功';
                        $result             = array(
                            'push_status' => 1,
                            'msg'         => '推送成功',
                        );
                    } else {
                        $log['request_msg'] = $declareResult['err_code_des'];
                        $result             = array(
                            'push_status' => 0,
                            'msg'         => $declareResult['err_code_des'],
                        );
                    }
                    $addLog = Db::name('cross_declare_log')->insert($log);
                    if (empty($addLog)) $this->throwError('保存支付单推送记录失败');
                }
            } else {//重推
                if ($declareLog['platform'] == 1) {//支付宝
                    if ($orderInfo['payment_subject'] == 4) {
                        require_once(root_path() . "lib/alipay.acquire.customs/function.php");
                        $declareResultXml = custom_declare($declareLog['out_request_no'], $declareLog['trade_no'], $orderInfo['payment_amount']);
                    } else {
                        require_once(root_path() . "lib/alipay_customs/function.php");
                        $declareResultXml = custom_declare($declareLog['out_request_no'], $declareLog['trade_no'], $orderInfo['payment_amount']);
                    }
                    $declareResult = json_decode(json_encode(simplexml_load_string($declareResultXml)), true);
                    //支付单推送记录添加
                    $addLog = Db::name('cross_declare_log')
                        ->insert(array(
                            'platform'                   => 1,
                            'main_order_no'              => $params['main_order_no'],
                            'trade_no'                   => $orderInfo['tradeno'],
                            'submit_data'                => $declareResultXml,
                            'request_status'             => $declareResult['is_success'],
                            'request_msg'                => isset($declareResult['response']['alipay']['detail_error_des']) ? $declareResult['response']['alipay']['detail_error_des'] : '',
                            'response_data'              => json_encode($declareResult, JSON_UNESCAPED_UNICODE),
                            'result_code'                => $declareResult['response']['alipay']['result_code'],
                            'ver_dept'                   => isset($declareResult['response']['alipay']['ver_dept']) ? $declareResult['response']['alipay']['ver_dept'] : 3,
                            'verify_department_trade_id' => isset($declareResult['response']['alipay']['pay_transaction_id']) ? $declareResult['response']['alipay']['pay_transaction_id'] : '',
                            'out_request_no'             => $declareLog['out_request_no'],
                            'declare_no'                 => isset($declareResult['response']['alipay']['alipay_declare_no']) ? $declareResult['response']['alipay']['alipay_declare_no'] : '',
                            'created_time'               => time()
                        ));
                    if (empty($addLog)) $this->throwError('保存支付单推送记录失败');
                    $result = array(
                        'push_status' => 1,
                        'msg'         => '推送成功',
                    );
                    if ($declareResult['response']['alipay']['result_code'] == 'FAIL') {
                        $result = array(
                            'push_status' => 0,
                            'msg'         => isset($declareResult['response']['alipay']['detail_error_des']) ? $declareResult['response']['alipay']['detail_error_des'] : '推送失败',
                        );
                    }
                } elseif ($declareLog['platform'] == 2) {//微信
                    if ($orderInfo['payment_subject'] == 4) {
                        require_once(root_path() . "lib/wechat.acquire.customs/function.php");
                        $declareResultXml = custom_redeclare($params['main_order_no'], $orderInfo['tradeno']);
                    } else {
                        require_once(root_path() . "lib/wechat_customs/function.php");
                        $declareResultXml = custom_redeclare($declareLog['main_order_no'], $orderInfo['tradeno']);
                    }
                    $declareResult = json_decode(json_encode(simplexml_load_string($declareResultXml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
                    if (!isset($declareResult['out_trade_no'])) $this->throwError($declareResult['err_code_des']);
                    $log = array(
                        'platform'                   => 2,
                        'main_order_no'              => $declareResult['out_trade_no'],
                        'trade_no'                   => $declareResult['transaction_id'],
                        'submit_data'                => $declareResultXml,
                        'request_status'             => $declareResult['return_code'],
                        'response_data'              => json_encode($declareResult, JSON_UNESCAPED_UNICODE),
                        'result_code'                => $declareResult['result_code'],
                        'ver_dept'                   => $declareLog['ver_dept'],
                        'verify_department_trade_id' => $declareLog['verify_department_trade_id'],
                        'state'                      => $declareResult['state'],
                        'identity_check'             => $declareLog['identity_check'],
                        'created_time'               => time()
                    );
                    if ($declareResult['return_code'] == 'SUCCESS' && $declareResult['result_code'] == 'SUCCESS') {
                        $log['request_msg'] = '成功';
                        $result             = array(
                            'push_status' => 1,
                            'msg'         => '推送成功',
                        );
                    } else {
                        $log['request_msg'] = $declareResult['err_code_des'];
                        $result             = array(
                            'push_status' => 0,
                            'msg'         => isset($declareResult['response']['alipay']['detail_error_des']) ? $declareResult['response']['alipay']['detail_error_des'] : '推送失败',
                        );
                    }
                    $addLog = Db::name('cross_declare_log')->insert($log);
                    if (empty($addLog)) $this->throwError('保存支付单推送记录失败');
                }
            }
            //推送结果处理
            $result['main_order_no'] = $mainOrderNo;
            $result['sub_order_no']  = $orderInfo['sub_order_no'];
            $result['store_type']    = $orderInfo['store_type'];
            $result['ver_dept']      = 2;
            $this->declarePushResultDeal($result);
        } catch (\Exception $exception) {
            $result = array(
                'push_status'   => 0,
                'msg'           => $exception->getMessage(),
                'main_order_no' => $mainOrderNo,
                'sub_order_no'  => $orderInfo['sub_order_no'],
                'store_type'    => $orderInfo['store_type'],
                'ver_dept'      => 2
            );
            $this->declarePushResultDeal($result);
            $this->throwError($params['main_order_no'] . '支付单推送失败:' . $exception->getMessage());
        }
        return true;
    }

    /**
     * Description:支付单推送结果处理
     * Author: zrc
     * Date: 2022/5/7
     * Time: 16:42
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function declarePushResultDeal($requestparams)
    {
        $params = $requestparams;
        //查询跨境海关申报记录
        $declare_record = Db::name('cross_customs_declare_record')->where(['main_order_no' => $params['main_order_no']])->find();
        if (empty($declare_record)) {
            $data = array(
                'main_order_no'  => $params['main_order_no'],
                'store_type'     => $params['store_type'],
                'abnormal_node'  => $params['push_status'] == 1 ? 0 : 1,
                'error_des'      => $params['msg'],
                'customs_status' => $params['push_status'] == 1 ? 0 : 1,
                'created_time'   => time(),
            );
            Db::name('cross_customs_declare_record')->insert($data);
        } else {
            if ($declare_record['customs_status'] < 2 && $declare_record['abnormal_node'] < 2) {
                $data = array(
                    'store_type'     => $params['store_type'],
                    'abnormal_node'  => $params['push_status'] == 1 ? 0 : 1,
                    'error_des'      => $params['msg'],
                    'customs_status' => $params['push_status'] == 1 ? 0 : 1,
                    'update_time'    => time(),
                );
                Db::name('cross_customs_declare_record')->where(['main_order_no' => $params['main_order_no']])->update($data);
            }
        }
        //变更订单支付单推送状态
        Db::name('cross_order')->where(['sub_order_no' => $params['sub_order_no']])->update(['payment_doc' => $params['push_status'] == 1 ? 1 : 2, 'update_time' => time(), 'ver_dept' => $params['ver_dept'] ?? 3]);
        //添加订单备注
        $remarks      = array(
            'sub_order_no' => $params['sub_order_no'],
            'order_type'   => 2,
            'content'      => '支付单推送：' . $params['msg'],
            'admin_id'     => 0
        );
        $orderService = new OrderService();
        $orderService->createRemarks($remarks);
        return true;
    }

    /**
     * Description:支付信息海关申报(银联-单笔)
     * Author: zrc
     * Date: 2022/5/9
     * Time: 10:09
     * @param $param
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function UnionPayCustomsDeclare($param)
    {
        // 获取订单信息
        $order_info = Db::name('order_main')
            ->alias('om')
            ->field('om.uid,om.payment_amount,om.tradeno,co.sub_order_no,co.store_type,co.realname,co.id_card_no,co.payment_time,co.payment_request_raw_data,co.payment_request_raw_data')
            ->join('cross_order co', 'co.main_order_id=om.id')
            ->where(['om.main_order_no' => $param['main_order_no']])
            ->find();
        if (empty($order_info)) $this->throwError('未获取到订单信息');
        //if (empty($order_info['tradeno']) || empty($order_info['payment_request_raw_data']) || empty($order_info['payment_request_raw_data'])) $this->throwError('缺少海关需要的支付信息');
        //支付人姓名、身份证解密处理
        if (!empty($order_info['realname'])) {
            $realname               = $order_info['realname'];
            $encrypt                = cryptionDeal(2, [$realname], $order_info['uid'], '前端用户');
            $realname               = isset($encrypt[$realname]) ? $encrypt[$realname] : '';
            $order_info['realname'] = $realname;
        }
        if (!empty($order_info['id_card_no'])) {
            $id_card_no               = $order_info['id_card_no'];
            $encrypt                  = cryptionDeal(2, [$id_card_no], $order_info['uid'], '前端用户');
            $id_card_no               = isset($encrypt[$id_card_no]) ? $encrypt[$id_card_no] : '';
            $order_info['id_card_no'] = $id_card_no;
        }
        try {
            // 加载支付单申报class
            require_once(root_path() . "/extend/umsSecss/SecssUtil.php");
            $secssUtil        = new \SecssUtil();
            $securityPropFile = root_path() . "/extend/umsSecss/security.properties";
            $init_status      = $secssUtil->init($securityPropFile); //初始化安全控件
            if (!$init_status) $this->throwError('初始化异常');
            // 海关信息域
            $CustomsReserved = json_encode([
                'CustomsType'    => '001', //业务类型,001-海关进口业务 有疑问
                'CustomsId'      => '502', //支付信息推送的海关编码 广州总署海关编码 有疑问
                'GoodsAmt'       => sprintf("%012d", intval(bcmul($order_info['payment_amount'], 100))), //货款金额 12位分格式
                'CustomsOrderNo' => $param['main_order_no'], //电商平台订单号
                'BillMode'       => env('ORDERS.BillMode'), //1-一般模式 2-保税模式 有疑问
                'InsuredAmt'     => 0, //保费金额 12位分格式
                'TaxAmt'         => 0, //税款金额 12位分格式
                'TransportAmt'   => 0 //运费金额 12位分格式
            ]);
            // 个人信息域
            $secssUtil->encryptData(base64_encode(json_encode([
                'CertType'  => '01', //支付人证件类型 01-身份证 必输
                'CertNo'    => $order_info['id_card_no'], //支付人证件号码 必输
                'PayerName' => $order_info['realname'], //支付人姓名 必输
            ])));
            if ("00" !== $secssUtil->getErrCode()) $this->throwError($secssUtil->getErrMsg());
            $data = [
                'AppType'          => 1, //报送类型
                'BankInstNo'       => env('ORDERS.BankInstNo'), //支付机构号 银联全渠道：*************** 有疑问
                'BusiType'         => env('ORDERS.BusiType'), //业务类型 有疑问
                'CurryNo'          => env('ORDERS.CurryNo'), //交易币种
                'CustomsReserved'  => $CustomsReserved, //海关信息域
                'Version'          => env('ORDERS.Version'), //版本号
                'MerBgUrl'         => env('ORDERS.MerBgUrl'), //商户后台接收推送结果的地址
                'MerId'            => env('ORDERS.MerId'), //商户号
                'MerOrderNo'       => $param['main_order_no'], //商户订单号
                'TranDate'         => date('Ymd', time()), //商户交易日期
                'PayAmt'           => sprintf("%012d", intval(bcmul($order_info['payment_amount'], 100))), // 支付金额 12位 分
                'PersonalReserved' => $secssUtil->getEncValue(), //个人信息域
                'ProductType'      => env('ORDERS.ProductType'), //产品类型 海关信息申报业务 有疑问
                'TranType'         => env('ORDERS.TranType'), //交易类型 0008-后台消费 有疑问
            ];
            // 签名
            $secssUtil->sign($data);
            if ("00" !== $secssUtil->getErrCode()) $this->throwError($secssUtil->getErrMsg());
            // 获取签名
            $data['Signature'] = $secssUtil->getSign();
            $submit_data       = json_encode($data);
            $result_not_format = httpPostString(env('ORDERS.declare_url'), http_build_query($data), ['Content-Type: application/x-www-form-urlencoded;charset=utf-8']); //x-www-form-urlencoded form-data
            if (empty($result_not_format)) $this->throwError('支付单申报失败');
            parse_str($result_not_format, $result);
            if (!isset($result['respCode'])) {
                $result['respCode'] = '';
                $result['respMsg']  = $result_not_format;
            }
            // 写入响应结果
            $log = array(
                'platform'       => 3,
                'main_order_no'  => $param['main_order_no'],
                'trade_no'       => $order_info['tradeno'],
                'submit_data'    => $submit_data,
                'request_status' => $result['respCode'] ?? '',
                'request_msg'    => $result['respMsg'] ?? '',
                'created_time'   => time(),
            );
            $msg = '';
            if ($result['respCode'] == "0025") {
                $log['result_code'] = 'SUCCESS';
                $msg                = "重复推送";
            }
            if ($result['respCode'] == "1015") {
                $log['result_code'] = 'SUCCESS';
                $msg                = "支付单发送中";
            }
            $resultData = array(
                'push_status'   => 1,
                'msg'           => $msg,
                'main_order_no' => $param['main_order_no'],
                'sub_order_no'  => $order_info['sub_order_no'],
                'store_type'    => $order_info['store_type'],
                'ver_dept'      => 1
            );
            $this->declarePushResultDeal($resultData);
            Db::name('cross_declare_log')->insert($log);
            //同步响应失败处理
            if ($result['respCode'] != "1015" && $result['respCode'] != "0025") $this->throwError($result['respMsg']);
        } catch (\Exception $exception) {
            //推送结果处理
            $resultData = array(
                'push_status'   => 0,
                'msg'           => $exception->getMessage(),
                'main_order_no' => $param['main_order_no'],
                'sub_order_no'  => $order_info['sub_order_no'],
                'store_type'    => $order_info['store_type'],
                'ver_dept'      => 1
            );
            $this->declarePushResultDeal($resultData);
            $this->throwError($exception->getMessage());
        }
        return true;
    }

    /**
     * Description:支付信息海关申报回调-银联
     * Author: zrc
     * Date: 2022/5/9
     * Time: 10:09
     * @param $param
     * @return bool
     */
    public function declareNotify($param)
    {
        $notify_msg = '支付单推送成功';
        // 加载支付单申报class
        require_once(root_path() . "/extend/umsSecss/SecssUtil.php");
        $secssUtil        = new \SecssUtil();
        $securityPropFile = root_path() . "/extend/umsSecss/security.properties";
        $init_status      = $secssUtil->init($securityPropFile); //初始化安全控件
        if (!$init_status) {
            $notify_msg = '初始化异常';
        }
        // 验签
        $secssUtil->verify($param);
        if ("00" !== $secssUtil->getErrCode()) $this->throwError($secssUtil->getErrMsg());
        // 查询支付单推送记录
        $info = Db::name('cross_declare_log')->where(['main_order_no' => $param['MerOrderNo']])->order('id desc')->find();
        if (empty($info)) {
            $notify_msg = '支付单' . $param['MerOrderNo'] . '不存在';
        }
        // 编辑异步回调信息
        $data = array(
            'trade_no'      => $param['CpSeqId'],
            'notify_status' => $param['CustomsStat'],
            'notify_msg'    => $param['ErrMsg'] ?? $notify_msg,
            'response_data' => json_encode($param)
        );
        if ($param['CustomsStat'] == 3) {
            $data['result_code'] = 'SUCCESS';
        } else {
            $data['result_code'] = 'FAIL';
        }
        Db::name('cross_declare_log')->where(['id' => $info['id']])->update($data);
        return true;
    }

    /**
     * Description:后台萌牙发货-订单号/期数推萌牙
     * Author: zrc
     * Date: 2022/5/9
     * Time: 17:06
     * @param $excelData
     * @return bool
     */
    public function wmsShip($requestparams)
    {
        $params                  = $requestparams;
        $params['orderOrPeriod'] = trim($params['orderOrPeriod']);
        if ($params['type'] == 1) {
            //retry  5-重试优先 1-自动重试
            $result = $this->pushWms(['sub_order_no' => $params['orderOrPeriod'], 'operator' => $params['operator'], 'retry' => 5]);
        } else if ($params['type'] == 2) {
            $es      = new ElasticSearchService();
            $arr     = array(
                'index'  => ['periods'],
                'match'  => [['id' => $params['orderOrPeriod']]],
                'source' => ['periods_type'],
                'limit'  => 1,
            );
            $periods = $es->getDocumentList($arr);
            if (!isset($periods['data'][0]['periods_type'])) $this->throwError('未获取到期数频道');
            $order_type = config('config')['order_type'];//订单频道获取
            $where      = [];
            $where[]    = ['period', '=', $params['orderOrPeriod']];
            $where[]    = ['sub_order_status', '=', 1];
            $where[]    = ['push_wms_status', 'in', [0, 2]];
            $orderData  = Db::name($order_type[intval($periods['data'][0]['periods_type'])]['table'])->field('sub_order_no,order_type')->where($where)->select()->toArray();
            if (empty($orderData)) $this->throwError('未获取到可推送订单');
            foreach ($orderData as $key => $val) {
                if (count($orderData) == 1) {
                    $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'mark' => 1, 'period' => $params['orderOrPeriod'], 'operator' => $params['operator'], 'retry' => 5]));
                } else {
                    if ($key == 0) {
                        $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'mark' => 0, 'period' => $params['orderOrPeriod'], 'operator' => $params['operator'], 'retry' => 5]));
                    }
                    if ($key > 0 && $key < count($orderData) - 1) {
                        $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'period' => $params['orderOrPeriod'], 'operator' => $params['operator'], 'retry' => 5]));
                    }
                    if ($key == count($orderData) - 1) {
                        $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'mark' => 1, 'period' => $params['orderOrPeriod'], 'operator' => $params['operator'], 'retry' => 5]));
                    }
                }
            }
            //萌牙推送处理队列
            $pushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'push.wms',
                'data'          => $dataStr,
            );
            Log::write("订单推送WMS队列 3: " . json_encode($pushData));
            $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
            if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('数据推送队列失败');
        }
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError($result['error_msg']);
        return true;
    }

    /**
     * Description:后台恢复销售单-订单号/期数推T+
     * Author: zrc
     * Date: 2022/5/9
     * Time: 18:00
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function resumeSalesOrder($requestparams)
    {
        $params = $requestparams;
        if ($params['type'] == 1) {
            $result = $this->pushTplus(['sub_order_no' => $params['orderOrPeriod'], 'operator' => $params['operator']]);
        } else if ($params['type'] == 2) {
            $es      = new ElasticSearchService();
            $arr     = array(
                'index'  => ['periods'],
                'match'  => [['id' => $params['orderOrPeriod']]],
                'source' => ['periods_type'],
                'limit'  => 1,
            );
            $periods = $es->getDocumentList($arr);
            if (!isset($periods['data'][0]['periods_type'])) $this->throwError('未获取到期数频道');
            $order_type = config('config')['order_type'];//订单频道获取
            $where      = [];
            $where[]    = ['period', '=', $params['orderOrPeriod']];
            $where[]    = ['sub_order_status', 'in', [2, 3]];
            $where[]    = ['push_t_status', 'in', [0, 1, 2]];
            $orderData  = Db::name($order_type[intval($periods['data'][0]['periods_type'])]['table'])->field('sub_order_no,order_type')->where($where)->select()->toArray();
            if (empty($orderData)) $this->throwError('未获取到可推送订单');
            foreach ($orderData as $key => $val) {
                if (count($orderData) == 1) {
                    $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'mark' => 1, 'period' => $params['orderOrPeriod'], 'operator' => $params['operator']]));
                } else {
                    if ($key == 0) {
                        $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'mark' => 0, 'period' => $params['orderOrPeriod'], 'operator' => $params['operator']]));
                    }
                    if ($key > 0 && $key < count($orderData) - 1) {
                        $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'period' => $params['orderOrPeriod'], 'operator' => $params['operator']]));
                    }
                    if ($key == count($orderData) - 1) {
                        $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'mark' => 1, 'period' => $params['orderOrPeriod'], 'operator' => $params['operator']]));
                    }
                }
            }
            //T+推送处理队列
            $pushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'push.erp',
                'data'          => $dataStr,
            );
            $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
            if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('数据推送队列失败');
        }
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError($result['error_msg']);
        return true;
    }

    /**
     * Description:订单批量推送代发仓
     * Author: zrc
     * Date: 2022/5/10
     * Time: 9:18
     * @param $excelData
     * @return bool
     */
    public function batchCrossPushWarehouse($excelData)
    {
        $data = [];
        foreach ($excelData as $key => $val) {
            $main_order_no = trim($val[0]);
            if ($main_order_no) $data[] = base64_encode(json_encode(['main_order_no' => $main_order_no]));
        }
        //代发仓推送处理队列
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'push.cross.external.warehouse',
            'data'          => $data,
        );
        $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('excel数据推送队列失败');
        return true;
    }

    /**
     * Description:订单批量推送支付单
     * Author: zrc
     * Date: 2022/5/10
     * Time: 9:20
     * @param $excelData
     * @return bool
     */
    public function batchCustomsDeclare($excelData)
    {
        $data = [];
        foreach ($excelData as $key => $val) {
            $main_order_no = trim($val[0]);
            if ($main_order_no) $data[] = base64_encode(json_encode(['main_order_no' => $main_order_no]));
        }
        //代发仓推送处理队列
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'push.cross.paydoc',
            'data'          => $data,
        );
        $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('excel数据推送队列失败');
        return true;
    }

    /**
     * Description:跨境订单批量直推
     * Author: zrc
     * Date: 2022/7/11
     * Time: 13:38
     * @param $excelData
     * @return bool
     * @throws \Exception
     */
    public function batchCustomsDirectPush($excelData)
    {
        $data = [];
        foreach ($excelData as $key => $val) {
            $main_order_no = trim($val[0]);
            if ($main_order_no) $data[] = base64_encode(json_encode(['main_order_no' => $main_order_no]));
        }
        //代发仓推送处理队列
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'push.cross.external.customsDirectPush',
            'data'          => $data,
        );
        $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('excel数据推送队列失败');
        return true;
    }

    /**
     * Description:跨境直推接口（支付单+代发仓）
     * Author: zrc
     * Date: 2022/6/8
     * Time: 16:12
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function customsDirectPush($params)
    {
        //查询订单详情
        $orderInfo = $this->crossGetOrderInfo($params['main_order_no']);
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        //订单验证
        $this->checkCrossOrder($orderInfo);
        $params['order_info'] = $orderInfo;
        //支付单推送
        if (in_array($orderInfo['payment_subject'], [3, 4])) {
            $this->customsDeclare($params);
        } else {
            $this->UnionPayCustomsDeclare($params);
        }
        //代发仓推送
        if ($orderInfo['store_type'] == 1) {
            $this->crossPushGuSiTi($params);
        } else if ($orderInfo['store_type'] == 2) {
            $this->crossPushNanSha($params);
        }
        return true;
    }

    /**
     * Description:萌牙/T+推送成功结果处理
     * Author: zrc
     * Date: 2022/6/29
     * Time: 17:20
     * @param $params
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function pushSuccessDeal($params)
    {
        $order_no_arr = explode(',', $params['order_no_str']);
        $sub_order_group = [];
        foreach ($order_no_arr as &$val) {
            $msg                       = '';
            $updateData['update_time'] = time();
            switch ($params['type']) {
                case 1:
                    $msg                           = '推送萌牙成功';
                    $updateData['push_wms_status'] = 1;
                    $updateData['remarks']         = $msg;
                    break;
                case 2:
                    $msg                         = '推送T+成功';
                    $updateData['push_t_status'] = 1;
                    $updateData['remarks']       = $msg;
                    break;
                case 3:
                    $msg                           = '推送京东成功';
                    $updateData['push_wms_status'] = 1;
                    $updateData['remarks']         = $msg;
                    break;
                case 4:
                    $msg                         = '推送U8C成功';
                    $updateData['push_t_status'] = 1;
                    $updateData['remarks']       = $msg;
                    break;
            }
            if (isset($params['push_status']) && is_numeric($params['push_status'])) {
                $msg = $params['remarks'];
                switch ($params['type']) {
                    case 1:
                    case 3:
                        $updateData['push_wms_status'] = $params['push_status'];
                        break;
                    case 2:
                    case 4:
                        $updateData['push_t_status'] = $params['push_status'];
                        break;
                }
                $updateData['remarks'] = $msg;
            }
            //修改订单推送状态
            $order_type = config('config')['order_type'];//订单频道获取
            Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $val])->update($updateData);
            //添加订单备注
            $remarks = array(
                'sub_order_no' => $val,
                'admin_id'     => 0,
                'remarks'      => $msg,
                'created_time' => time(),
            );
            Db::name('order_remarks')->insert($remarks);

            if (($updateData['push_wms_status'] ?? 0) == 1) {
                $sub_order_group = $sub_order_list = [];
                $sub_order_field = 'main_order_no,period,sub_order_no,is_gift,order_type';
                $sub_order       = Es::name(Es::ORDERS)->where([['_id', '==', $val]])->field($sub_order_field)->find();

                if (!empty($sub_order) && !empty($sub_order['main_order_no'])) {
                    $sub_order_list = Es::name(Es::ORDERS)->where([['main_order_no', '==', $sub_order['main_order_no']]])->field($sub_order_field)->select()->toArray();
                    foreach ($sub_order_list as $sbo_item) {
                        $sub_order_group[($sbo_item['is_gift'] ?? 0)][] = $sbo_item;
                    }
                }


                $is_gift       = $sub_order['is_gift'] ?? 0;
                $allow_push    = true;
                $auto_push_arr = [];

                if (($is_gift == 0) && !empty($sub_order_group[1])) {
                    $order_type_conf = config('config')['order_type'];
                    $all_periods     = array_column($sub_order_list, 'period');
                    $erp_ids         = Db::table('vh_commodities.vh_periods_product_inventory')->where('period', 'in', $all_periods)->column('erp_id', 'period');
                    foreach ($sub_order_group as $k => $v) {
                        foreach ($v as $kk => $vv) {
                            $vv_erp_id                                   = $erp_ids[$vv['period']];
                            $sub_order_group[$k][$kk]['erp_id']          = $vv_erp_id;
                            $sub_order_group[$k][$kk]['push_wms_status'] = Db::name($order_type_conf[intval($vv['order_type'])]['table'])->where(['sub_order_no' => $vv['sub_order_no']])->value('push_wms_status');
                        }
                    }
                    $cp_erp_ids       = array_column($sub_order_group[0], 'erp_id');
                    $cp_push_wms_done = implode(',', array_values(array_unique(array_column($sub_order_group[0], 'push_wms_status')))) == '1';
                    $merge_group      = [];

                    foreach ($sub_order_group[1] as $zp_order) {
                        if (in_array($zp_order['erp_id'], $cp_erp_ids)) {
                            $merge_group[$zp_order['erp_id']][1][] = $zp_order;
                            foreach ($sub_order_group[0] as $cp_order) {
                                if ($zp_order['erp_id'] == $cp_order['erp_id']) {
                                    if ($cp_order['push_wms_status'] != '1') {
                                        $merge_group[$zp_order['erp_id']][0][] = $cp_order;
                                    }
                                }
                            }
                        }
                    }

                    if (!empty($merge_group[$erp_ids[$sub_order['period']]])) {
                        if (empty($merge_group[$erp_ids[$sub_order['period']]][0])) {
                            $auto_push_arr = $merge_group[$erp_ids[$sub_order['period']]][1] ?? [];
                        }
                    } else {
                        if ($cp_push_wms_done) {
                            $auto_push_arr = $sub_order_group[1];
                        }
                    }
                }

                if (!empty($auto_push_arr)) {
                    foreach ($auto_push_arr as $gift_order) {
                        if (in_array($gift_order['push_wms_status'], [0, 2])) {
                            $this->pushWms(['sub_order_no' => $gift_order['sub_order_no'], 'order_type' => $gift_order['order_type']]);
                        }
                    }
                }
            }

        }
        return true;
    }

    public function gift()
    {
        $field       = 'main_order_id,period,is_ts,is_gift,sub_order_no,push_wms_status,order_type,sub_order_status,refund_status,is_delete';
        $gift_orders = Es::name(Es::ORDERS)->where([
            ['sub_order_status', 'in', [1, 2, 3]],
            ['refund_status', 'in', [0, 3]],
            ['is_delete', '==', 0],
            ['is_gift', '==', 1],
            ['is_ts', '==', 0],
            ['push_wms_status', 'in', [0, 2]],
        ])->field($field)->select()->toArray();
        $orders      = Es::name(Es::ORDERS)->where([
            ['main_order_id', 'in', array_values(array_unique(array_column($gift_orders, 'main_order_id')))]
        ])->field($field)->select()->toArray();

        $orders_group = [];
        foreach ($orders as $order) {
            $orders_group[$order['main_order_id']][$order['is_gift']][] = $order;
        }

        foreach ($orders_group as $sub_order_group) {
            $push          = true;
            $auto_push_arr = [];
            if (!empty($sub_order_group[1])) {
                $order_type_conf = config('config')['order_type'];
                $all_periods     = array_column(array_merge(...$sub_order_group), 'period');
                $erp_ids         = Db::table('vh_commodities.vh_periods_product_inventory')->where('period', 'in', $all_periods)->column('erp_id', 'period');
                foreach ($sub_order_group as $k => $v) {
                    foreach ($v as $kk => $vv) {
                        $vv_erp_id                                   = $erp_ids[$vv['period']];
                        $sub_order_group[$k][$kk]['erp_id']          = $vv_erp_id;
                        $sub_order_group[$k][$kk]['push_wms_status'] = Db::name($order_type_conf[intval($vv['order_type'])]['table'])->where(['sub_order_no' => $vv['sub_order_no']])->value('push_wms_status');
                    }
                }
                $cp_erp_ids       = array_column($sub_order_group[0], 'erp_id');
                $cp_push_wms_done = implode(',', array_values(array_unique(array_column($sub_order_group[0], 'push_wms_status')))) == '1';
                if ($cp_push_wms_done) {
                    $auto_push_arr = $sub_order_group[1];
                } else {
                    $merge_group = [];
                    foreach ($sub_order_group[1] as $zp_order) {
                        if (in_array($zp_order['erp_id'], $cp_erp_ids)) {
                            $merge_group[$zp_order['erp_id']][1][] = $zp_order;
                            foreach ($sub_order_group[0] as $cp_order) {
                                if ($zp_order['erp_id'] == $cp_order['erp_id']) {
                                    if ($cp_order['push_wms_status'] != '1') {
                                        $merge_group[$zp_order['erp_id']][0][] = $cp_order;
                                    }
                                }
                            }
                        }
                    }

                    foreach ($merge_group as $wh_id => $wh_group) {
                        if (empty($wh_group[0]) && !empty($wh_group[1])) {
                            foreach ($wh_group[1] as $item) {
                                $auto_push_arr[] = $item;
                            }
                        }
                    }
                }

            }

            foreach (($sub_order_group[0] ?? []) as $og0i) {
                if (!in_array($og0i['is_delete'], [0])) $push = false;
                if (!in_array($og0i['refund_status'], [0, 3])) $push = false;
                if (!in_array($og0i['sub_order_status'], [1, 2, 3])) $push = false;
            }

            if ($push && !empty($auto_push_arr)) {
                foreach ($auto_push_arr as $gift_order) {
                    $wms_push = true;
                    if (!in_array($gift_order['is_delete'], [0])) $wms_push = false;
                    if (!in_array($gift_order['refund_status'], [0, 3])) $wms_push = false;
                    if (!in_array($gift_order['sub_order_status'], [1, 2, 3])) $wms_push = false;
                    if (!in_array($gift_order['push_wms_status'], [0, 2])) $wms_push = false;
                    if (!in_array($gift_order['is_ts'], [0])) $wms_push = false;

                    if ($wms_push) {
                        $this->pushWms(['sub_order_no' => $gift_order['sub_order_no'], 'order_type' => $gift_order['order_type']]);
                    }
                }
            }
        }

    }

    /**
     * Description:贵重物品信息
     * Author: gangh
     * Date: 2024/12/4
     * @param $params
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function valuableInfo($params)
    {
        return Db::name('valuables')->where('id', 1)->find();
    }

    /**
     * Description:设置贵重物品信息
     * Author: gangh
     * Date: 2024/12/4
     * @param $params
     * @return bool
     * @throws \think\db\exception\DbException
     */
    public function setValuableInfo($params)
    {
        try {
            $params['update_time'] = time();
            Db::name('valuables')->where('id', 1)->update($params);

        } catch (\Exception $e) {
            $this->throwError($e->getMessage());
        }
        return true;
    }

}