<?php


namespace app\service;


use app\BaseService;
use app\service\AfterSales as AfterSalesService;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\Order as OrderService;
use PhpOffice\PhpSpreadsheet\Worksheet\DataValidation;
use think\facade\Db;
use think\facade\Log;

class Additional extends BaseService
{
    /**
     * Description:异步回调处理补差价订单
     * Author: zrc
     * Date: 2022/3/25
     * Time: 14:36
     * @param $requestparams
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function expressAdditionalDeal($requestparams)
    {
        $params = $requestparams;
        //查询订单类型
        $es   = new ElasticSearchService();
        $arr  = array(
            'index'  => ['orders'],
            'match'  => [['sub_order_no.keyword' => $params['related_order_no']]],
            'source' => ['sub_order_no', 'order_type', 'sub_order_status', 'freeze_status']
        );
        $data = $es->getDocumentList($arr);
        if (!isset($data['data'][0]['order_type'])) $this->throwError('未获取到订单类型');
        $order_type   = $data['data'][0]['order_type'];
        $orderService = new OrderService();
        //用户信息处理
        $consignee                 = $params['consignee'];
        $consignee_phone           = $params['consignee_phone'];
        $encrypt                   = cryptionDeal(2, [$consignee, $consignee_phone], $params['uid'], '前端用户');
        $consignee                 = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
        $consignee_phone           = isset($encrypt[$consignee_phone]) ? $encrypt[$consignee_phone] : '';
        $params['consignee']       = $consignee;
        $params['consignee_phone'] = $consignee_phone;
        $updateData                = array(
            'operator'        => $params['uid'],
            'order_type'      => $order_type,
            'order_no'        => $params['related_order_no'],
            'express_type'    => $params['express_type'],
            'consignee'       => $params['consignee'],
            'consignee_phone' => $params['consignee_phone'],
            'province_id'     => $params['province_id'],
            'city_id'         => $params['city_id'],
            'district_id'     => $params['district_id'],
            'address'         => $params['address'],
        );
        $orderService->updateOrder($updateData);
        $msg = '修改收货信息/快递方式补差价订单';
        //添加订单备注
        $orderService = new OrderService();
        $remark       = array(
            'sub_order_no' => $params['related_order_no'],
            'order_type'   => $order_type,
            'content'      => $msg . ':' . $params['sub_order_no'],
            'admin_id'     => 0
        );
        $orderService->createRemarks($remark);
        $remarks = array(
            'sub_order_no' => $params['sub_order_no'],
            'order_type'   => 0,
            'content'      => '关联订单：' . $params['related_order_no'],
            'admin_id'     => 0
        );
        $orderService->createRemarks($remarks);
        //订单解冻，用户自动升级冷链需要
        if ($data['data'][0]['freeze_status'] == 1) {
            $freezeData        = array(
                'sub_order_no' => $params['related_order_no'],
                'order_type'   => $order_type,
                'type'         => 2,
            );
            $afterSalesService = new AfterSalesService();
            try {
                $afterSalesService->freezeOrder($freezeData);
            } catch (\Exception $e) {
            }
        }
        return true;
    }

    /**
     * Description:获取订单相关文本
     * Author: zrc
     * Date: 2021/12/16
     * Time: 11:36
     * @param $requestparams
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderText($params)
    {
        if (isset($params['start_time']) && isset($params['end_time'])) {
            $stime   = substr($params['start_time'], 0, strrpos($params['start_time'], ":"));
            $sminute = substr($params['start_time'], strripos($params['start_time'], ":") + 1);
            $etime   = substr($params['end_time'], 0, strrpos($params['end_time'], ":"));
            $eminute = substr($params['end_time'], strripos($params['end_time'], ":") + 1);
            $now     = date('H', time());
            $nminute = date('i', time());
            if ($stime * 60 + $sminute > $now * 60 + $nminute) {
                $time    = str_pad(($stime + 3), 2, '0', STR_PAD_LEFT) . ':' . $sminute;
                $times   = str_pad($stime, 2, '0', STR_PAD_LEFT) . ':' . $sminute;
                $content = '亲爱的兔友，你的订单将于今日' . $times . '开始配送，预计' . $time . '之前送达，感谢您的支持。';
            } else if ($stime * 60 + $sminute <= $now * 60 + $nminute && $now * 60 + $nminute <= $etime * 60 + $eminute - 30 && $now + 3 < 24) {
                $time    = str_pad(($now + 3), 2, '0', STR_PAD_LEFT) . ':' . date('i', time());
                $times   = str_pad($now, 2, '0', STR_PAD_LEFT) . ':' . date('i', time());
                $content = '亲爱的兔友，你的订单将于今日' . $times . '开始配送，预计' . $time . '之前送达，感谢您的支持。';
            } else {
                $time    = str_pad(($stime + 3), 2, '0', STR_PAD_LEFT) . ':' . $sminute;
                $times   = str_pad($stime, 2, '0', STR_PAD_LEFT) . ':' . $sminute;
                $content = '亲爱的兔友，你的订单将于次日' . $times . '开始配送，预计' . $time . '之前送达，感谢您的支持。';
            }
            $result = array(array(
                'id'      => 1,
                'type'    => 14,
                'content' => $content,
                'sort'    => 0,
                'status'  => 1,
                'title'   => '',
            ));
        } else if (isset($params['is_cross']) && $params['is_cross'] == 1) {//跨境冷链须知
            $result = Db::name('order_text')->where(['type' => 17, 'status' => 1])->order('sort asc,id asc')->select()->toArray();
        } else {
            $result = Db::name('order_text')->where(['type' => $params['type'], 'status' => 1])->order('sort asc,id asc')->select()->toArray();
        }
        return $result;
    }

    /**
     * Description:物流同步推送队列
     * Author: zrc
     * Date: 2022/6/14
     * Time: 11:32
     * @param $params
     * @return bool
     */
    public function logisticSync($params, $admin_id = 0)
    {
        $data = [];
        $header = array_shift($params);
        $num_temp = [
            '序号'
        ];
        $order_no_temp = [
            '原始订单号',
            '子订单号',
            '订单号',
            '原始订单号(子订单号)',
        ];
        $express_number_temp = [
            '物流单号',
            '物流单号(多个单号#号分开)',
            '快递单号',
            '发货单号',
        ];
        $express_type_temp = [
            '快递方式下拉选择（不填默认订单原快递方式）',
            '快递方式',
            '物流方式',
            '发货方式',
        ];
        $wh_temp = [
            '仓库（代发仓请填写对应代发仓）',
            '仓库',
        ];
        $num_keys = $order_no_keys = $express_number_keys = $express_type_keys = $wh_keys = [];
        foreach ($header as $hk => $hname) {
            foreach ($num_temp as $nt) {
                if (strpos($hname, $nt) !== false) {
                    $num_keys[$hk] = $hk;
                }
            }
            foreach ($order_no_temp as $nt) {
                if (strpos($hname, $nt) !== false) {
                    $order_no_keys[$hk] = $hk;
                }
            }
            foreach ($express_number_temp as $nt) {
                if (strpos($hname, $nt) !== false) {
                    $express_number_keys[$hk] = $hk;
                }
            }


            foreach ($express_type_temp as $nt) {
                if (strpos($hname, $nt) !== false) {
                    $express_type_keys[$hk] = $hk;
                }
            }

            foreach ($wh_temp as $nt) {
                if (strpos($hname, $nt) !== false) {
                    $wh_keys[$hk] = $hk;
                }
            }
        }
        foreach (array_values($params) as $key => $val) {
            $num = $sub_order_no = $express_number = $express_name = $wh_name = '';
            foreach ($num_keys as $nk) {
                if (empty($num)) $num = $val[$nk] ?? '';
                if (!empty($num)) break;
            }
            foreach ($order_no_keys as $nk) {
                if (empty($sub_order_no)) $sub_order_no = $val[$nk] ?? '';
                if (!empty($sub_order_no)) break;
            }
            foreach ($express_number_keys as $nk) {
                if (empty($express_number)) $express_number = $val[$nk] ?? '';
                if (!empty($express_number)) break;
            }
            foreach ($express_type_keys as $nk) {
                if (empty($express_name)) $express_name = $val[$nk] ?? '';
                if (!empty($express_name)) break;
            }
            foreach ($wh_keys as $nk) {
                if (empty($wh_name)) $wh_name = $val[$nk] ?? '';
                if (!empty($wh_name)) break;
            }

            if (empty($num) || empty($sub_order_no) || empty($express_number)) {
                $this->throwError('上传的文件内容与模板不符！');
            }
            $num            = trim($num);
            $sub_order_no   = trim($sub_order_no);
            $express_number = trim($express_number);
            $express_name   = trim($express_name);
            $wh_name        = trim($wh_name);

            switch ($express_name) {
                case '顺丰快递' :
                    $express_type = 2;
                    break;
                case '顺丰冷链' :
                    $express_type = 3;
                    break;
                case '京东快递(不保价)' :
                case '京东快递（不保价）' :
                    $express_type = 4;
                    break;
                case '京东快递(保价)' :
                case '京东快递（保价）' :
                    $express_type = 5;
                    break;
                case '客户仓库自提' :
                    $express_type = 6;
                    break;
                case '京东快运' :
                    $express_type = 10;
                    break;
                case '欣运物流自提' :
                    $express_type = 21;
                    break;
                case '欣运物流上门' :
                    $express_type = 22;
                    break;
                case '京东TC' :
                    $express_type = 23;
                    break;
                case '韵达快递' :
                    $express_type = 51;
                    break;
                case '圆通速递' :
                    $express_type = 52;
                    break;
                case '联邦快递' :
                    $express_type = 53;
                    break;
                case '中通快递' :
                    $express_type = 54;
                    break;
                case '申通快递' :
                    $express_type = 55;
                    break;
                case 'EMS' :
                    $express_type = 56;
                    break;
                case '德邦快递' :
                    $express_type = 57;
                    break;
                case '天天快递' :
                    $express_type = 58;
                    break;
                case '优速快递' :
                    $express_type = 59;
                    break;
                case '百世快运' :
                    $express_type = 60;
                    break;
                case '顺心捷达' :
                    $express_type = 61;
                    break;
                case '九曳供应链' :
                    $express_type = 62;
                    break;
                case '同城快寄' :
                    $express_type = 63;
                    break;
                case '跨越速运' :
                    $express_type = 64;
                    break;
                case '极兔快递' :
                    $express_type = 66;
                    break;
                case '壹米滴答' :
                    $express_type = 67;
                    break;
                case '商家配送' :
                    $express_type = 95;
                    break;
                default:
                    $express_type = 0;
            }
            if($express_type == 0){
                $express_name = '';
            }
            if (empty($express_name)) {
                if (stripos($express_number, 'SF') !== false) {
                    $express_name = '顺丰快递';
                } elseif (stripos($express_number, 'JD') !== false) {
                    $express_name = '京东快递(不保价)';
                } elseif (stripos($express_number, 'DPK') !== false) {
                    $express_name = '德邦快递';
                } elseif (stripos($express_number, 'YT') !== false) {
                    $express_name = '圆通速递';
                } elseif (stripos($express_number, 'JT') !== false) {
                    $express_name = '极兔快递';
                } else {
                    $express_name = '商家配送';
                }
            }
            $wh_names = [
                '佰酿云酒（代发仓）' => '034',
                '酒云微醺代发仓' => '272',
                '重庆办公室' => '052',
                "跨境破损仓" => "377",
                "供应商承担仓" => "375",
                "酒云研酒所（重庆解放碑店）" => "043",
                "上海鸿寿坊店" => "363",
                "跨境退货仓" => "316",
                "南通破损仓" => "102",
                "佰酿美酒办公室" => "188",
                "上海OT仓" => "023",
            ];
//            $wh_name   = trim($val[4]);
//            if (!in_array($wh_name, array_keys($wh_names))) {
//                $rows = $key + 2;
//                $this->throwError('模板表格第' . $rows . '行仓库数据有误，请调整后重新提交！');
//            }
            $wh_code = $wh_names[$wh_name] ?? null;
            $is_robot = request()->header('admin_id', 'none') == 0;
            if(!$is_robot && empty($wh_code)) $this->throwError('请填写正确的仓库!');
            if (empty($num)) continue;
            if (empty($sub_order_no) || empty($express_number)) {
                $rows = $key + 2;
                $this->throwError('模板表格第' . $rows . '行数据有误，请调整后重新提交！');
            }
            switch ($express_name) {
                case '顺丰快递' :
                    $express_type = 2;
                    break;
                case '顺丰冷链' :
                    $express_type = 3;
                    break;
                case '京东快递(不保价)' :
                case '京东快递（不保价）' :
                    $express_type = 4;
                    break;
                case '京东快递(保价)' :
                case '京东快递（保价）' :
                    $express_type = 5;
                    break;
                case '客户仓库自提' :
                    $express_type = 6;
                    break;
                case '京东快运' :
                    $express_type = 10;
                    break;
                case '欣运物流自提' :
                    $express_type = 21;
                    break;
                case '欣运物流上门' :
                    $express_type = 22;
                    break;
                case '京东TC' :
                    $express_type = 23;
                    break;
                case '韵达快递' :
                    $express_type = 51;
                    break;
                case '圆通速递' :
                    $express_type = 52;
                    break;
                case '联邦快递' :
                    $express_type = 53;
                    break;
                case '中通快递' :
                    $express_type = 54;
                    break;
                case '申通快递' :
                    $express_type = 55;
                    break;
                case 'EMS' :
                    $express_type = 56;
                    break;
                case '德邦快递' :
                    $express_type = 57;
                    break;
                case '天天快递' :
                    $express_type = 58;
                    break;
                case '优速快递' :
                    $express_type = 59;
                    break;
                case '百世快运' :
                    $express_type = 60;
                    break;
                case '顺心捷达' :
                    $express_type = 61;
                    break;
                case '九曳供应链' :
                    $express_type = 62;
                    break;
                case '同城快寄' :
                    $express_type = 63;
                    break;
                case '跨越速运' :
                    $express_type = 64;
                    break;
                case '极兔快递' :
                    $express_type = 66;
                    break;
                case '壹米滴答' :
                    $express_type = 67;
                    break;
                case '商家配送' :
                    $express_type = 95;
                    break;
                default:
                    $express_type = 0;
            }
            if (count($params) == 1) {
                $data[] = base64_encode(json_encode(['sub_order_no' => $sub_order_no, 'mark' => 1, 'express_number' => $express_number, 'express_type' => $express_type, 'admin_id' => $admin_id, 'express_name' => $express_name, 'wh_code' => $wh_code]));
            } else {
                if ($key == 0) {
                    $data[] = base64_encode(json_encode(['sub_order_no' => $sub_order_no, 'mark' => 0, 'express_number' => $express_number, 'express_type' => $express_type, 'admin_id' => $admin_id, 'express_name' => $express_name, 'wh_code' => $wh_code]));
                }
                if ($key > 0 && $key < count($params) - 1) {
                    $data[] = base64_encode(json_encode(['sub_order_no' => $sub_order_no, 'express_number' => $express_number, 'express_type' => $express_type, 'admin_id' => $admin_id, 'express_name' => $express_name, 'wh_code' => $wh_code]));
                }
                if ($key == count($params) - 1) {
                    $data[] = base64_encode(json_encode(['sub_order_no' => $sub_order_no, 'mark' => 1, 'express_number' => $express_number, 'express_type' => $express_type, 'admin_id' => $admin_id, 'express_name' => $express_name, 'wh_code' => $wh_code]));
                }
            }
        }
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'logistic_sync',
            'data'          => $data,
        );
        $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('excel数据推送队列失败');
        return true;
    }

    /**
     * Description:物流同步队列处理
     * Author: zrc
     * Date: 2022/6/14
     * Time: 17:10
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function logisticSyncDeal($params)
    {
        Log::info('物流同步处理参数:{info}', ['info' => $params]);
        $params = json_decode($params, true);
        //查询订单类型
        $searchData  = array(
            'sub_order_no' => $params['sub_order_no'],
            'fields'       => 'sub_order_no,order_type,sub_order_status,express_type,express_number,push_t_status,warehouse_code,delivery_time,is_supplier_delivery,order_from_thirdparty'
        );
        $batchSearch = httpGet(env('ITEM.MYSQL_BATCH_SEARCH') . '/services/v3/batchsearch/orders/fields', $searchData);
        if (isset($batchSearch['data']['order_type']) && ($batchSearch['data']['order_type'] == 7)) $batchSearch['data']['warehouse_code'] = $batchSearch['data']['warehouse_code'] ?? '';
        if (!isset($batchSearch['data']['type']) || !isset($batchSearch['data']['sub_order_no']) || !isset($batchSearch['data']['sub_order_status']) || !isset($batchSearch['data']['express_type']) || !isset($batchSearch['data']['express_number']) || !isset($batchSearch['data']['push_t_status']) || !isset($batchSearch['data']['warehouse_code']) || !isset($batchSearch['data']['delivery_time'])) {
            //回执信息处理
            $this->logisticSyncReceipt($params, 10002, '未获取到订单信息');
            $this->throwError($params['sub_order_no'] . '未获取到订单信息');
        }
        if (!in_array($batchSearch['data']['sub_order_status'], [1, 2])) {
            //回执信息处理
            $this->logisticSyncReceipt($params, 10002, '订单状态异常，同步物流失败');
            $this->throwError($params['sub_order_no'] . '订单状态异常，同步物流失败');
        }
        $express_number_arr = explode('#', $params['express_number']);
        foreach ($express_number_arr as &$val) {
            $val = trim($val);
        }
        $express_number       = implode(',', $express_number_arr);
        $params['order_type'] = $batchSearch['data']['type'];
        $order_type           = config('config')['order_type'];//订单频道获取
        $updateData           = array(
            'express_number' => $express_number,
            'update_time'    => time(),
        );
        if ($batchSearch['data']['sub_order_status'] == 1) $updateData['sub_order_status'] = 2;
        if ($params['express_type'] > 0) $updateData['express_type'] = $params['express_type'];
        if ($batchSearch['data']['delivery_time'] == 0) $updateData['delivery_time'] = time();
        $force_push_t_plus = false;
        if (!empty($params['wh_code'])) {
            if (in_array(intval($params['order_type']), [0, 1, 3])) {
                $force_push_t_plus = true;
                $updateData['warehouse_code'] = $params['wh_code'];
            }
            if (in_array(intval($params['order_type']), [7])) {
                $updateData['warehouse_id'] = $params['wh_code'];
            }
        }
        Db::startTrans();
        try {
            $updateOrder = Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->update($updateData);
            if (empty($updateOrder)) $this->throwError('修改子订单信息失败');
            //添加订单备注
            $msg = '物流同步：' . $express_number;
            if ($params['express_type'] > 0) {
                $msg = '物流同步：' . $express_number . '-' . $params['express_name'];
            }
            $remarks      = array(
                'sub_order_no' => $params['sub_order_no'],
                'order_type'   => $params['order_type'],
                'content'      => $msg,
                'admin_id'     => $params['admin_id']
            );
            $orderService = new OrderService();
            $orderService->createRemarks($remarks);
            //记录日志
            $original_order_info = ['express_type' => $batchSearch['data']['express_type'], 'express_number' => $batchSearch['data']['express_number']];
            $log                 = array(
                'sub_order_no'        => $params['sub_order_no'],
                'express_type'        => $params['express_type'],
                'express_number'      => $express_number,
                'original_order_info' => json_encode($original_order_info),
                'admin_id'            => $params['admin_id'],
                'created_time'        => time(),
            );
            $addLog              = Db::name('logistic_sync_log')->insert($log);
            if (empty($addLog)) $this->throwError('记录同步物流日志失败');

            if ($params['order_type'] == 8) {
                // 线下订单发货后创建应收单
                pushQueue('orders', 'create_arap_ys_order', ['sub_order_no' => $params['sub_order_no']]);
            }
            
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            //回执信息处理
            $this->logisticSyncReceipt($params, 10002, $e->getMessage());
            $this->throwError($params['sub_order_no'] . $e->getMessage());
        }
        //回执信息处理
        $this->logisticSyncReceipt($params, 0, '物流同步成功');
        //未推送T+的订单同步物流触发推送
        if (($batchSearch['data']['push_t_status'] == 0) || $force_push_t_plus) {
            $pushData = array(
                'exchange_name' => 'orders',
                'routing_key'   => 'push.erp',
                'data'          => base64_encode(json_encode(['sub_order_no' => $params['sub_order_no'], 'operator' => $params['admin_id'], 'order_type' => $params['order_type']])),
            );
            $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
            if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('数据推送队列失败');
        }
        //三方代发订单推送运单号到三方平台
        if ($params['order_type'] == 7) {
//            if ($batchSearch['data']['is_supplier_delivery'] == 1) {
                $tr_info = Db::name('tripartite_order')->where('sub_order_no', $params['sub_order_no'])->find();
                $data = base64_encode(json_encode([
                    'main_order_no' => Db::name('order_main')->where('id', ($orderInfo['main_order_id'] ?? ''))->value('main_order_no'),
                    'store_id'      => $tr_info['store_id'] ?? '',
                    'store_name'    => $tr_info['store_name'] ?? '',
                    'orderNo'       => $params['sub_order_no'],
                    'wayBill'       => $express_number,
                    'expressType'   => $params['express_type']
                ]));
                $pushData = null;

                switch ($batchSearch['data']['order_from_thirdparty']) {
                    case 1:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_jd',
                            'data'          => $data,
                        );
                        break;
                    case 2:
                    case 4:
                    case 16:
                    case 24:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_taobao',
                            'data'          => $data,
                        );
                        break;
                    case 3:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_pdd',
                            'data'          => $data,
                        );
                        break;
                    case 13:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_tmall',
                            'data'          => $data,
                        );
                        break;
                    case 17:
                        if (!in_array(($tr_info['store_id'] ?? ''), [
                            '02223', //渝中区微醺酒业商行 （兔子）
                        ])) {
                            $pushData = array(
                                'exchange_name' => 'order_trackingnumbers',
                                'routing_key'   => 'order_trackingnumbers_xiaohongshu',
                                'data'          => $data,
                            );
                        }
                        break;
                    case 18:
                    case 19:
                    case 25:
                        $pushData = array(
                            'exchange_name' => 'openapi',
                            'routing_key'   => 'wms.order.status.24',
                            'data'          => $data,
                        );
                        break;
                    case 21:
                        $pushData = array(
                            'exchange_name' => 'openapi',
                            'routing_key'   => 'wms.order.status.21',
                            'data'          => $data,
                        );
                        break;
                    case 26:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_yuou',
                            'data'          => $data,
                        );
                        break;
                    case 27:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_ktt',
                            'data'          => $data,
                        );
                        break;
                    case 28:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_tmall_kj',
                            'data'          => $data,
                        );
                        break;
                    case 29:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_tmall_snack',
                            'data'          => $data,
                        );
                        break;
                    case 30:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_tzxq',
                            'data'          => $data,
                        );
                        break;
                    case 31:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_mldmydm',
                            'data'          => $data,
                        );
                        break;
                    case 32:
                        $pushData = array(
                            'exchange_name' => 'order_trackingnumbers',
                            'routing_key'   => 'order_trackingnumbers_mldredeem',
                            'data'          => $data,
                        );
                        break;
                }

                if ($pushData !== null) {
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                }
//            }
        }

        return true;
    }

    /**
     * Description:后台物流同步回执信息redis处理
     * Author: zrc
     * Date: 2022/8/16
     * Time: 17:03
     * @param $params
     * @param $error_code
     * @param $msg
     * @return bool
     */
    public function logisticSyncReceipt($params, $error_code, $msg)
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        if (isset($params['mark']) && $params['mark'] == 0) {
            $redis->del('logisticSync.temporary.log' . $params['admin_id']);
            $redis->del('logisticSync.log' . $params['admin_id']);
        }
        $redis->rPush('logisticSync.temporary.log' . $params['admin_id'], json_encode([$params['sub_order_no'], $error_code, $msg], JSON_UNESCAPED_UNICODE));
        $redis->expireAt('logisticSync.temporary.log' . $params['admin_id'], time() + 600);
        if (isset($params['mark']) && $params['mark'] == 1) {
            $data = $redis->lrange('logisticSync.temporary.log' . $params['admin_id'], 0, -1);
            foreach ($data as &$val) {
                $val = json_decode($val);
                $redis->rPush('logisticSync.log' . $params['admin_id'], json_encode($val, JSON_UNESCAPED_UNICODE));
            }
            $redis->expireAt('logisticSync.log' . $params['admin_id'], time() + 300);
            $redis->del('logisticSync.temporary.log' . $params['admin_id']);
        }
        //同步异常订单推送企业微信中台
        if ($error_code != 0) {
            //获取发起人企业微信信息
            $userInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $params['admin_id'], 'field' => 'userid,dept_id']);
            if (isset($userInfo['data'][$params['admin_id']]['userid'])) {
                $userid  = $userInfo['data'][$params['admin_id']]['userid'];
                $content = '#订单同步物流异常提示：';
                $content .= "订单号：{$params['sub_order_no']}，";
                $content .= "同步结果：失败，";
                $content .= "异常明细：{$msg}。";
                $msgData = array(
                    'content' => $content,
                    'userid'  => $userid,
                    'msgtype' => 'text',
                    'agentid' => 0,
                );
                httpPostString(env('ITEM.WECHART_URL') . '/wechat/v3/wecom/app/send', json_encode($msgData));
            }
        }
        return true;
    }

    /**
     * Description:获取后台物流同步回执信息
     * Author: zrc
     * Date: 2022/8/16
     * Time: 17:35
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getLogisticSyncReceipt($params)
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        $data = $redis->lrange('logisticSync.log' . $params['admin_id'], 0, -1);
        if (empty($data)) $this->throwError('暂无推送回执信息', 20103);
        $result = [];
        foreach ($data as $key => $val) {
            $val      = json_decode($val);
            $result[] = array(
                'sub_order_no' => $val[0],
                'status'       => $val[1],
                'msg'          => $val[2],
            );
        }
        $redis->del('logisticSync.log' . $params['admin_id']);
        return $result;
    }

    /**
     * Description:子订单退还库存
     * Author: zrc
     * Date: 2022/6/29
     * Time: 19:25
     * @param $params
     */
    public function subOrderReturnInventory($params)
    {
        $stock_param = Db::name('order_deal_log')->where(['main_order_no' => $params['main_order_no']])->value('stock_param');
        $result      = [];
        if (!empty($stock_param)) {
            $stock_param = json_decode($stock_param, true);
            $pushData    = array(
                'orderno' => $params['sub_order_no'],
                'groupid' => 0,
                'items'   => []
            );
            foreach ($stock_param['items'] as &$val) {
                if ($val['set_id'] == $params['package_id']) {
                    $pushData['items'][] = $val;
                }
            }
            if (empty($pushData['items'])) return $result;
            //退还库存
            $result = httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', json_encode($pushData));
            Log::write('***********子订单库存退还请求日志*********', json_encode($pushData, JSON_UNESCAPED_UNICODE) . '返回结果：' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }
        return $result;
    }

    /**
     * Description:批量修改订单发票状态
     * Author: zrc
     * Date: 2022/7/15
     * Time: 17:15
     * @param $params
     */
    public function batchUpdateInvoice($params)
    {
        Db::startTrans();
        try {
            $data = array();
            foreach ($params['order_info'] as &$val) {
                if (isset($val['sub_order_no']) && isset($val['order_type'])) {
                    $data[intval($val['order_type'])][] = $val['sub_order_no'];
                }
            }
            foreach ($data as $k => $v) {
                $updateData = array(
                    'invoice_progress' => $params['invoice_progress'],
                    'update_time'      => time(),
                );
                $order_type = config('config')['order_type'];//订单频道获取
                $result     = Db::name($order_type[$k]['table'])->where([['sub_order_no', 'in', $v]])->update($updateData);
                if (empty($result)) $this->throwError('修改订单开票状态失败');
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error(json_encode($params) . '：批量修改订单发票状态失败:' . $e->getMessage());
            //异常不抛错，发票模块容易崩
            //$this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:期数已购标识添加
     * Author: zrc
     * Date: 2022/7/22
     * Time: 10:18
     * @param $params
     * @return bool
     */
    public function periodPurchasedAdd($params)
    {
        $k_prefix = 'vinehoo.hought.';
        if  (!empty($params['special_type']) && $params['special_type'] == 4) {//定金
            $k_prefix = 'vinehoo.deposit.';
        }

        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(3);
        $redis->zIncrBy($k_prefix . $params['period'], 1, $params['uid']);
        return true;
    }

    /**
     * Description:期数已购标识移除
     * Author: zrc
     * Date: 2022/7/22
     * Time: 10:18
     * @param $params
     * @return bool
     */
    public function periodPurchasedDec($params)
    {
        $k_prefix = 'vinehoo.hought.';
        if  (!empty($params['special_type']) && $params['special_type'] == 4) {//定金
            $k_prefix = 'vinehoo.deposit.';
        }

        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(3);
        $result = $redis->zIncrBy($k_prefix . $params['period'], -1, $params['uid']);
        if ($result <= 0) {
            $redis->zRem($k_prefix . $params['period'], $params['uid']);
        }
        return true;
    }

    /**
     * Description:期数已购推送队列数据
     * Author: zrc
     * Date: 2022/7/22
     * Time: 16:36
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function periodPurchasedPushQueueData($params)
    {
        $periodsArr = explode(',', $params['periods']);
        foreach ($periodsArr as &$val) {
            $data['periods'] = $val;
            if (isset($params['end_time']) && !empty($params['end_time'])) $data['end_time'] = $params['end_time'];
            if (isset($params['is_del_key']) && $params['is_del_key'] == 1) $data['is_del_key'] = $params['is_del_key'];
            $dataStr[] = base64_encode(json_encode($data));
        }
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'period_purchased',
            'data'          => $dataStr,
        );
        $result   = httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        if (!isset($result['error_code']) || $result['error_code'] != 0) $this->throwError('数据推送队列失败');
        return true;
    }

    /**
     * Description:期数已购统计
     * Author: zrc
     * Date: 2022/7/22
     * Time: 15:53
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function periodPurchasedStatistics($params)
    {
        $periodsArr = explode(',', $params['periods']);
        $es         = new ElasticSearchService();
        $redis      = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(3);
        foreach ($periodsArr as &$val) {
            //获取期数频道
            $arr      = array(
                'index'  => ['periods'],
                'match'  => [['id' => $val]],
                'source' => ['periods_type'],
                'limit'  => 1
            );
            $esPeriod = $es->getDocumentList($arr);
            if (!isset($esPeriod['data'][0]['periods_type'])) $this->throwError('未获取到期数频道');
            $periods_type = $esPeriod['data'][0]['periods_type'];
            //获取需要添加已售标识的订单
            $order_type = config('config')['order_type'];//订单频道获取
            $where      = [];
            $where[]    = ['period', '=', $val];
            $where[]    = ['sub_order_status', 'in', [1, 2, 3]];
            if (in_array($periods_type, [0, 1, 3])) $where[] = ['group_status', 'in', [0, 2]];
            if (in_array($periods_type, [0, 1, 2, 3])) $where[] = ['refund_status', '<>', 2];
            if (isset($params['end_time']) && !empty($params['end_time'])) $where[] = ['created_time', '<', $params['end_time']];
            $uidArr = Db::name($order_type[intval($periods_type)]['table'])->field('uid,special_type')->where($where)->select()->toArray();
            if (isset($params['is_del_key']) && $params['is_del_key'] == 1) $redis->del('vinehoo.hought.' . $val);
            foreach ($uidArr as &$v) {
                $k_prefix = 'vinehoo.hought.';
                if  ($v['special_type'] == 4) {//定金
                    $k_prefix = 'vinehoo.deposit.';
                }
                $redis->zIncrBy($k_prefix . $val, 1, $v['uid']);
            }
        }
        Log::record('期数已购统计', json_encode($params));
        return true;
    }

    /**
     * Description:查询订单银联支付情况
     * Author: zrc
     * Date: 2022/7/27
     * Time: 13:56
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function inquireOrderPay($params)
    {
        if (strpos($params['main_order_no'], 'VHP') !== false) {//酒会
            $winePartyOrder = $this->httpGet(env('ITEM.WINEPARTY_URL') . '/wineparty/v3/order/getOrderInfo', ['main_order_no' => $params['main_order_no']]);
            if (!isset($winePartyOrder['error_code']) || $winePartyOrder['error_code'] != 0) $this->throwError('未获取到订单详情');
            $orderMain['payment_method']  = $winePartyOrder['data']['payment_method'];
            $orderMain['payment_subject'] = $winePartyOrder['data']['payment_subject'];
            $orderMain['order_type'] = 5;
        } else {
            $orderMain = Db::name('order_main')->field('payment_method,payment_subject,id,order_type')->where(['main_order_no' => $params['main_order_no']])->find();
            if (empty($orderMain)) $this->throwError('未获取到订单信息');
        }
        if ($orderMain['payment_method'] < 0) $this->throwError('订单未支付');
        switch ($orderMain['payment_subject']) {
            case 1:
            case 2:
                $queryData = array(
                    'main_order_no'  => $orderMain['payment_method'] . $params['main_order_no'],
                    'payment_method' => $orderMain['payment_method'],
                    'subject'        => $orderMain['payment_subject'],
                    'is_cross'       => $orderMain['order_type'] == 2 ? 1 : 0,
                );
                $umsInfo   = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/query', json_encode($queryData));
                if (!isset($umsInfo['error_code']) || $umsInfo['error_code'] != 0) $this->throwError(isset($umsInfo['error_msg']) ? $umsInfo['error_msg'] : '查询银联支付记录失败');
                if (isset($umsInfo['data']['billStatus'])) {
                    $result = array(
                        'payTime'     => $umsInfo['data']['billPayment']['payTime'],
                        'mid'         => $umsInfo['data']['mid'],
                        'merName'     => $umsInfo['data']['merName'],
                        'totalAmount' => $umsInfo['data']['billPayment']['totalAmount'] / 100,
                        'seqId'       => $umsInfo['data']['billPayment']['paySeqId'],
                        'merOrderId'  => $umsInfo['data']['billPayment']['merOrderId'],
                        'status'      => $umsInfo['data']['billPayment']['status'],
                    );
                } else {
                    $result = array(
                        'payTime'     => $umsInfo['data']['payTime'],
                        'mid'         => $umsInfo['data']['mid'],
                        'merName'     => $umsInfo['data']['merName'],
                        'totalAmount' => $umsInfo['data']['totalAmount'] / 100,
                        'seqId'       => $umsInfo['data']['seqId'],
                        'merOrderId'  => $umsInfo['data']['merOrderId'],
                        'status'      => $umsInfo['data']['status'],
                    );
                }
                switch ($result['status']) {
                    case "NEW_ORDER":
                        $result['status_msg'] = "新订单";
                        break;
                    case "UNKNOWN":
                        $result['status_msg'] = "不明确的交易状态";
                        break;
                    case "TRADE_CLOSED":
                        $result['status_msg'] = "在指定时间段内未支付时关闭的交易；在交易完成撤销成功时关闭的交易；支付失败的交易。";
                        break;
                    case "WAIT_BUYER_PAY":
                        $result['status_msg'] = "交易创建，等待买家付款。";
                        break;
                    case "TRADE_SUCCESS":
                        $result['status_msg'] = "支付成功";
                        break;
                    case "TRADE_REFUND":
                        $result['status_msg'] = "订单转入退货流程(退货可能是部分也可能是全部)";
                        break;
                }
                break;
            case 4:
                $method = 'alipay';
                if (in_array($orderMain['payment_method'], [3, 4, 5, 7, 8, 9])) $method = 'wechat';
                $queryData     = array(
                    'main_order_no'  => $params['main_order_no'],
                    'method'         => $method,
                    'payment_method' => $orderMain['payment_method']
                );
                $weiAndAliInfo = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/query', json_encode($queryData));
                if (!isset($weiAndAliInfo['error_code']) || $weiAndAliInfo['error_code'] != 0) $this->throwError(isset($weiAndAliInfo['error_msg']) ? $weiAndAliInfo['error_msg'] : '查询支付记录失败');
                if ($method == 'alipay') {
                    $result = array(
                        'payTime'     => '',
                        'mid'         => '',
                        'merName'     => '科技公司支付宝',
                        'totalAmount' => $weiAndAliInfo['data']['total_amount'],
                        'seqId'       => $weiAndAliInfo['data']['trade_no'],
                        'merOrderId'  => $weiAndAliInfo['data']['out_trade_no'],
                        'status'      => $weiAndAliInfo['data']['trade_status'],
                    );
                    switch ($result['status']) {
                        case "WAIT_BUYER_PAY":
                            $result['status_msg'] = "交易创建，等待买家付款";
                            break;
                        case "TRADE_CLOSED":
                            $result['status_msg'] = "未付款交易超时关闭，或支付完成后全额退款";
                            break;
                        case "TRADE_SUCCESS":
                            $result['status_msg'] = "交易支付成功";
                            break;
                        case "TRADE_FINISHED":
                            $result['status_msg'] = "交易结束，不可退款";
                            break;
                    }
                } else if ($method == 'wechat') {
                    $result = array(
                        'payTime'     => $weiAndAliInfo['data']['time_end'],
                        'mid'         => '',
                        'merName'     => '科技公司微信',
                        'totalAmount' => $weiAndAliInfo['data']['total_fee'] / 100,
                        'seqId'       => $weiAndAliInfo['data']['transaction_id'],
                        'merOrderId'  => $weiAndAliInfo['data']['out_trade_no'],
                        'status'      => $weiAndAliInfo['data']['trade_state'],
                    );
                    switch ($result['status']) {
                        case "SUCCESS":
                            $result['status_msg'] = "支付成功";
                            break;
                        case "REFUND":
                            $result['status_msg'] = "转入退款";
                            break;
                        case "NOTPAY":
                            $result['status_msg'] = "未支付";
                            break;
                        case "CLOSED":
                            $result['status_msg'] = "已关闭";
                            break;
                        case "REVOKED":
                            $result['status_msg'] = "已撤销(刷卡支付)";
                            break;
                        case "USERPAYING":
                            $result['status_msg'] = "用户支付中";
                            break;
                        case "PAYERROR":
                            $result['status_msg'] = "支付失败(其他原因，如银行返回失败)";
                            break;
                        case "ACCEPT":
                            $result['status_msg'] = "已接收，等待扣款";
                            break;
                    }
                }
                if (($orderMain['order_type'] ?? null) == 2 && ($result['status'] == 'REFUND')) {
                    if (Db::name('cross_order')->where([
                            'main_order_id' => $orderMain['id'],
                            'refund_status' => 2,
                        ])->count() > 0) {
                        $result['status'] == 'TRADE_REFUND';
                    }
                }
                break;
            case 11: // 添加华为支付查询
                $queryData = array(
                    'main_order_no' => $params['main_order_no'],
                    'payment_method' => $orderMain['payment_method']
                );
                $huaweiInfo = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/kit/query', json_encode($queryData));
                if (!isset($huaweiInfo['error_code']) || $huaweiInfo['error_code'] != 0) {
                    $this->throwError(isset($huaweiInfo['error_msg']) ? $huaweiInfo['error_msg'] : '查询华为支付记录失败');
                }

                // 计算总金额
                $totalAmount = 0;
                if (!empty($huaweiInfo['data']['subOrders'])) {
                    foreach ($huaweiInfo['data']['subOrders'] as $subOrder) {
                        $totalAmount += $subOrder['totalAmount'];
                    }
                }

                $result = array(
                    'payTime' => '', // 华为支付接口未返回支付时间
                    'mid' => $huaweiInfo['data']['combinedAppId'],
                    'merName' => '华为支付',
                    'totalAmount' => $totalAmount,
                    'seqId' => '', // 华为支付接口未返回平台流水号
                    'merOrderId' => $huaweiInfo['data']['combinedMercOrderNo'],
                    'status' => $huaweiInfo['data']['orderStatus'],
                );

                // 转换华为支付状态为统一格式
                switch ($result['status']) {
                    case "TRX_SUCCESS":
                        $result['status'] = "TRADE_SUCCESS";
                        $result['status_msg'] = "支付成功";
                        break;
                    case "TRX_FAILED":
                        $result['status'] = "TRADE_CLOSED";
                        $result['status_msg'] = "支付失败";
                        break;
                    case "TRX_APPLY":
                    case "TRX_PROC":
                        $result['status'] = "WAIT_BUYER_PAY";
                        $result['status_msg'] = "交易处理中";
                        break;
                    default:
                        $result['status'] = "UNKNOWN";
                        $result['status_msg'] = "未知状态";
                        break;
                }
                $has_refund = Db::name('refund_order')->where('main_order_no', $params['main_order_no'])->column('refund_order_no');
                if (!empty($has_refund)) {
                    foreach ($has_refund as $ref_code) {
                        try {
                            $ref_data = \Curl::refundQuery([
                                "main_order_no"   => $params['main_order_no'],//主订单号
                                "payment_method"  => 12,//支付方式，12鸿蒙APP收银台
                                "refund_order_no" => $ref_code,//退款订单号
                            ]);

                            if (!empty($ref_data['refundOrderStatus']) && $ref_data['refundOrderStatus'] == 'REFUND_SUCCESS') {
                                $result['status']     = "TRADE_REFUND";
                                $result['status_msg'] = "订单转入退货流程(退货可能是部分也可能是全部)";
                                break;
                            }
                        } catch (\Exception $e) {
                        }
                    }
                }

                break;
        }

        if (($orderMain['order_type'] ?? null) == 2 && ($result['status'] == 'REFUND')) {
            if (Db::name('cross_order')->where([
                    'main_order_id' => $orderMain['id'],
                    'refund_status' => 2,
                ])->count() > 0) {
                $result['status'] == 'TRADE_REFUND';
            }
        }
        return $result;
    }

    /**
     * Description:获取换绑仓库订单列表
     * Author: zrc
     * Date: 2022/8/10
     * Time: 15:57
     * @param $params
     * @return mixed
     */
    public function getExchangeOrderList($params)
    {
        $es      = new ElasticSearchService();
        $arr     = array(
            'index'  => ['periods'],
            'match'  => [['id' => $params['period']]],
            'source' => ['periods_type'],
            'limit'  => 1,
        );
        $periods = $es->getDocumentList($arr);
        if (!isset($periods['data'][0]['periods_type'])) $this->throwError('未获取到期数频道');
        $where = [];
        $terms = [];
        if ($periods['data'][0]['periods_type'] == 2) {
            $terms[] = ['push_store_status' => [0, 2, 3]];
        } else {
            $terms[] = ['push_wms_status' => [0, 2, 3]];
        }
        $where[]         = ['period' => $params['period']];
        $where[]         = ['sub_order_status' => 1];
        $arrData         = array(
            'index'  => ['orders'],
            'match'  => $where,
            'terms'  => $terms,
            'source' => ['sub_order_no', 'period', 'sub_order_status', 'is_ts', 'title', 'package_name', 'order_qty', 'payment_amount', 'consignee_encrypt', 'consignee', 'consignee_phone_encrypt', 'consignee_phone', 'created_time', 'warehouse_code', 'order_type'],
            'page'   => $params['page'],
            'limit'  => $params['limit'],
            'sort'   => [['created_time' => 'desc']]
        );
        $data            = $es->getDocumentList($arrData);
        $totalNum        = $data['total']['value'];
        $result['list']  = $data['data'];
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:换绑订单仓库
     * Author: zrc
     * Date: 2022/8/10
     * Time: 17:29
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function changeOrderWarehouse($params)
    {
        $sub_order_no_arr = explode(',', $params['sub_order_no']);
        $order_type       = config('config')['order_type'];//订单频道获取
        $ordersInfo       = Db::name($order_type[intval($params['order_type'])]['table'])->field('sub_order_no,warehouse_code')->where([['sub_order_no', 'in', $sub_order_no_arr]])->order('created_time asc')->select()->toArray();
        $msg_arr          = $sub_order_nos = [];
        foreach ($ordersInfo as &$v) {
            $msg_arr[$v['sub_order_no']] = $v['warehouse_code'];
            $sub_order_nos[] = $v['sub_order_no'];
        }
        // 订单换绑时，需要根据选择的订单，按下单时间正序换绑并进行推单
        $sub_order_no_arr = $sub_order_nos;

        $updateData = array(
            'warehouse_code' => $params['warehouse_code'],
            'operator'       => $params['operator'],
            'update_time'    => time(),
        );
        //跨境处理
        if ($params['order_type'] == 2) {
            if ($params['warehouse_code'] == '021') $updateData['store_type'] = 1;
            if ($params['warehouse_code'] == '028') $updateData['store_type'] = 2;
        }
        Db::startTrans();
        try {
            $order_list = Db::name($order_type[intval($params['order_type'])]['table'])->where([['sub_order_no', 'in', $sub_order_no_arr]])->column('id,sub_order_no,push_wms_status','sub_order_no');
            $updateOrder = Db::name($order_type[intval($params['order_type'])]['table'])->where([['sub_order_no', 'in', $sub_order_no_arr]])->update($updateData);
            if (empty($updateOrder)) $this->throwError('修改订单发货仓失败');
            foreach ($sub_order_no_arr as &$val) {
                $warehouse_code = isset($msg_arr[$val]) ? $msg_arr[$val] : '';
                $msg            = '订单发货仓调整：' . $warehouse_code . '-->' . $params['warehouse_code'];
                //添加订单备注
                $remarks      = array(
                    'sub_order_no' => $val,
                    'order_type'   => $params['order_type'],
                    'content'      => $msg,
                    'admin_id'     => isset($params['operator']) ? $params['operator'] : 0
                );
                $orderService = new OrderService();
                $orderService->createRemarks($remarks);
            }
            unset($val);
            Db::commit();
            
            foreach ($sub_order_no_arr as $v) {
                if (($order_list[$v]['push_wms_status'] ?? null) == 2 && $params['order_type'] != 2) { //推送失败重推WMS
                    $pushData = [
                        'exchange_name' => 'orders',
                        'routing_key'   => 'one.push.wms',
                        'data'          => base64_encode(json_encode([
                            'sub_order_no' => $v,
                            'order_type'   => $params['order_type']
                        ])),
                    ];
                    Log::write("换绑套餐重推WMS队列 1: " . json_encode($pushData));
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                }
            }
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:工单系统获取订单信息
     * Author: zrc
     * Date: 2022/8/15
     * Time: 10:07
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function workGetOrderInfo($params)
    {
        $order_type = config('config')['order_type'];//订单频道获取
        $field      = 'so.*,om.payment_amount as main_payment_amount,om.payment_method,om.main_order_no,om.consignee,om.payment_subject,om.payment_time';
        $ordersInfo = Db::name($order_type[intval($params['order_type'])]['table'])
            ->alias('so')
            ->field($field)
            ->leftJoin('order_main om', 'om.id=so.main_order_id')
            ->where(['sub_order_no' => $params['sub_order_no']])
            ->find();
        if (empty($ordersInfo)) $this->throwError('未获取到订单信息');
        return $ordersInfo;
    }

    /**
     * Description:工单获取拍卖订单信息
     * Author: zrc
     * Date: 2023/3/24
     * Time: 13:59
     * @param $params
     * @return array|mixed|Db|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function workGetOrderInfoAuction($params)
    {
        $ordersInfo = Db::table('vh_auction.vh_orders')->where(['order_no' => $params['sub_order_no']])->find();
        if (empty($ordersInfo)) $this->throwError('未获取到订单信息');
        $ordersInfo['main_payment_amount'] = $ordersInfo['payment_amount'];
        $ordersInfo['main_order_no']       = $ordersInfo['order_no'];
        $ordersInfo['sub_order_no']        = $ordersInfo['order_no'];
        $ordersInfo['sub_order_status']    = $ordersInfo['order_status'];
        $ordersInfo['package_id']          = 0;
        return $ordersInfo;
    }

    /**
     * Description:期数下架未付尾款的订金订单批量退款
     * Author: zrc
     * Date: 2023/6/25
     * Time: 17:07
     * @param $params
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function depositRefund($params)
    {
        $dataStr    = [];
        $order_type = config('config')['order_type'];//订单频道获取
        //获取待退款订金订单
        $where     = array(
            'period'           => $params['period'],
            'special_type'     => 4,
            'sub_order_status' => 1,
        );
        $field     = 'uid,sub_order_no,order_type';
        $orderInfo = Db::name($order_type[intval($params['period_type'])]['table'])->field($field)->where($where)->select()->toArray();
        foreach ($orderInfo as &$val) {
            $dataStr[] = base64_encode(json_encode(['sub_order_no' => $val['sub_order_no'], 'order_type' => $val['order_type']]));
        }
        //推送处理队列
        if (empty($dataStr)) $this->throwError('未获取到需要退款的订金订单');
        $pushData = array(
            'exchange_name' => 'orders',
            'routing_key'   => 'deposit_order_refund',
            'data'          => $dataStr,
        );
        $result   = curlRequest(env('ITEM.QUEUE_URL'), json_encode($pushData), [], 'POST');
        if (!isset($result['error_code'])) $this->throwError('批量退款处理失败：队列服务请求异常');
        if ($result['error_code'] != 0) $this->throwError('批量退款处理失败：' . $result['error_msg']);
        return true;
    }

    /**
     * Description:订金订单批量退款队列回调处理
     * Author: zrc
     * Date: 2023/6/25
     * Time: 18:12
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function depositRefundDeal($params)
    {
        $params     = json_decode($params, true);
        $order_type = config('config')['order_type'];//订单频道获取
        $where      = array(
            'sub_order_no'     => $params['sub_order_no'],
            'special_type'     => 4,
            'order_type'       => $params['order_type'],
            'sub_order_status' => 1
        );
        $field      = 'uid,sub_order_status,refund_status,payment_amount';
        $orderInfo  = Db::name($order_type[intval($params['order_type'])]['table'])->field($field)->where($where)->find();
        try {
            //查询优惠券发放记录ID判断尾款订单是否支付
            $couponIssueId = Db::name('deposit_inflation_record')->where(['uid' => $orderInfo['uid'], 'sub_order_no' => $params['sub_order_no']])->value('coupon_issue_id');
            if ($couponIssueId) {
                $balancePaymentWhere = array(
                    ['coupon_id', '=', $couponIssueId],
                    ['uid', '=', $orderInfo['uid']],
                    ['sub_order_status', 'in', [1, 2, 3]],
                );
                $balancePaymentOrder = Db::name($order_type[intval($params['order_type'])]['table'])->where($balancePaymentWhere)->count();
                if ($balancePaymentOrder > 0) {
                    Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no'], 'uid' => $orderInfo['uid']])->update(['sub_order_status' => 3, 'update_time' => time()]);
                    $this->throwError('尾款订单已支付');
                }
            }
            //订单退款
            $afterSalesService = new AfterSalesService();
            $afterSalesService->orderAutomaticRefund(['sub_order_no' => $params['sub_order_no'], 'order_type' => $params['order_type']]);
            Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no'], 'uid' => $orderInfo['uid']])->update(['refund_money' => $orderInfo['payment_amount'], 'update_time' => time()]);
        } catch (\Exception $e) {
            //异常推送企微群
            $queueData = array(
                'access_token' => '007b8136c66ac4a86349ee2dd9362a1781000ae8a494e3936e6f62016981a597',
                'type'         => 'text',
                'at'           => '***********',
                'content'      => base64_encode($params['sub_order_no'] . '订金订单退款处理失败，错误信息：' . $e->getMessage()),
            );
            $data      = base64_encode(json_encode($queueData));
            $pushData  = array(
                'exchange_name' => 'dingtalk',
                'routing_key'   => 'dingtalk_sender',
                'data'          => $data,
            );
            httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
        }
        return true;
    }

    /**
     * Description:获取订单开票信息
     * Author: zrc
     * Date: 2022/12/27
     * Time: 11:51
     * @param $params
     * @return array
     */
    public function getInvoiceInfo($params)
    {
        //商品es数据查询
        $goodsIds     = array_unique(array_column($params['items_info'], 'period'));
        $goodsWhere[] = ['terms' => ['id' => array_values($goodsIds)]];
        $goodsData    = esGetList('vinehoo.periods', $goodsWhere, [], 0, 1000);
        $goodsArr     = [];
        if (isset($goodsData['hits']['hits'])) {
            foreach ($goodsData['hits']['hits'] as &$gv) {
                $goodsArr[$gv['_source']['id']] = $gv['_source'];
            }
        }
        $result = [
            [
                'invoice_company' => '重庆云酒佰酿电子商务有限公司',
                'total_money'     => 0,
                'goods_info'      => [],
            ],
            [
                'invoice_company' => '佰酿云酒（重庆）科技有限公司',
                'total_money'     => 0,
                'goods_info'      => [],
            ],
            [
                'invoice_company' => '渝中区微醺酒业商行',
                'total_money'     => 0,
                'goods_info'      => [],
            ]
        ];
        foreach ($params['items_info'] as &$val) {
            if (isset($goodsArr[intval($val['period'])]['payee_merchant_id'])) {
                if ($goodsArr[intval($val['period'])]['payee_merchant_id'] == 1) {
                    $result[0]['total_money']  = $result[0]['total_money'] + $val['goods_money'];
                    $result[0]['goods_info'][] = array(
                        'title' => $goodsArr[intval($val['period'])]['title'],
                        'price' => $val['goods_money']
                    );
                } else if ($goodsArr[intval($val['period'])]['payee_merchant_id'] == 5) {
                    $result[2]['total_money']  = $result[2]['total_money'] + $val['goods_money'];
                    $result[2]['goods_info'][] = array(
                        'title' => $goodsArr[intval($val['period'])]['title'],
                        'price' => $val['goods_money']
                    );
                } else {
                    $result[1]['total_money']  = $result[1]['total_money'] + $val['goods_money'];
                    $result[1]['goods_info'][] = array(
                        'title' => $goodsArr[intval($val['period'])]['title'],
                        'price' => $val['goods_money']
                    );
                }
            } else {
                $result[1]['total_money']  = $result[1]['total_money'] + $val['goods_money'];
                $result[1]['goods_info'][] = array(
                    'title' => $goodsArr[intval($val['period'])]['title'],
                    'price' => $val['goods_money']
                );
            }
        }
        if (count($result[0]['goods_info']) == 0) unset($result[0]);
        if (count($result[1]['goods_info']) == 0) unset($result[1]);
        if (count($result[2]['goods_info']) == 0) unset($result[2]);
        $result = array_values($result);
        return $result;
    }

    /**
     * Description:导出前天11:00点到今天11:00点代发订单
     * Author: zrc
     * Date: 2023/10/10
     * Time: 14:10
     * @param $params
     */
    public function supplierDeliveryOrderExport($params)
    {

        Log::write("supplierDeliveryOrderExport 1: " . json_encode($params));

        // 更新代发期数发货时间
        updateDfDeliveryTime();
        
        $file = app()->getRootPath() . "/public/exportExcels";
        $public_path = app()->getRootPath().'public';
        deleteAllFilesInDirectory($file);
        $es    = new ElasticSearchService();
        $stime = date('Y-m-d 11:00:00', strtotime('-1 day'));
        $etime = date('Y-m-d 11:00:00');
        $match = [['sub_order_status' => 1], ['refund_status' => 0]];

        if (!empty($params['t']) && $params['t'] == 1) {
            $match[] = ['period' => 131772];
            $stime = date('Y-m-d 15:30:00', strtotime('-1 day'));
            $etime = date('Y-m-d 15:30:00');
        }

        $supplier_id = []; //2469 =》上海刹那光文化传媒有限公司 657=》天生汇
        $supplier_periods = array_column(Es::name(Es::PERIODS)->where([['supplier_id','in',$supplier_id]])->field('id')->select()->toArray(),'id');
        $must_not  = [['terms' => ['period' => $supplier_periods]]];
        // 获取代发仓编码
        $warehouse_code = GetDfWarehouseCode();
        $arr   = array(
            'index'  => ['orders'],
            'source' => ['sub_order_no', 'remarks', 'period', 'order_qty', 'created_time', 'package_name', 'title', 'sub_order_status', 'consignee', 'consignee_phone', 'address', 'province_name', 'city_name', 'district_name', 'order_type'],
            'match'  => $match,
            'must_not'  => $must_not,
            'terms'  => [['warehouse_code' => $warehouse_code]],
            'range'  => [['created_time' => ['gte' => $stime]], ['created_time' => ['lt' => $etime]]],
            'limit'  => 10000,
        );
        $data  = $es->getDocumentList($arr);
        if (count($data['data']) == 0) $this->throwError('昨天11点到今天11点暂无可导出的代发订单');
        $orderInfo = $data['data'];

        $remark_arr = Db::name('order_remarks')->where('id', 'in',
            Db::name('order_remarks')
                ->where('sub_order_no', 'in', array_column($orderInfo, 'sub_order_no'))
                ->where('admin_id', '>', 0)
                ->group('sub_order_no')
                ->column('MAX(id) as id')
        )->column('remarks', 'sub_order_no');

        $periodArr = array_intersect_key($orderInfo, array_unique(array_column($orderInfo, 'period')));
        $groupData = [];
        foreach ($periodArr as &$val) {
            if (empty($params['t']) && $val['period'] == 131772) {
                continue;
            }
            $table = 'vh_periods_flash';
            if ($val['order_type'] == 1) $table = 'vh_periods_second';
            $supplier = Db::table('vh_commodities.' . $table)->field('id,supplier,supplier_id,short_code')->where([['id', '=', $val['period']]])->find();
            foreach ($orderInfo as &$va) {
                if ($va['period'] == $val['period']) {
                    $groupData[intval($val['period'])][] = array(
                        'supplier'         => $supplier['supplier'],
                        'period_id'        => $va['period'],
                        'sub_order_no'     => $va['sub_order_no'],
                        'remarks'          => $remark_arr[$va['sub_order_no']] ?? $va['remarks'],
                        'period'           => $va['period'] . '（' . $supplier['short_code'] . '）',
                        'order_qty'        => $va['order_qty'],
                        'created_time'     => $va['created_time'],
                        'package_name'     => $va['package_name'],
                        'title'            => $va['title'],
                        'sub_order_status' => $va['sub_order_status'],
                        'consignee'        => $va['consignee'],
                        'consignee_phone'  => $va['consignee_phone'],
                        'province' => $va['province_name'],
                        'city'     => $va['city_name'],
                        'district' => $va['district_name'],
                        'address'          => $va['province_name'] . $va['city_name'] . $va['district_name'] . $va['address'],
                    );
                }
            }
        }
        $backData = [];
        foreach ($groupData as &$vv) {
            //用户信息解密
            $consigneeArr       = array_unique(array_column($vv, 'consignee'));
            $consignee_phoneArr = array_unique(array_column($vv, 'consignee_phone'));
            $encrypt            = cryptionDeal(2, array_merge($consigneeArr, $consignee_phoneArr), '15736175219', '宗仁川');
            foreach ($vv as $k => $v) {
                $vv[$k]['key']              = $k + 1;
                $vv[$k]['express_number']   = '';
                $vv[$k]['express_type']     = '';
                $vv[$k]['sub_order_status'] = '已支付';
                $vv[$k]['consignee']        = isset($encrypt[$v['consignee']]) ? $encrypt[$v['consignee']] : '';
                $vv[$k]['consignee_phone']  = isset($encrypt[$v['consignee_phone']]) ? $encrypt[$v['consignee_phone']] : '';
            }
            $filename  = $vv[0]['period_id'] . mb_substr($vv[0]['supplier'], 0, 4, 'utf-8') . date('Y-m-d', time()) . '代发货订单';
            $header    = array(
                array('column' => 'key', 'name' => '序号', 'width' => 30),
                array('column' => 'sub_order_no', 'name' => '原始订单号(子订单号)', 'width' => 30),
                array('column' => 'express_number', 'name' => '物流单号(多个单号#号分开)', 'width' => 30, 'back_color' => 'Yellow'),
                array('column' => 'express_type', 'name' => '快递方式下拉选择（不填默认订单原快递方式）', 'width' => 30, 'back_color' => 'Yellow'),
                array('column' => 'remarks', 'name' => '备注', 'width' => 30),
                array('column' => 'period', 'name' => '期数及相关简码', 'width' => 30),
                array('column' => 'order_qty', 'name' => '购买数量', 'width' => 15),
                array('column' => 'created_time', 'name' => '订单创建时间', 'width' => 15),
                array('column' => 'package_name', 'name' => '套餐名称', 'width' => 15),
                array('column' => 'title', 'name' => '商品名称', 'width' => 15),
                array('column' => 'sub_order_status', 'name' => '订单状态', 'width' => 15),
                array('column' => 'consignee', 'name' => '收货人名称', 'width' => 15),
                array('column' => 'consignee_phone', 'name' => '收货人手机号', 'width' => 15),
                array('column' => 'province', 'name' => '省', 'width' => 15),
                array('column' => 'city', 'name' => '市', 'width' => 15),
                array('column' => 'district', 'name' => '区', 'width' => 15),
                array('column' => 'address', 'name' => '收货地址', 'width' => 40),
            );
            $uploadUrl = exportSheelExcels2($vv, $header, $filename);
            if (empty($uploadUrl)) $this->throwError('生成excel文件异常');
            $localFile  = "/" . $uploadUrl;
            $wx_file = \Curl::upTempFileToWechat($public_path.$localFile);
            $backData[] = array(
                'supplier' => $vv[0]['supplier'],
                'wx_media_id' => $wx_file['media_id'],
                'media_id' => $localFile,
            );
        }
        foreach ($orderInfo as &$vav) {
            $isset = Db::table('vh_data_statistics.vh_out_deliversuborders_count')->where(['sub_order_no' => $vav['sub_order_no']])->count();
            (new OrderService)->createRemarks([
                'sub_order_no' => $vav['sub_order_no'],
                'order_type'   => $vav['order_type'],
                'content'      => '导出订单时间：' . date('Y年m月d日 H:i:s'),
                'admin_id'     => 0
            ]);
            if ($isset == 0) {
                Db::table('vh_data_statistics.vh_out_deliversuborders_count')->insert(['sub_order_no' => $vav['sub_order_no'], 'count' => 1, 'created_time' => time()]);
            } else {
                Db::table('vh_data_statistics.vh_out_deliversuborders_count')->where(['sub_order_no' => $vav['sub_order_no']])->inc('count')->update();
            }
        }
        /*$where               = array(
            ['to.created_time', '>=', strtotime($stime)],
            ['to.created_time', '<', strtotime($etime)],
            ['to.order_from_thirdparty', '=', 27],
            ['to.is_supplier_delivery', '=', 1],
            ['to.sub_order_status', '=', 1],
            ['to.refund_status', '=', 0],
        );
        $tripartiteOrderInfo = Db::name('tripartite_order')
            ->alias('to')
            ->field('to.sub_order_no, to.remarks,to.order_qty,to.created_time, to.title, to.sub_order_status,to.items_info,to.store_name,to.province,to.city,to.district,to.address,om.consignee,om.consignee_phone')
            ->leftJoin('order_main om', 'om.id=to.main_order_id')
            ->where($where)
            ->select()->toArray();
        if (!empty($tripartiteOrderInfo)) {
            $consigneeArr       = array_unique(array_column($tripartiteOrderInfo, 'consignee'));
            $consignee_phoneArr = array_unique(array_column($tripartiteOrderInfo, 'consignee_phone'));
            $encrypt            = cryptionDeal(2, array_merge($consigneeArr, $consignee_phoneArr), '15736175219', '宗仁川');
            foreach ($tripartiteOrderInfo as $key => $value) {
                $tripartiteOrderInfo[$key]['key']              = $key + 1;
                $tripartiteOrderInfo[$key]['express_number']   = '';
                $tripartiteOrderInfo[$key]['express_type']     = '';
                $tripartiteOrderInfo[$key]['sub_order_status'] = '已支付';
                $tripartiteOrderInfo[$key]['consignee']        = isset($encrypt[$value['consignee']]) ? $encrypt[$value['consignee']] : '';
                $tripartiteOrderInfo[$key]['consignee_phone']  = isset($encrypt[$value['consignee_phone']]) ? $encrypt[$value['consignee_phone']] : '';
                $goodsInfo                           = explode(',', $value['items_info']);
                $tripartiteOrderInfo[$key]['period'] = '';
                foreach ($goodsInfo as $k => $v) {
                    $goods = explode('*', $v);
                    if (count($goodsInfo) == $k + 1) {
                        $tripartiteOrderInfo[$key]['period'] .= $goods[0] . '*' . $goods[1];
                    } else {
                        $tripartiteOrderInfo[$key]['period'] .= $goods[0] . '*' . $goods[1] . ',';
                    }

                }
                $tripartiteOrderInfo[$key]['province'] = $value['province'];
                $tripartiteOrderInfo[$key]['city'] = $value['city'];
                $tripartiteOrderInfo[$key]['district'] = $value['district'];
                $tripartiteOrderInfo[$key]['address'] = $value['province'] . $value['city'] . $value['district'] . $value['address'];
            }
            $filename  = '快团团-' . date('Y-m-d', time()) . '代发货订单导出数据';
            $header    = array(
                array('column' => 'key', 'name' => '序号', 'width' => 30),
                array('column' => 'sub_order_no', 'name' => '原始订单号(子订单号)', 'width' => 30),
                array('column' => 'express_number', 'name' => '物流单号(多个单号#号分开)', 'width' => 30, 'back_color' => 'Yellow'),
                array('column' => 'express_type', 'name' => '快递方式下拉选择（不填默认订单原快递方式）', 'width' => 30, 'back_color' => 'Yellow'),
                array('column' => 'remarks', 'name' => '备注', 'width' => 30),
                array('column' => 'period', 'name' => '相关简码', 'width' => 30),
                array('column' => 'order_qty', 'name' => '购买数量', 'width' => 15),
                array('column' => 'created_time', 'name' => '订单创建时间', 'width' => 15),
                array('column' => 'store_name', 'name' => '店铺名称', 'width' => 15),
                array('column' => 'title', 'name' => '商品名称', 'width' => 15),
                array('column' => 'sub_order_status', 'name' => '订单状态', 'width' => 15),
                array('column' => 'consignee', 'name' => '收货人名称', 'width' => 15),
                array('column' => 'consignee_phone', 'name' => '收货人手机号', 'width' => 15),
                array('column' => 'province', 'name' => '省', 'width' => 15),
                array('column' => 'city', 'name' => '市', 'width' => 15),
                array('column' => 'district', 'name' => '区', 'width' => 15),
                array('column' => 'address', 'name' => '收货地址', 'width' => 40),
            );
            $uploadUrl = exportSheelExcels2($tripartiteOrderInfo, $header, $filename);
            if (empty($uploadUrl)) $this->throwError('生成excel文件异常');
            $localFile  = "/storage/" . $uploadUrl;
            $backData[] = array(
                'supplier' => '快团团',
                'media_id' => $localFile,
            );
        }*/
        return $backData;
    }

    public function supplierDeliveryOrderExportTsh($params)
    {
        Log::write("supplierDeliveryOrderExportTsh 1: " . json_encode($params));


        $file = app()->getRootPath() . "/public/exportExcels";
//        deleteAllFilesInDirectory($file);
        $es = new ElasticSearchService();
        $w  = date('w');
        if (in_array($w, [2, 3, 4, 5])) {
            $stime = date('Y-m-d 09:40:00', strtotime('-1 day'));
            $etime = date('Y-m-d 09:40:00');
        } elseif (in_array($w, [1])) {
            $stime = date('Y-m-d 09:40:00', strtotime('-3 day'));
            $etime = date('Y-m-d 09:40:00');
        } elseif (in_array($w, [0, 6])) {
            //周末返回空
            $this->throwError('周末不导出订单代发');
        }

        $match = [['sub_order_status' => 1], ['refund_status' => 0]];
        $supplier_id = []; //2469 =》上海刹那光文化传媒有限公司 657=》天生汇
        $supplier_periods = array_column(Es::name(Es::PERIODS)->where([['supplier_id','in',$supplier_id]])->field('id')->select()->toArray(),'id');

        // 获取代发仓编码
        $warehouse_code = GetDfWarehouseCode();
        $arr   = array(
            'index'  => ['orders'],
            'source' => ['sub_order_no', 'remarks', 'period', 'order_qty', 'created_time', 'package_name', 'title', 'sub_order_status', 'consignee', 'consignee_phone', 'address', 'province_name', 'city_name', 'district_name', 'order_type'],
            'match'  => $match,
            'terms'  => [['warehouse_code' => $warehouse_code],['period' => $supplier_periods]],
            'range'  => [['created_time' => ['gte' => $stime]], ['created_time' => ['lt' => $etime]]],
            'limit'  => 10000,
        );

        $data  = $es->getDocumentList($arr);
        if (count($data['data']) == 0) $this->throwError('昨天到今天暂无可导出的代发订单');
        $orderInfo = $data['data'];
        $periodArr = array_intersect_key($orderInfo, array_unique(array_column($orderInfo, 'period')));
        $groupData = [];
        foreach ($periodArr as &$val) {
            if (empty($params['t']) && $val['period'] == 131772) {
                continue;
            }
            $table = 'vh_periods_flash';
            if ($val['order_type'] == 1) $table = 'vh_periods_second';
            $supplier = Db::table('vh_commodities.' . $table)->field('id,supplier,supplier_id,short_code')->where([['id', '=', $val['period']]])->find();
            foreach ($orderInfo as &$va) {
                if ($va['period'] == $val['period']) {
                    $groupData[intval($val['period'])][] = array(
                        'supplier'         => $supplier['supplier'],
                        'period_id'        => $va['period'],
                        'sub_order_no'     => $va['sub_order_no'],
                        'remarks'          => $remark_arr[$va['sub_order_no']] ?? $va['remarks'],
                        'period'           => $va['period'] . '（' . $supplier['short_code'] . '）',
                        'order_qty'        => $va['order_qty'],
                        'created_time'     => $va['created_time'],
                        'package_name'     => $va['package_name'],
                        'title'            => $va['title'],
                        'sub_order_status' => $va['sub_order_status'],
                        'consignee'        => $va['consignee'],
                        'consignee_phone'  => $va['consignee_phone'],
                        'province' => $va['province_name'],
                        'city'     => $va['city_name'],
                        'district' => $va['district_name'],
                        'address'          => $va['province_name'] . $va['city_name'] . $va['district_name'] . $va['address'],
                    );
                }
            }
        }
        $backData = [];
        foreach ($groupData as &$vv) {
            //用户信息解密
            $consigneeArr       = array_unique(array_column($vv, 'consignee'));
            $consignee_phoneArr = array_unique(array_column($vv, 'consignee_phone'));
            $encrypt            = cryptionDeal(2, array_merge($consigneeArr, $consignee_phoneArr), '15736175219', '宗仁川');
            foreach ($vv as $k => $v) {
                $vv[$k]['key']              = $k + 1;
                $vv[$k]['express_number']   = '';
                $vv[$k]['express_type']     = '';
                $vv[$k]['sub_order_status'] = '已支付';
                $vv[$k]['consignee']        = isset($encrypt[$v['consignee']]) ? $encrypt[$v['consignee']] : '';
                $vv[$k]['consignee_phone']  = isset($encrypt[$v['consignee_phone']]) ? $encrypt[$v['consignee_phone']] : '';
            }
            $filename  = $vv[0]['period_id'] . mb_substr($vv[0]['supplier'], 0, 4, 'utf-8') . date('Y-m-d', time()) . '代发货订单';
            $header    = array(
                array('column' => 'key', 'name' => '序号', 'width' => 30),
                array('column' => 'sub_order_no', 'name' => '原始订单号(子订单号)', 'width' => 30),
                array('column' => 'express_number', 'name' => '物流单号(多个单号#号分开)', 'width' => 30, 'back_color' => 'Yellow'),
                array('column' => 'express_type', 'name' => '快递方式下拉选择（不填默认订单原快递方式）', 'width' => 30, 'back_color' => 'Yellow'),
                array('column' => 'remarks', 'name' => '备注', 'width' => 30),
                array('column' => 'period', 'name' => '期数及相关简码', 'width' => 30),
                array('column' => 'order_qty', 'name' => '购买数量', 'width' => 15),
                array('column' => 'created_time', 'name' => '订单创建时间', 'width' => 15),
                array('column' => 'package_name', 'name' => '套餐名称', 'width' => 15),
                array('column' => 'title', 'name' => '商品名称', 'width' => 15),
                array('column' => 'sub_order_status', 'name' => '订单状态', 'width' => 15),
                array('column' => 'consignee', 'name' => '收货人名称', 'width' => 15),
                array('column' => 'consignee_phone', 'name' => '收货人手机号', 'width' => 15),
                array('column' => 'province', 'name' => '省', 'width' => 15),
                array('column' => 'city', 'name' => '市', 'width' => 15),
                array('column' => 'district', 'name' => '区', 'width' => 15),
                array('column' => 'address', 'name' => '收货地址', 'width' => 40),
            );
            $uploadUrl = exportSheelExcels2($vv, $header, $filename);
            if (empty($uploadUrl)) $this->throwError('生成excel文件异常');
            $localFile  = "/" . $uploadUrl;
            $backData[] = array(
                'supplier' => $vv[0]['supplier'],
                'media_id' => $localFile,
            );
        }
        foreach ($orderInfo as &$vav) {
            $isset = Db::table('vh_data_statistics.vh_out_deliversuborders_count')->where(['sub_order_no' => $vav['sub_order_no']])->count();
            (new OrderService)->createRemarks([
                'sub_order_no' => $vav['sub_order_no'],
                'order_type'   => $vav['order_type'],
                'content'      => '导出订单时间：' . date('Y年m月d日 H:i:s'),
                'admin_id'     => 0
            ]);
            if ($isset == 0) {
                Db::table('vh_data_statistics.vh_out_deliversuborders_count')->insert(['sub_order_no' => $vav['sub_order_no'], 'count' => 1, 'created_time' => time()]);
            } else {
                Db::table('vh_data_statistics.vh_out_deliversuborders_count')->where(['sub_order_no' => $vav['sub_order_no']])->inc('count')->update();
            }
        }
        $where               = array(
            ['to.created_time', '>=', strtotime($stime)],
            ['to.created_time', '<', strtotime($etime)],
            ['to.order_from_thirdparty', '=', 27],
            ['to.is_supplier_delivery', '=', 1],
            ['to.sub_order_status', '=', 1],
            ['to.refund_status', '=', 0],
        );
        $tripartiteOrderInfo = Db::name('tripartite_order')
            ->alias('to')
            ->field('to.sub_order_no, to.remarks,to.order_qty,to.created_time, to.title, to.sub_order_status,to.items_info,to.store_name,to.province,to.city,to.district,to.address,om.consignee,om.consignee_phone')
            ->leftJoin('order_main om', 'om.id=to.main_order_id')
            ->where($where)
            ->select()->toArray();
        if (!empty($tripartiteOrderInfo)) {
            $consigneeArr       = array_unique(array_column($tripartiteOrderInfo, 'consignee'));
            $consignee_phoneArr = array_unique(array_column($tripartiteOrderInfo, 'consignee_phone'));
            $encrypt            = cryptionDeal(2, array_merge($consigneeArr, $consignee_phoneArr), '15736175219', '宗仁川');
            foreach ($tripartiteOrderInfo as $key => $value) {
                $tripartiteOrderInfo[$key]['key']              = $key + 1;
                $tripartiteOrderInfo[$key]['express_number']   = '';
                $tripartiteOrderInfo[$key]['express_type']     = '';
                $tripartiteOrderInfo[$key]['sub_order_status'] = '已支付';
                $tripartiteOrderInfo[$key]['consignee']        = isset($encrypt[$value['consignee']]) ? $encrypt[$value['consignee']] : '';
                $tripartiteOrderInfo[$key]['consignee_phone']  = isset($encrypt[$value['consignee_phone']]) ? $encrypt[$value['consignee_phone']] : '';
                $goodsInfo                           = explode(',', $value['items_info']);
                $tripartiteOrderInfo[$key]['period'] = '';
                foreach ($goodsInfo as $k => $v) {
                    $goods = explode('*', $v);
                    if (count($goodsInfo) == $k + 1) {
                        $tripartiteOrderInfo[$key]['period'] .= $goods[0] . '*' . $goods[1];
                    } else {
                        $tripartiteOrderInfo[$key]['period'] .= $goods[0] . '*' . $goods[1] . ',';
                    }

                }
                $tripartiteOrderInfo[$key]['province'] = $value['province'];
                $tripartiteOrderInfo[$key]['city'] = $value['city'];
                $tripartiteOrderInfo[$key]['district'] = $value['district'];
                $tripartiteOrderInfo[$key]['address'] = $value['province'] . $value['city'] . $value['district'] . $value['address'];
            }
            $filename  = '快团团-' . date('Y-m-d', time()) . '代发货订单导出数据';
            $header    = array(
                array('column' => 'key', 'name' => '序号', 'width' => 30),
                array('column' => 'sub_order_no', 'name' => '原始订单号(子订单号)', 'width' => 30),
                array('column' => 'express_number', 'name' => '物流单号(多个单号#号分开)', 'width' => 30, 'back_color' => 'Yellow'),
                array('column' => 'express_type', 'name' => '快递方式下拉选择（不填默认订单原快递方式）', 'width' => 30, 'back_color' => 'Yellow'),
                array('column' => 'remarks', 'name' => '备注', 'width' => 30),
                array('column' => 'period', 'name' => '相关简码', 'width' => 30),
                array('column' => 'order_qty', 'name' => '购买数量', 'width' => 15),
                array('column' => 'created_time', 'name' => '订单创建时间', 'width' => 15),
                array('column' => 'store_name', 'name' => '店铺名称', 'width' => 15),
                array('column' => 'title', 'name' => '商品名称', 'width' => 15),
                array('column' => 'sub_order_status', 'name' => '订单状态', 'width' => 15),
                array('column' => 'consignee', 'name' => '收货人名称', 'width' => 15),
                array('column' => 'consignee_phone', 'name' => '收货人手机号', 'width' => 15),
                array('column' => 'province', 'name' => '省', 'width' => 15),
                array('column' => 'city', 'name' => '市', 'width' => 15),
                array('column' => 'district', 'name' => '区', 'width' => 15),
                array('column' => 'address', 'name' => '收货地址', 'width' => 40),
            );
            $uploadUrl = exportSheelExcels2($tripartiteOrderInfo, $header, $filename);
            if (empty($uploadUrl)) $this->throwError('生成excel文件异常');
            $localFile  = "/storage/" . $uploadUrl;
            $backData[] = array(
                'supplier' => '快团团',
                'media_id' => $localFile,
            );
        }
        return $backData;
    }
}