<?php
/**
 * 测试按月统计API接口
 */

// 测试不同的参数组合
$testCases = [
    // 基本测试
    ['company_code' => '032', 'year' => '2025'],
    ['company_code' => '032', 'month' => '2025-08'],
    ['company_code' => '032', 'year' => '2025', 'month' => '2025-08'],
    
    // 冲突测试
    ['company_code' => '032', 'year' => '2024', 'month' => '2025-08'],
    
    // 格式测试
    ['month' => '2025-8'],  // 单数字月份
    ['month' => '2025-12'], // 双数字月份
];

echo "测试按月统计API接口...\n\n";

foreach ($testCases as $index => $params) {
    echo "测试案例 " . ($index + 1) . ": " . http_build_query($params) . "\n";
    
    // 验证month参数格式
    if (isset($params['month'])) {
        if (preg_match('/^\d{4}-(0?[1-9]|1[0-2])$/', $params['month'])) {
            echo "✓ month格式验证通过\n";
        } else {
            echo "✗ month格式验证失败\n";
        }
    }
    
    echo "---\n";
}

echo "测试完成！\n";
