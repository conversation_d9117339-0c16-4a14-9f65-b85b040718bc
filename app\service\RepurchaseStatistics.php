<?php

namespace app\service;

use app\ElasticSearchConnection;
use app\BaseService;
use app\model\Repurchase;
use app\model\RepurchaseCollection;
use app\model\RepurchaseStatisticsPeriod;
use app\service\elasticsearch\ElasticSearchService;
use app\model\RepurchaseStatistics as RepurchaseStatisticsModel;
use think\exception\ValidateException;
use think\facade\Db;

class RepurchaseStatistics extends BaseService
{

    //列表
    public static function list($params)
    {
        //查询条件
        $where = function ($query) use ($params) {

            if (isset($params['short_code']) && strlen($params['short_code']) > 0) {
                $query->where('short_code', '=', $params['short_code']);
            }
            if (isset($params['bar_code']) && strlen($params['bar_code']) > 0) {
                $query->where('bar_code', '=', $params['bar_code']);
            }
            if (isset($params['product_name']) && strlen($params['product_name']) > 0) {
                $query->where('product_name', 'like', "%" . $params['product_name'] . "%");
            }
        };
        //分页
        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;
        $data      = RepurchaseStatisticsModel::where($where)->order('nums', 'desc')->limit($pagestrat, $limit)->select();
        $count     = RepurchaseStatisticsModel::where($where)->count();

        return ['list' => $data, 'total' => $count];
    }

    //用户
    public function users($params)
    {

        //查询条件
        $where = function ($query) use ($params) {

            if (isset($params['period']) && strlen($params['period']) > 0) {
                $query->where('period', '=', $params['period']);
            }
            if (isset($params['uid']) && strlen($params['uid']) > 0) {
                $query->where('uid', '=', $params['uid']);
            }
            if (isset($params['user_phone']) && strlen($params['user_phone']) > 0) {
                $query->where('user_phone', '=', $params['user_phone']);
            }

        };
        //分页
        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;
        $data      = Repurchase::where($where)->whereRaw("find_in_set('" . $params['short_codes'] . "',short_codes)")->limit($pagestrat, $limit)->select()->toArray();

        $count = Repurchase::where($where)->whereRaw("find_in_set('" . $params['short_codes'] . "',short_codes)")->count();

        $code = ['periodcount' => 0, 'uidcount' => 0];
        if ($data) {

            $user_id_str = implode(',', array_column($data, 'uid'));
            $userInfo    = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $user_id_str, 'field' => "uid,realname,nickname"]);

            if ($userInfo['error_code'] != 0) $this->throwError("用户调用失败");
            $userlist = $userInfo['data']['list'];
            $data     = array_map(function ($v) use ($userlist) {

                $keyuserlist = array_column($userlist, null, 'uid');
                $data        = array_merge($v, $keyuserlist[$v['uid']]);

                return $data;
            }, $data);
            $code     = $this->shortCodeSta(['short_code' => $params['short_codes']]);
        }
        return array_merge(['list' => $data, 'total' => $count], $code);
    }

    //需求
    public function needlist($params)
    {
        //查询条件
        $where = function ($query) use ($params) {
            if (isset($params['period']) && strlen($params['period']) > 0) {
                $query->where('period', '=', $params['period']);
            }
            if (isset($params['uid']) && strlen($params['uid']) > 0) {
                $query->where('uid', '=', $params['uid']);
            }
            if (isset($params['user_phone']) && strlen($params['user_phone']) > 0) {
                $query->where('user_phone', '=', $params['user_phone']);
            }
            if (isset($params['is_from']) && strlen($params['is_from']) > 0) {
                $query->where('is_from', '=', $params['is_from']);
            }

        };

        //分页
        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;

        if (isset($params['short_codes']) && strlen($params['short_codes']) > 0) {
//            $query->where('short_codes','like',"%".$params['short_codes']."%");
            $data  = Repurchase::where($where)->whereRaw("find_in_set('" . $params['short_codes'] . "',short_codes)")->limit($pagestrat, $limit)->select()->toArray();
            $count = Repurchase::where($where)->whereRaw("find_in_set('" . $params['short_codes'] . "',short_codes)")->count();
        } else {
            $data  = Repurchase::where($where)->limit($pagestrat, $limit)->select()->toArray();
            $count = Repurchase::where($where)->count();
        }

        if ($data) {

            $user_id_str = implode(',', array_column($data, 'uid'));
            $userInfo    = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $user_id_str, 'field' => "uid,realname,nickname"]);

            if ($userInfo['error_code'] != 0) $this->throwError("用户调用失败");
            $userlist = $userInfo['data']['list'];
            $data     = array_map(function ($v) use ($userlist) {

                $keyuserlist = array_column($userlist, null, 'uid');
                $data        = array_merge($v, $keyuserlist[$v['uid']]);

                return $data;
            }, $data);
        }

        return ['list' => $data, 'total' => $count];
    }

    //创建记录
    public function create($params)
    {
        //获取订单
        $this->checkVersionUpdate($params);

        $es   = new ElasticSearchService();
        $arr  = array(
            'source' => ["sub_order_no", "period", "uid", "consignee_phone_encrypt", "consignee_phone"],
            'index'  => ['orders'],
            'match'  => [['sub_order_no.keyword' => $params['sub_order_no']]],
            'page'   => 1,
            'limit'  => 10
        );
        $data = $es->getDocumentList($arr);

        if (!(isset($data['total']) && $data['total']['value'] >= 1)) $this->throwError("订单不存在");


        $order      = $data['data'][0];
        $period     = $order['period'];
        $uid        = $order['uid'];
        $user_phone = $order['consignee_phone'];

        //是否已点击
        $checkd = Repurchase::field('id')->where('sub_order_no', $params['sub_order_no'])->find();
//        if($checkd) $this->throwError('已记录复购需求');

        //获取期数   简码
        $periodarr  = array(
            'source' => ["product_id", "id","onsale_time","sold_out_time","onsale_status","is_channel"],
            'index'  => ['periods'],
            'match'  => [['id' => $period]],
            'page'   => 1,
            'limit'  => 10
        );
        $perioddata = $es->getDocumentList($periodarr);

        if (!(isset($perioddata['total']) && $perioddata['total']['value'] >= 1)) $this->throwError("期数不存在");
        $periodrow  = $perioddata['data'][0];
        $productids = $periodrow['product_id'];

        if($productids == '') $this->throwError("您购买的宝贝太久远，兔子君已无法找到");
        //获取产品
        $productidsarr = explode(',', $productids);
        $index         = array_search('', $productidsarr);
        if ($index != false) unset($productidsarr[$index]);

        $productarr = array(
            'source' => ["bar_code", "short_code", "id", "cn_product_name", "en_product_name"],
            'index'  => ['panshi.products'],
            'terms'  => [['id' => $productidsarr]],
            'page'   => 1,
            'limit'  => 100
        );

        $productdata = $es->getDocumentList($productarr);
        if (!(isset($productdata['total']) && $productdata['total']['value'] >= 1)) $this->throwError("产品不存在");
        $productrow  = $productdata['data'];
        $short_codes = array_column($productrow, 'short_code');


        //判断简码是否已存在
        $repurchase_statistics_short_code = RepurchaseStatisticsModel::whereIn('short_code', $short_codes)->column('short_code');

        $short_code_diff       = array_diff($short_codes, $repurchase_statistics_short_code);//差集简码
        $short_code_inttersect = array_intersect($short_codes, $repurchase_statistics_short_code);//交集简码
        if (!$checkd) {
            $time = time();
            //新增统计 根据产品简码
            $productrowdiff       = array_column($productrow, null, 'short_code');
            $short_code_diff_data = array_map(function ($v) use ($productrowdiff, $time) {
                $code = [
                    'short_code'   => $v,
                    'bar_code'     => $productrowdiff[$v]['bar_code'],
                    'product_name' => $productrowdiff[$v]['cn_product_name'],
                    'nums'         => 1,
                    'create_time'  => $time,
                    'update_time'  => $time
                ];
                return $code;
            }, $short_code_diff);
            RepurchaseStatisticsModel::insertAll($short_code_diff_data);
            //修改统计 根据产品简码
            RepurchaseStatisticsModel::whereIn('short_code', $short_code_inttersect)->inc('nums')->update(['update_time' => $time]);
            //新增统计 根据期数 start
            $periodStatistics = RepurchaseStatisticsPeriod::where('period',$period)->find();
            if($periodStatistics){
                $periodStatistics->inc('nums')->update(['update_time' => $time]);
            }else{
                $periodStatisticsData = [
                    'period'=>$period,
                    'short_code'   => implode(',',$short_codes),//简码
                    'bar_code'     => implode(',',array_column($productrow, 'bar_code')),//条码
                    'product_name' => implode(',',array_column($productrow, 'cn_product_name')),
                    'nums'         => 1,
                    'period_start_time'  => strtotime($periodrow['onsale_time']),//上架时间
                    'period_end_time'  => strtotime($periodrow['sold_out_time']),//下架时间
                    'period_uids'  => "",//下架时间
                    'create_time'  => $time,
                    'update_time'  => $time
                ];
                RepurchaseStatisticsPeriod::insert($periodStatisticsData);
            }
            //新增统计 根据期数 end

            //解密
            $truehpone = cryptionDeal(2, [$user_phone], $uid, '前端用户');
            if (!$truehpone) $this->throwError("解密失败");

            //写入需求表
            $RepurchaseData = [
                'sub_order_no' => $params['sub_order_no'],
                'period'       => $period,
                'uid'          => $uid,
                'user_phone'   => $truehpone[$user_phone],
                'product'      => $productrow,
                'short_codes'  => implode(',', $short_codes),
                'create_time'  => $time,
                'update_time'  => $time
            ];
            $Repurchaseid     = Repurchase::insertGetId($RepurchaseData);

            if (!$Repurchaseid) $this->throwError("写入需求表失败");
        }

        //返回已上架商品  onsale_status 2   id desc
        /*$reperiodarr  = array(
            'source' => ["product_id", "id"],
            'index'  => ['periods'],
            'match'  => [['onsale_status' => 2], ["is_channel" => 0]],
            'terms'  => [['short_code.keyword' => $short_codes]],
            'sort'   => [['id' => "desc"]],
            'page'   => 1,
            'limit'  => 1
        );
        $reperioddata = $es->getDocumentList($reperiodarr);*/
        if ($periodrow['onsale_status'] != 2 && $periodrow['is_channel'] == 0) {

            if (!$checkd){
                return ['rid'=>(int)$Repurchaseid];
            }else{
                return ['rid'=>(int)$checkd['id']];
//                $this->throwError("兔子君已收到您的复购需求，会尽快为您补货哟");
            }
        }
        return ["product_id"=>$periodrow['product_id'], "id"=>$periodrow['id']];
    }

    //获取订单 需求列表 按钮
    public static function getNeedsList($sub_order_no)
    {
        $result = Repurchase::whereIn('sub_order_no', $sub_order_no)->column('sub_order_no');
        return $result;
    }


    //上架触发，发送短信
    public function putOnShelves($params)
    {
        $where      = [];
        $Repurchase = new Repurchase();

        $period = $params['period'];
        $short_codes = explode(',', $params['short_codes']);
        foreach ($short_codes as $v) {
            array_push($where, "find_in_set('" . $v . "',short_codes)");
        }

        //查询符合条件的订单复购
        $data = $Repurchase
            ->where('push_status', 0)
            ->where('is_msg', 1)
            ->whereRaw(implode(" or ", $where))
            ->column('user_phone,uid', 'id');
        if (empty($data)) $this->throwError("尚未有用户点击过复购");


        //获取期数   简码
        $es   = new ElasticSearchService();
        $periodarr  = array(
            'source' => ["title", "id","brief","banner_img","is_hidden_price","price","quota_number","limit_number","purchased","vest_purchased","periods_type","sell_time"],
            'index'  => ['periods'],
            'match'  => [['id' => $period]],
            'page'   => 1,
            'limit'  => 10
        );
        $perioddata = $es->getDocumentList($periodarr);
        if (!(isset($perioddata['total']) && $perioddata['total']['value'] >= 1)) $this->throwError("期数不存在");
        $periodrow  = $perioddata['data'][0];
        $periodrow['data_from'] = 1;
        $periodrow['banner_img'] = env("ALIURL").$periodrow['banner_img'];

        $uidArr = array_values(array_unique(array_column($data,'uid')));
        //发送小程序推送
        httpPostString(env('ITEM.USER_CACHE_URL') . '/wechat/miniSubscribeMessage', json_encode(['template_id' => '9LfIhMYeiemMf-lmrpawQz3OYOvsG9as2HWTTMn1Fd8','uids' => $uidArr,'data' => [
            'thing1' => ['value' => $periodrow['title']],
            'amount3' => ['value' => $periodrow['price']],
            'time4' => ['value' => $periodrow['sell_time']],
        ]]));



        $recode = [];
        //发送短信
        foreach ($data as $k=>$v){
            if(in_array($v['uid'].$period,$recode)){
                continue;
            }else{
                $postdata = [
                    "is_push"=>1,
                    "uid"=>$v['uid'],
                    "title"=>$periodrow['title'],
                    "content"=>$periodrow['brief'],
                    "data_type"=>1,
                    "data"=>$periodrow,
                    "label"=>"GoodsDetail",
                    "custom_param"=>["id"=>$period]
                ];
                $result   = $this->httpPost(env('ITEM.APPPUSH_URL') . '/apppush/v3/push/single', $postdata);
                if($result['error_code'] != 0) continue;
                array_push($recode,$v['uid'].$period);
            }
            Db::name("repurchase")->where('id', $k)->update([
                'push_status' => 1,
                'push_no'     => $params['rtid'],
            ]); //修改复购记录
        }

        Db::name("repurchase_task")->where('id', $params['rtid'])->update([
            'status' => 1
        ]); //修改上架数据

        return true;
    }

    public static function getproduct($short_codes)
    {

        $es          = new ElasticSearchService();
        $productarr  = array(
            'source' => ["cn_product_name", "en_product_name"],
            'index'  => ['panshi.products'],
            'terms'  => [['short_code' => $short_codes]],
            'page'   => 1,
            'limit'  => 1
        );
        $productdata = $es->getDocumentList($productarr);
        if (!isset($productdata['total']) && $productdata['total']['value'] >= 1) return false;

        $name = implode(',', array_column($productdata['data'], 'cn_product_name'));
        return $name;

    }


    //简码统计
    public function shortCodeSta($params)
    {
        $perioddata = Repurchase::whereRaw("find_in_set('" . $params['short_code'] . "',short_codes)")
            ->group("period")->count();
        $uiddata    = Repurchase::whereRaw("find_in_set('" . $params['short_code'] . "',short_codes)")
            ->group("uid")->count();

        return ['periodcount' => $perioddata, 'uidcount' => $uiddata];
    }

    //期数上下架同步
    public function periodTask($params)
    {
        if ($params['is_delete']) return '期数已被删除';
        if ($params['is_channel']) return '渠道商品';
        if (!in_array($params['onsale_status'], [1, 2, 3])) return '待上架或已售罄不做处理'; //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）

        //判断是否存在数据
        $rt_obj = Db::name('repurchase_task')
            ->where('period', $params['period'])
            ->where('status', 0)
            ->where('onsale_status', 'in', [1, 2])
            ->find();

        $time = time();

        if ($rt_obj) {
            //修改
            Db::name('repurchase_task')->where('id', $rt_obj['id'])->update([
                'period'        => $params['period'],
                'short_code'    => $params['short_code'],
                'period_name'   => $params['title'],
                'sale_time'     => $params['sell_time'],
                'onsale_status' => $params['onsale_status'],
                'update_time'   => $time,
            ]);
        } else {
            //新增
            Db::name('repurchase_task')->insert([
                'period'        => $params['period'],
                'short_code'    => $params['short_code'],
                'period_name'   => $params['title'],
                'sale_time'     => $params['sell_time'],
                'onsale_status' => $params['onsale_status'],
                'remarks'       => '',
                'create_time'   => $time,
                'update_time'   => $time,
                'status'        => 0,
            ]);
        }
        return true;
    }

    //发送复购消息
    public static function sendRepurchChasemsg()
    {
        //查询上架商品表 onsale_status 上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）
        //is_channel 渠道销售（0：否，1：是） sell_time 开售时间

        //9:00-22:00直接就直接发送，其他时间就加上12个小时后发送


        //时间参数
        $time  = time();
        $stime = strtotime(date("Y-m-d", $time) . " 09:00:00");
        $etime = strtotime(date("Y-m-d", $time) . " 22:00:00");

        //发送短信时间
        if (($stime <= $time) && ($time <= $etime)) {
            //region 获取数据
            $where  = [
                ['status', "=", 0],
                ['onsale_status', "in", [1, 2]]
            ];
            $result = Db::name("repurchase_task")->where($where)->select();
            if ($result->isEmpty()) return "暂无符合条件的数据";
            //endregion 获取数据

//            var_dump($result);
            //region 处理数据
            foreach ($result as $v) {
                //开售时间
                $sale_time = &$v['sale_time'];
                if (($stime <= $sale_time) && ($sale_time <= $etime)) {

                    (new self())->checkdata($v['id'], $v['period_name'], $v['short_code'], $v['period']); //发送短信
                } else {
                    //预计时间
                    $estimatetime = $sale_time + (12 * 3600);
                    if ($time > $estimatetime) {
                        (new self())->checkdata($v['id'], $v['period_name'], $v['short_code'], $v['period']); //发送短信
                    }
                }
            }
            //endregion 处理数据
            return true;
        }

        return "非短信发送时间";
    }


    public function checkdata($id, $period_name, $short_code, $period)
    {

        $params = ['short_codes' => $short_code, 'period_name' => $period_name, 'rtid' => $id, 'period' => $period];
        try {
            $this->putOnShelves($params);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
        return true;
    }


    //期数查询
    public function period($params)
    {
        //查询条件
        $where = function ($query) use ($params) {

            if (isset($params['short_code']) && strlen($params['short_code']) > 0) {
                $query->where('short_code', '=', $params['short_code']);
            }
            if (isset($params['period']) && strlen($params['period']) > 0) {
                $query->where('period', '=', $params['period']);
            }
            if (isset($params['product_name']) && strlen($params['product_name']) > 0) {
                $query->where('product_name', 'like', "%" . $params['product_name'] . "%");
            }
            if (isset($params['start_time']) && strlen($params['start_time']) > 0) {
                $query->where('period_start_time', '>=', strtotime($params['start_time']));
            }

            if (isset($params['end_time']) && strlen($params['end_time']) > 0) {
                $query->where('period_start_time', '<=', strtotime($params['end_time']));
            }
            //点击人数
            if (isset($params['start_nums']) && strlen($params['start_nums']) > 0) {
                $query->where('nums', '>=', strtotime($params['start_nums']));
            }
            if (isset($params['end_nums']) && strlen($params['end_nums']) > 0) {
                $query->where('nums', '<=', strtotime($params['end_nums']));
            }

            //购买人数
            if (isset($params['start_buy_people_nums']) && strlen($params['start_buy_people_nums']) > 0) {
                $query->where('buy_people_nums', '>=', strtotime($params['start_buy_people_nums']));
            }
            if (isset($params['end_buy_people_nums']) && strlen($params['end_buy_people_nums']) > 0) {
                $query->where('buy_people_nums', '<=', strtotime($params['end_buy_people_nums']));
            }
        };
        //分页
        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;
        $order = [];
        if (isset($params['propsort']) && strlen($params['propsort']) > 0) {
            $order['proportion'] = $params['propsort']==0?'asc':'desc';
        }


        if (isset($params['numssort']) && strlen($params['numssort']) > 0) {
            $order['nums'] = $params['numssort']==0?'asc':'desc';
        }
        if (isset($params['buypeoplesort']) && strlen($params['buypeoplesort']) > 0) {
            $order['buy_people_nums'] = $params['buypeoplesort']==0?'asc':'desc';
        }
        $data      = RepurchaseStatisticsPeriod::where($where)->order($order)->limit($pagestrat, $limit)->select()->toArray();
        $count     = RepurchaseStatisticsPeriod::where($where)->count();

        return ['list' => $data, 'total' => $count];
    }
    //修改统计接收消息状态
    public function saverepurchmsg($params)
    {
        $where = ['id'=>$params['id']];$data = ['is_msg'=>$params['is_msg']];
        $result = Repurchase::where($where)->find();
        if(!$result) throw new ValidateException('为查询到该数据请检查');
        $result = $result->save($data);
        if($result) return[];
        throw new ValidateException('修改数据失败!');
    }

    //修改统计接收消息状态
    public function remove($params)
    {

        $result = Repurchase::whereIn('id',$params['id'])->select();
        if(!$result) throw new ValidateException('未查询到该数据请检查');
        $result = $result->delete();
        if($result) return[];
        throw new ValidateException('修改数据失败!');
    }

    public function wishlist($params)
    {
        //查询条件
        $where = function ($query) use ($params) {

            if (isset($params['uid']) && strlen($params['uid']) > 0) {
                $query->where('uid', '=', $params['uid']);
            }
            $query->where('is_msg', '=', 1);
        };
        //分页
        $field = 'id,period,create_time';
        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;
        $data      = Repurchase::where($where)->field($field)->order('create_time', 'desc')->select()->toArray();
        $count     = Repurchase::where($where)->group("period")->count();
        if(!$data) return ['list' => $data, 'total' => $count];

        $reperiodarr  = array(
            'source' => ["id","title", "onsale_time", "brief","banner_img","price","market_price","is_hidden_price","purchased","vest_purchased","limit_number","quota_rule","banner_img_str","periods_type","marketing_attribute","onsale_status"],
            'index'  => ['periods'],
            'terms'  => [['id' => array_column($data,'period')],['is_channel' => [0]]],
            'sort'   => [['onsale_status'=>'asc'],['onsale_time' => "desc"]],
            'page'   => $page,
            'limit'  => $limit
        );
        $es   = new ElasticSearchService();
        $reperioddata = $es->getDocumentList($reperiodarr);
        if($reperioddata['total']['value'] <=0 )return ['list' => $data, 'total' => $count];
        $reperioddatakey = array_column($reperioddata['data'],null,'id');

        $resultdata = [];$qsdata = array_column($data,null,'period');
        foreach ($reperioddatakey as $v){

            $v['banner_img_str'] = $v['banner_img'];
            $v['banner_img'] = explode(',',$v['banner_img']);
            $item = [
                'id'=>$qsdata[$v['id']]['id'],
                'create_time'=>$qsdata[$v['id']]['create_time'],
                'periods_type'=>$v['periods_type'],
                'period_id'=>$v['id'],
                'period_info'=>$v,
            ];
            array_push($resultdata,$item);
        }
        
        return ['list' => $resultdata, 'total' => $count];
    }

    /**
     *
     * @param $params
     * @return int[]
     * @throws \Exception
     */
    public function wantCreate($params)
    {
        //验证版本号
        $this->checkVersionUpdate($params);
        //是否已点击
        $where = ['period'=>$params['period'],'uid'=>$params['uid'],'is_from'=>2];
        $checkd = Repurchase::field('id')->where($where)->value('id');
        if($checkd) return ['rid'=>$checkd];

        $es   = new ElasticSearchService();
        $period_inster = $params['period']; $period = [$params['period']];$uid= $params['uid'];

        //解密 手机号码
        $user_method = "/user/v3/profile/getUserInfo";
        $user = $this->httpGet(env('ITEM.USER_URL').$user_method,['uid'=>$uid,'field'=>'uid,telephone,telephone_encrypt,plaintext_telephone']);
        if(!isset($user['error_code']) ||$user['error_code']!=0)$this->throwError("获取用户失败");
        $telephone = $user['data']['list'][0]['plaintext_telephone']?? $this->throwError("获取用户失败");

        //期数查询 获取期数   简码
        $periodarr  = array(
            'source' => ["product_id", "id","onsale_time","sold_out_time","onsale_status","is_channel"],
            'index'  => ['periods'],
            'terms'  => [['id' => $period]],
            'page'   => 1,
            'limit'  => 10
        );
        $perioddata = $es->getDocumentList($periodarr);
        if (!(isset($perioddata['total']) && $perioddata['total']['value'] >= 1)) $this->throwError("期数不存在");
        $periodrow  = $perioddata['data'][0];$productids = $periodrow['product_id'];$productidsarr = explode(',', $productids);
        if (intval($perioddata['data'][0]['is_channel']) === 1) {
            $this->throwError("专属定制，限时拥有！");
        }

        $termsproduct = [];
        foreach ($productidsarr as $v){
            if($v != "") array_push($termsproduct,$v);
        }
        //产品查询
        $productarr = array(
            'source' => ["bar_code", "short_code", "id", "cn_product_name", "en_product_name"],
            'index'  => ['panshi.products'],
            'terms'  => [['id' => $termsproduct]],
            'page'   => 1,
            'limit'  => count($termsproduct)
        );
        $productdata = $es->getDocumentList($productarr);
        if (!(isset($productdata['total']) && $productdata['total']['value'] >= 1)) $this->throwError("产品不存在");
        $productrow  = $productdata['data'];
        $short_codes = array_column($productrow, 'short_code');

        $time = time();
        //写入需求表
        $RepurchaseData = [
            'sub_order_no' => "",//
            'period'       => $period_inster,
            'uid'          => $uid,
            'user_phone'   => $telephone,
            'product'      => $productrow,
            'is_from'      => 2,//1 再来一单 2 商品详情
            'short_codes'  => implode(',', $short_codes),
            'create_time'  => $time,
            'update_time'  => $time
        ];
        $Repurchaseid     = Repurchase::insertGetId($RepurchaseData);

        return ['rid'=>$Repurchaseid];
    }

    /**
     * 收藏统计列表
     * @return array|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function collectionList($params)
    {
        //查询条件
        $where = [];


        if (isset($params['period']) && strlen($params['period']) > 0) {
            $where[]=['period_id',"=",$params['period']];
        }

        $where[]=['period_id',">=",100000];
        //收藏数据
        //分页
        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;
        $tabledata = Db::table("vh_commodities.vh_periods_user_collection")->field("id,periods_type,user_id,period_id,count(period_id) as collectionnums")
            ->where($where)->group('period_id')->limit($pagestrat, $limit)->order("collectionnums","desc")->select()->toArray();
//        $tablecount = Db::table("vh_commodities.vh_periods_user_collection")->where($where)->group('period_id')->count();
        $tablecount = 50;

        if(!$tabledata) return ['list'=>[],'total'=>0];
        $periodArr = array_column($tabledata,'period_id');
//        var_dump($periodArr);
        //期数数据
        $periodarr  = array(
            'source' => ["product_id", "id","onsale_time","sold_out_time","onsale_status","is_channel"],
            'index'  => ['periods'],
            'terms'  => [['id' => $periodArr]],
            'page'   => 1,
            'limit'  => count($periodArr)
        );

        $es   = new ElasticSearchService();
        $perioddata = $es->getDocumentList($periodarr);
        if($perioddata['total']['value']<=0) return ['list'=>[],'total'=>0];
        $perioddatavaule = $perioddata['data'];

//        var_dump($perioddatavaule);
        $productidsArr = implode(',',array_column($perioddata['data'],'product_id'));
        $productidsArr = explode(',',$productidsArr);
        $productids = [];
        foreach ($productidsArr as $v){
            if($v != "")array_push($productids,$v);
        }

        //产品查询
        $productwhere = array(
            'source' => ["bar_code", "short_code", "id", "cn_product_name", "en_product_name"],
            'index'  => ['panshi.products'],
            'terms'  => [['id' => $productids]],
            'page'   => 1,
            'limit'  => count($productids)
        );
        $productdata = $es->getDocumentList($productwhere);
        if($productdata['total']['value']<=0) return ['list'=>[],'total'=>0];
        $productdatavaule = $productdata['data'];
        //心愿清单查询
        $wantData = Repurchase::field("period,count(period) as wantnums")->where('is_from',2)
            ->whereIn('period',$periodArr)
            ->group('period')->select()->toArray();
//        echo Repurchase::getLastsql();
        //组合数据
        $retureData = array_map(function ($v) use($perioddatavaule,$productdatavaule,$wantData){
            $wantDataKey = array_column($wantData,'wantnums','period');

            $perioddataKey = array_column($perioddatavaule,'product_id','id');
            $productdataKey = array_column($productdatavaule,null,'id');

            $product_names = [];$product_shorts = [];

            foreach ($perioddataKey as $k =>$vp){
                if($v['period_id'] == $k){
                    foreach ($productdataKey as $kk =>$vv){
                        if($vp == $kk){
                            array_push($product_names,$vv['cn_product_name']);
                            array_push($product_shorts,$vv['short_code']);
                        }
                    }
                }


            }
            $iterm = [
                'id'=>$v['id'],
                'period'=>$v['period_id'],
                'short_code'=>implode(',',$product_shorts),
                'product_names'=>implode(',',$product_names),
                'collection_nums'=>$v['collectionnums'],
                'want_nums'=>$wantDataKey[$v['period_id']]??0,
            ];
            return $iterm;
        },$tabledata);

        return ['list'=>$retureData,'total'=>$tablecount];
    }

    public function userlist($param)
    {

        $page      = $params['page'] ?? 1;
        $limit     = $params['limit'] ?? 10;
        $pagestrat = ($page - 1) * $limit;
        $where = ['period_id'=>$param['period_id']];
        $tabledata = Db::table("vh_commodities.vh_periods_user_collection")->field("id,periods_type,user_id,period_id")->where($where)->limit($pagestrat, $limit)->select()->toArray();
//        echo Db::getLastSql();
        $tablecount = Db::table("vh_commodities.vh_periods_user_collection")->where($where)->count();
        if(!$tabledata) return ['list'=>[],'total'=>0];
        $uid = implode(",",array_column($tabledata,'user_id'));
        $user_method = "/user/v3/profile/getUserInfo";
        $user = $this->httpGet(env('ITEM.USER_URL').$user_method,['uid'=>$uid,'field'=>'uid,telephone,telephone_encrypt,plaintext_telephone,nickname']);

        if(!isset($user['error_code']) ||$user['error_code']!=0)$this->throwError("获取用户失败");
        $userlist = array_column($user['data']['list'],null,'uid');

        $resultData = array_map(function ($v)use ($userlist){

            $item = [
                'id'=>$v['id'],
                'uid'=>$v['user_id'],
                'nickname'=>$userlist[$v['user_id']]['nickname'],
                'plaintext_telephone'=>$userlist[$v['user_id']]['plaintext_telephone'],
            ];
            return $item;
        },$tabledata);

        return ['list'=>$resultData,'total'=>$tablecount];
    }

    /**
     * 统计期数数据
     * @return void
     */
    public static function statisticsperiods()
    {
        $start_time = time();
        RepurchaseStatisticsPeriod::chunk(10,function ($v){
            //获取单个期数，完善数据
            $data = $v->toArray();
            $periods = array_column($data,'period');
            //获取订单统计
            $statisData = self::getorderesdataByperiod($periods);
            $statisDataKey = array_column($statisData,null,'key');
            //再来一单点击人数
            $redata = Db::name('repurchase')->field('period,count(distinct uid) as cs')
                ->where('delete_time',0)
                ->where('is_from',1)->whereIn('period',$periods)->group('period')->select()->toArray();
//            echo Db::getLastSql();
            $redata_key = array_column($redata,'cs','period');
            $updateData = array_map(function ($value) use($statisDataKey,$redata_key){
                if(!isset($redata_key[$value['period']])){
                    $redata_key[$value['period']] = 0;
                }
                $item = [
                    "id"=> $value['id'],
                    "period"=> $value['period'],
                    "nums"=> $redata_key[$value['period']],
                    "proportion"=> number_format(($redata_key[$value['period']])/($statisDataKey[$value['period']]['doc_count']),4),
                    "buy_people_nums"=> $statisDataKey[$value['period']]['user_nums'],
                    "order_nums"=> $statisDataKey[$value['period']]['doc_count'],
                    "period_uids"=> $statisDataKey[$value['period']]['uids'],
                    "sale_nums"=> $statisDataKey[$value['period']]['product_nums'],
                ];
                return $item;
            },$data);
//            echo json_encode($updateData);
            $result = (new RepurchaseStatisticsPeriod())->saveAll($updateData);

        });
        echo time() - $start_time;
    }

    /**
     * @param $periods
     * @param $type
     * @return mixed [{"key":"76229","doc_count":2,"user_nums":1,"product_nums":2}]
     */
    public static function getorderesdataByperiod(array $periods)
    {
        //查询条件
        $where = [
            "index"=>['vinehoo.orders'],
            "body"=>[
                "_source"=> ["period","package_id","uid","order_qty"],
                "query"=>[
                    "terms"=>[
                        "period"=>$periods
                    ]
                ],
                "aggs"=>["ageAgg"=>["terms"=>["field"=>"period","size"=> 10]]],
                "size"=>10000
            ]
        ];
        $es     = new ElasticSearchConnection();
        $result = $es->connection()->search($where);
        //原数据
        $his = array_column($result['hits']['hits'],'_source');
        //统计数据 期数对应的订单数
        $ageagg = $result['aggregations']['ageAgg']['buckets'];
        //套餐id
        $period_package_id = array_column($his,'package_id');
//        echo json_encode($period_package_id);
        //套餐id去重
        $period_package_where = array_unique($period_package_id);
//        var_dump($his,$period_package_id);
        //获取套餐数量

        $package_key = self::getpackagenums($period_package_where);


        //内容分组 期数
        $period_data_key = [];
        foreach ($his as &$v){
            if(!isset($package_key[$v['package_id']])){
                $package_key[$v['package_id']] = 0;
            }
            $v['nums'] = $package_key[$v['package_id']]*$v['order_qty'];
            $period_data_key[$v['period']][] = $v;
        }


        //处理数据 统计数据
        foreach ($ageagg as &$v){

            if($period_data_key[$v['key']]){
                //获取去重用户数量
                $v['user_nums'] = count(array_unique(array_column($period_data_key[$v['key']],'uid')));
                $v['uids'] = implode(",",array_unique(array_column($period_data_key[$v['key']],'uid')));
                $v['product_nums'] = array_sum(array_column($period_data_key[$v['key']],'nums'));
            }
        }

        return $ageagg;
    }

    /**
     * 获取套餐数量
     * @param $package_id
     * @return [['pageage_id'=>2]] 套餐对应的酒瓶数
     */
    public static function getpackagenums($package_id)
    {
        $es   = new ElasticSearchService();
        $arr  = array(
            'source' => ["associated_products","id"],
            'index'  => ['periods_set'],
            'terms'  => [['id' => array_values($package_id)]],
            'page'   => 1,
            'limit'  => count($package_id)
        );
        $data = $es->getDocumentList($arr);
        $package = $data['data'];

        $result = array_map(function ($v) {
            $associated_products = json_decode($v['associated_products']);
            $nums = array_sum(array_column($associated_products,'nums'));
            return ['id'=>$v['id'],'nums'=>$nums];
        },$package);
        return array_column($result,'nums','id');
    }


    /**
     * 核对当前版本
     * @param $param
     * @return void
     */
    public function checkVersionUpdate($param)
    {
        $version = str_replace('.','',$param['vinehoo-client']);
        if (mb_strlen($version,"utf-8")==2) {
            $version = $version."0";
        }
        // 当前 安卓版本号 9.0.7 IOS版本号 9.13 --2023年1月17日10:09:30
        if (( $version== "android" && $param['vinehoo-client-version'] < 9.11) ||
            ( $version == "ios" && $param['vinehoo-client-version'] < 9.17)) {
            throw new ValidateException("功能升级，请更新到最新版本。");
        }
    }
}